// test-supabase-storage.js
const fetch = require('node-fetch');

// Configuration
const TEST_ENDPOINT = 'http://localhost:3000/api/test-db-simple';
const SUPABASE_URL = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://your-project.supabase.co';
const SUPABASE_KEY = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'your-anon-key';

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

// Helper function to print colored messages
function printColored(color, message) {
  console.log(`${color}${message}${colors.reset}`);
}

// Helper function to print <PERSON><PERSON><PERSON> in a readable format
function printJson(obj) {
  console.log(JSON.stringify(obj, null, 2));
}

// Main test function
async function testSupabaseStorage() {
  printColored(colors.cyan, '=== Testing Supabase Data Storage ===');
  printColored(colors.blue, `\nTesting endpoint: ${TEST_ENDPOINT}`);
  printColored(colors.blue, `Supabase URL: ${SUPABASE_URL}`);
  
  try {
    // Make a request to the test endpoint
    printColored(colors.yellow, '\nSending request to test endpoint...');
    
    const response = await fetch(TEST_ENDPOINT);
    const data = await response.json();
    
    // Check if the request was successful
    if (response.ok) {
      printColored(colors.green, '\n✅ Successfully connected to the test endpoint!');
      printColored(colors.green, `✅ Status: ${response.status} ${response.statusText}`);
      
      // Check if the response indicates successful Supabase connection
      if (data.success) {
        printColored(colors.green, '\n✅ Successfully connected to Supabase!');
        printColored(colors.green, `✅ Message: ${data.message}`);
        printColored(colors.green, `✅ Step: ${data.step}`);
        
        // Print the data that was stored or retrieved
        printColored(colors.magenta, '\nData from Supabase:');
        printJson(data.data);
        
        // If user data is available, print it
        if (data.user) {
          printColored(colors.magenta, '\nUser data:');
          printJson(data.user);
        }
        
        printColored(colors.green, '\n🎉 TEST PASSED: Data is being stored in Supabase! 🎉');
      } else {
        printColored(colors.red, '\n❌ Failed to connect to Supabase or store data.');
        printColored(colors.red, `❌ Message: ${data.message}`);
        printColored(colors.red, `❌ Step: ${data.step}`);
        
        if (data.error) {
          printColored(colors.red, '\nError details:');
          printJson(data.error);
        }
        
        if (data.suggestion) {
          printColored(colors.yellow, `\nSuggestion: ${data.suggestion}`);
        }
        
        printColored(colors.red, '\n❌ TEST FAILED: Data is not being stored in Supabase.');
      }
    } else {
      printColored(colors.red, `\n❌ Failed to connect to the test endpoint. Status: ${response.status} ${response.statusText}`);
      printColored(colors.red, '\nResponse data:');
      printJson(data);
      printColored(colors.red, '\n❌ TEST FAILED: Could not connect to the test endpoint.');
    }
  } catch (error) {
    printColored(colors.red, '\n❌ An error occurred while testing:');
    printColored(colors.red, error.message);
    
    if (error.code === 'ECONNREFUSED') {
      printColored(colors.yellow, '\nMake sure your Next.js server is running on http://localhost:3000');
    }
    
    printColored(colors.red, '\n❌ TEST FAILED: An error occurred during testing.');
  }
}

// Run the test
testSupabaseStorage();

/*
HOW TO USE THIS TEST FILE:

1. Make sure your Next.js application is running on http://localhost:3000
2. Run this file with Node.js: node test-supabase-storage.js
3. Check the console output to see if the test passed or failed

If you want to use your actual Supabase credentials, you can set them as environment variables:
- NEXT_PUBLIC_SUPABASE_URL
- NEXT_PUBLIC_SUPABASE_ANON_KEY

Or you can edit this file to include them directly (not recommended for production code).

EXPECTED OUTPUT (for successful test):

=== Testing Supabase Data Storage ===

Testing endpoint: http://localhost:3000/api/test-db-simple
Supabase URL: https://your-project.supabase.co

Sending request to test endpoint...

✅ Successfully connected to the test endpoint!
✅ Status: 200 OK

✅ Successfully connected to Supabase!
✅ Message: Successfully connected to Supabase and inserted test data
✅ Step: insert_data

Data from Supabase:
{
  "test_id": "test-2023-06-15T12:34:56.789Z",
  "created_at": "2023-06-15T12:34:56.789Z",
  "test_value": "This is a test entry"
}

🎉 TEST PASSED: Data is being stored in Supabase! 🎉
*/ 