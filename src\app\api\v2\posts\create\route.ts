/**
 * API v2 - Create Post Route
 * 
 * Version 2 of the create post API with enhanced response format and validation
 */

import { NextRequest } from 'next/server';
import { z } from 'zod';
import { postService } from '@/lib/services';
import {
  with<PERSON>rror<PERSON><PERSON><PERSON>,
  requireAuth
} from '@/lib/error-handler';
import {
  validateRequestBody,
  ValidationSchemas,
  NonEmptyStringSchema,
  PostStatusSchema,
  UUIDSchema,
  YouTubeSettingsSchema
} from '@/lib/validation';
import { withVersioning, withDeprecationWarning } from '@/lib/versioning/middleware';

/**
 * Enhanced V2 create post schema with additional validation
 */
const CreatePostSchemaV2 = z.object({
  // Base fields from CreatePostSchema
  title: NonEmptyStringSchema.max(200, 'Title cannot exceed 200 characters'),
  content: z.string().max(10000, 'Content cannot exceed 10000 characters').optional(),
  status: PostStatusSchema.default('draft'),
  scheduledDate: z.string().datetime().optional(),
  mediaIds: z.array(UUIDSchema).max(10, 'Cannot attach more than 10 media files').default([]),
  channels: z.array(z.string()).max(5, 'Cannot publish to more than 5 channels').default([]),
  tags: z.array(z.string().max(50, 'Tag cannot exceed 50 characters')).max(20, 'Cannot have more than 20 tags').default([]),
  youtubeSettings: YouTubeSettingsSchema.optional(),

  // V2 enhancements
  auto_publish: z.boolean().default(false),
  notification_settings: z.object({
    notify_followers: z.boolean().default(true),
    send_email: z.boolean().default(false),
    push_notification: z.boolean().default(false)
  }).optional(),
  seo_settings: z.object({
    meta_title: z.string().max(60).optional(),
    meta_description: z.string().max(160).optional(),
    keywords: z.array(z.string()).max(10).optional()
  }).optional()
}).strict().refine(
  (data) => {
    // If status is scheduled, scheduledDate should be provided
    if (data.status === 'scheduled' && !data.scheduledDate) {
      return false;
    }
    return true;
  },
  {
    message: 'scheduledDate is required when status is scheduled',
    path: ['scheduledDate']
  }
);

/**
 * Internal handler for post creation (V2 format)
 */
async function createPostHandlerV2(req: NextRequest) {
  // 1. Authenticate user
  const { user } = await requireAuth(req);

  // 2. Validate request body with V2 enhancements
  const validatedData = await validateRequestBody(req, CreatePostSchemaV2);

  // 3. Prepare post data for the service with V2 enhancements
  const createPostData = {
    user_id: user.id,
    title: validatedData.title,
    content: validatedData.content,
    status: validatedData.status,
    scheduled_at: validatedData.scheduledDate,
    youtube_settings: validatedData.youtubeSettings,
    mediaIds: validatedData.mediaIds,
    channels: validatedData.channels,
    tags: validatedData.tags,
    // V2 specific fields
    auto_publish: validatedData.auto_publish,
    notification_settings: validatedData.notification_settings,
    seo_settings: validatedData.seo_settings
  };

  // 4. Call service to create the post
  const result = await postService.createPost(createPostData);

  // 5. Handle service result
  if (!result.success) {
    throw result.error;
  }

  // Ensure data exists
  if (!result.data) {
    throw new Error('Post creation succeeded but no data returned');
  }

  // 6. Return response in V2 format (standardized with metadata)
  return {
    success: true,
    data: result.data,
    metadata: {
      timestamp: new Date().toISOString(),
      created: true,
      location: `/api/v2/posts/${result.data.id}`,
      version: 'v2',
      // V2 specific metadata
      processing: {
        auto_publish_scheduled: validatedData.auto_publish,
        notifications_queued: validatedData.notification_settings?.notify_followers || false,
        seo_optimized: !!validatedData.seo_settings
      },
      scheduling: validatedData.status === 'scheduled' && validatedData.scheduledDate ? {
        scheduled: true,
        scheduled_for: validatedData.scheduledDate,
        timezone: 'UTC', // This would be user's timezone
        estimated_publish_time: validatedData.scheduledDate
      } : undefined,
      validation: {
        schema_version: 'v2',
        enhanced_validation: true,
        fields_validated: Object.keys(validatedData).length
      }
    }
  };
}

/**
 * POST /api/v2/posts/create
 * Create a new post (V2 enhanced format)
 */
export const POST = withVersioning(
  withDeprecationWarning(
    withErrorHandler(createPostHandlerV2)
  )
);
