import { NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { google } from 'googleapis';

export async function GET(request: Request) {
  try {
    const url = new URL(request.url);
    const timeframe = url.searchParams.get('timeframe') || 'Weekly overview';
    
    // Initialize Supabase client
    const supabase = createClient();
    
    // Get the user session to ensure they're logged in
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    
    if (sessionError || !session) {
      return NextResponse.json(
        { error: 'Not authenticated', details: sessionError?.message },
        { status: 401 }
      );
    }
    
    // Get the YouTube token from google_oauth_tokens
    let tokenData;
    
    try {
      const { data: oauthToken, error: tokenError } = await supabase
        .from('google_oauth_tokens')
        .select('*')
        .eq('user_id', session.user.id)
        .eq('service_type', 'youtube')
        .single();
      
      if (oauthToken && oauthToken.access_token) {
        console.log('Found token in google_oauth_tokens');
        tokenData = oauthToken;
      } else {
        console.log('Token not found in google_oauth_tokens, error:', tokenError);
        
        // Try getting from connected_accounts as fallback
        console.log('Trying connected_accounts table...');
        const { data: connectedAccount, error: connectedError } = await supabase
          .from('connected_accounts')
          .select('*')
          .eq('user_id', session.user.id)
          .eq('provider', 'google_youtube')
          .single();
          
        if (connectedAccount && connectedAccount.access_token) {
          console.log('Found token in connected_accounts');
          tokenData = connectedAccount;
        } else {
          console.error('No tokens found in any table:', connectedError);
          return NextResponse.json(
            { error: 'YouTube token not found. Please connect your YouTube account.' }, 
            { status: 404 }
          );
        }
      }
    } catch (error) {
      console.error('Error retrieving tokens:', error);
      return NextResponse.json(
        { error: 'Error retrieving YouTube tokens. Please try again.' }, 
        { status: 500 }
      );
    }
    
    // Check if we have analytics scopes - with fallback to connected_accounts
    let scopes = tokenData.scope || '';
    let hasAnalyticsAccess = scopes.includes('yt-analytics.readonly');

    // If no analytics access found, check connected_accounts as fallback
    if (!hasAnalyticsAccess) {
      console.log('🔄 No analytics scope in google_oauth_tokens, checking connected_accounts...');
      const { data: connectedAccount } = await supabase
        .from('connected_accounts')
        .select('scope')
        .eq('user_id', userId)
        .eq('provider', 'google')
        .eq('service_type', 'youtube')
        .not('scope', 'is', null)
        .order('updated_at', { ascending: false })
        .limit(1);

      if (connectedAccount && connectedAccount.length > 0) {
        const fallbackScopes = connectedAccount[0].scope || '';
        hasAnalyticsAccess = fallbackScopes.includes('yt-analytics.readonly');
        console.log('✅ Fallback scope check result:', hasAnalyticsAccess ? 'HAS ANALYTICS' : 'NO ANALYTICS');

        // Update google_oauth_tokens with the correct scope for future requests
        if (hasAnalyticsAccess && !scopes.includes('yt-analytics.readonly')) {
          console.log('🔄 Updating google_oauth_tokens with analytics scope...');
          await supabase
            .from('google_oauth_tokens')
            .update({ scope: fallbackScopes, updated_at: new Date().toISOString() })
            .eq('user_id', userId)
            .eq('service_type', 'youtube');
          scopes = fallbackScopes;
        }
      }
    }

    if (!hasAnalyticsAccess) {
      console.log('YouTube token does not have analytics scope in either table');
      return NextResponse.json(
        { error: 'YouTube analytics access not granted. Please reconnect with analytics permissions.' },
        { status: 403 }
      );
    }
    
    // Create a YouTube API client
    const oauth2Client = new google.auth.OAuth2(
      process.env.GOOGLE_CLIENT_ID,
      process.env.GOOGLE_CLIENT_SECRET,
      process.env.GOOGLE_OAUTH_REDIRECT_URI
    );
    
    oauth2Client.setCredentials({
      access_token: tokenData.access_token,
      refresh_token: tokenData.refresh_token,
    });
    
    // Create YouTube Data API client to get channel info
    const youtube = google.youtube({
      version: 'v3',
      auth: oauth2Client,
    });
    
    // Get channel info first
    const channelResponse = await youtube.channels.list({
      part: ['snippet,statistics,contentDetails'],
      mine: true
    });
    
    if (!channelResponse?.data?.items || channelResponse.data.items.length === 0) {
      return NextResponse.json(
        { error: 'Channel not found' },
        { status: 404 }
      );
    }
    
    const channelId = channelResponse.data.items[0]?.id;
    const channelTitle = channelResponse.data.items[0]?.snippet?.title || 'Unknown Channel';
    const channelStatistics = channelResponse.data.items[0]?.statistics;
    const uploadsPlaylistId = channelResponse.data.items[0]?.contentDetails?.relatedPlaylists?.uploads;
    
    // Create YouTube Analytics API client
    const youtubeAnalytics = google.youtubeAnalytics({
      version: 'v2',
      auth: oauth2Client
    });
    
    // Calculate date range based on timeframe parameter
    const now = new Date();
    
    // Fix for potentially incorrect system date - ensure we use a valid date
    // YouTube API doesn't accept future dates, so we ensure the endDate is today or earlier
    const currentDate = new Date();
    const endDate = currentDate.toISOString().split('T')[0]; // Today's date as YYYY-MM-DD
    
    let startDate = new Date(currentDate);
    let dimensions = 'day';
    let prevStartDate = new Date(currentDate);
    
    switch (timeframe) {
      case 'Weekly overview':
        startDate.setDate(currentDate.getDate() - 7);
        prevStartDate.setDate(currentDate.getDate() - 14);
        dimensions = 'day';
        break;
      case 'Last 30 days':
        startDate.setDate(currentDate.getDate() - 30);
        prevStartDate.setDate(currentDate.getDate() - 60);
        dimensions = 'day';
        break;
      case 'Last 90 days':
        startDate.setDate(currentDate.getDate() - 90);
        prevStartDate.setDate(currentDate.getDate() - 180);
        dimensions = 'week';
        break;
      case 'Last 6 months':
        startDate.setMonth(currentDate.getMonth() - 6);
        prevStartDate.setMonth(currentDate.getMonth() - 12);
        dimensions = 'month';
        break;
      case 'Last one year':
        startDate.setFullYear(currentDate.getFullYear() - 1);
        prevStartDate.setFullYear(currentDate.getFullYear() - 2);
        dimensions = 'month';
        break;
      default:
        startDate.setDate(currentDate.getDate() - 30);
        prevStartDate.setDate(currentDate.getDate() - 60);
        dimensions = 'day';
    }
    
    const startDateString = startDate.toISOString().split('T')[0];
    const prevStartDateString = prevStartDate.toISOString().split('T')[0];
    const prevEndDateString = startDateString; // Previous period ends where current period starts
    
    console.log(`Fetching YouTube analytics for current period: ${startDateString} to ${endDate}`);
    console.log(`Fetching YouTube analytics for previous period: ${prevStartDateString} to ${prevEndDateString}`);
    
    // Fetch analytics data for current period
    let analyticsResponse;
    try {
      analyticsResponse = await youtubeAnalytics.reports.query({
        ids: `channel==${channelId}`,
        startDate: startDateString,
        endDate: endDate,
        metrics: 'views,comments,likes,subscribersGained,shares,estimatedMinutesWatched,averageViewDuration',
        dimensions: dimensions,
        sort: dimensions
      });
      console.log('Successfully fetched current period analytics data');
    } catch (error) {
      console.error('Error fetching YouTube analytics for current period:', error);
      
      // Provide a fallback response with empty data
      analyticsResponse = {
        data: {
          columnHeaders: [
            { name: dimensions }, 
            { name: 'views' }, 
            { name: 'comments' }, 
            { name: 'likes' }, 
            { name: 'subscribersGained' }, 
            { name: 'shares' }, 
            { name: 'estimatedMinutesWatched' }, 
            { name: 'averageViewDuration' }
          ],
          rows: []
        }
      };
      console.log('Using fallback empty data for current period');
    }
    
    // Fetch analytics data for previous period (for calculating growth)
    let prevAnalyticsResponse;
    try {
      prevAnalyticsResponse = await youtubeAnalytics.reports.query({
        ids: `channel==${channelId}`,
        startDate: prevStartDateString,
        endDate: prevEndDateString,
        metrics: 'views,comments,likes,subscribersGained,shares',
        dimensions: '',
      });
      console.log('Successfully fetched previous period analytics data');
    } catch (error) {
      console.error('Error fetching YouTube analytics for previous period:', error);
      
      // Provide a fallback response with empty data
      prevAnalyticsResponse = {
        data: {
          columnHeaders: [
            { name: 'views' }, 
            { name: 'comments' }, 
            { name: 'likes' }, 
            { name: 'subscribersGained' }, 
            { name: 'shares' }
          ],
          rows: [[0, 0, 0, 0, 0]]
        }
      };
      console.log('Using fallback empty data for previous period');
    }
    
    // Get subscriber data for followers growth
    let subscriberResponse;
    try {
      subscriberResponse = await youtubeAnalytics.reports.query({
        ids: `channel==${channelId}`,
        startDate: startDateString,
        endDate: endDate,
        metrics: 'subscribersGained,subscribersLost',
        dimensions: '',
      });
      console.log('Successfully fetched subscriber data');
    } catch (error) {
      console.error('Error fetching YouTube subscriber data:', error);
      
      // Provide a fallback response with empty data
      subscriberResponse = {
        data: {
          columnHeaders: [
            { name: 'subscribersGained' }, 
            { name: 'subscribersLost' }
          ],
          rows: [[0, 0]]
        }
      };
      console.log('Using fallback empty data for subscriber data');
    }
    
    // Get video upload dates to calculate posting frequency
    let videoCount = 0;
    let prevPeriodVideoCount = 0;
    
    if (uploadsPlaylistId) {
      try {
        // Get videos uploaded in the current period
        const uploadDateResponse = await youtube.playlistItems.list({
          part: ['snippet,contentDetails'],
          playlistId: uploadsPlaylistId,
          maxResults: 50
        });
        
        if (uploadDateResponse.data.items) {
          console.log(`Retrieved ${uploadDateResponse.data.items.length} videos from uploads playlist`);
          
          // Current period video count
          const currentPeriodVideos = uploadDateResponse.data.items.filter(item => {
            try {
              const publishedAt = new Date(item.snippet?.publishedAt || '');
              // Ensure date is valid
              if (isNaN(publishedAt.getTime())) {
                console.log(`Invalid publish date for video: ${item.snippet?.title}`);
                return false;
              }
              const isInCurrentPeriod = publishedAt >= startDate && publishedAt <= new Date(endDate);
              console.log(`Video ${item.snippet?.title} published at ${publishedAt.toISOString()} - in current period: ${isInCurrentPeriod}`);
              return isInCurrentPeriod;
            } catch (err) {
              console.error(`Error processing video date: ${err}`);
              return false;
            }
          });
          
          videoCount = currentPeriodVideos.length;
          console.log(`Found ${videoCount} videos in current period (${startDateString} to ${endDate})`);
          
          // Previous period video count
          const previousPeriodVideos = uploadDateResponse.data.items.filter(item => {
            try {
              const publishedAt = new Date(item.snippet?.publishedAt || '');
              // Ensure date is valid
              if (isNaN(publishedAt.getTime())) return false;
              return publishedAt >= prevStartDate && publishedAt < startDate;
            } catch {
              return false;
            }
          });
          
          prevPeriodVideoCount = previousPeriodVideos.length;
          console.log(`Found ${prevPeriodVideoCount} videos in previous period (${prevStartDateString} to ${prevEndDateString})`);
        }
      } catch (error) {
        console.error('Error fetching video upload dates:', error);
        // Continue with default values if there's an error
      }
    }
    
    // Process current period data
    const rows = analyticsResponse.data.rows || [];
    
    // Calculate totals for current period
    const totalViews = rows.reduce((sum, row) => sum + parseInt(row[1]), 0);
    const totalComments = rows.reduce((sum, row) => sum + parseInt(row[2]), 0);
    const totalLikes = rows.reduce((sum, row) => sum + parseInt(row[3]), 0);
    const totalSubscribersGained = rows.reduce((sum, row) => sum + parseInt(row[4]), 0);
    const totalShares = rows.reduce((sum, row) => sum + parseInt(row[5]), 0);
    
    // Get previous period totals
    const prevRows = prevAnalyticsResponse.data.rows || [];
    const prevTotalViews = prevRows.length > 0 ? parseInt(prevRows[0][0]) : 0;
    const prevTotalComments = prevRows.length > 0 ? parseInt(prevRows[0][1]) : 0;
    const prevTotalLikes = prevRows.length > 0 ? parseInt(prevRows[0][2]) : 0;
    const prevTotalSubscribersGained = prevRows.length > 0 ? parseInt(prevRows[0][3]) : 0;
    const prevTotalShares = prevRows.length > 0 ? parseInt(prevRows[0][4]) : 0;
    
    // Calculate total engagement (likes + comments + shares)
    const totalEngagements = totalLikes + totalComments + totalShares;
    const prevTotalEngagements = prevTotalLikes + prevTotalComments + prevTotalShares;
    
    // Calculate engagement rate
    const engagementRate = totalViews > 0 ? 
      ((totalLikes + totalComments + totalShares) / totalViews) * 100 : 0;
    
    // Create history data for chart
    const historyData = rows.map(row => {
      return {
        name: formatDateByDimension(row[0], dimensions), 
        score: calculateDailyScore(parseInt(row[1]), parseInt(row[2]), parseInt(row[3]), parseInt(row[4]), parseInt(row[5]))
      };
    });
    
    // Calculate percentage changes
    const engagementsChange = calculateChange(totalEngagements, prevTotalEngagements);
    const viewsChange = calculateChange(totalViews, prevTotalViews);
    const videoCountChange = calculateChange(videoCount, prevPeriodVideoCount);
    
    // Get subscriber data
    const subscriberRows = subscriberResponse.data.rows || [];
    const subscribersGained = subscriberRows.length > 0 ? parseInt(subscriberRows[0][0]) : 0;
    const subscribersLost = subscriberRows.length > 0 ? parseInt(subscriberRows[0][1]) : 0;
    const netSubscribers = subscribersGained - subscribersLost;
    
    // Calculate total score
    const totalScore = Math.round(calculateTotalScore(
      totalViews, 
      totalComments, 
      totalLikes, 
      totalSubscribersGained, 
      engagementRate, 
      parseInt(rows.reduce((sum, row) => sum + parseInt(row[6]), 0).toString()) // estimatedMinutesWatched
    ));
    
    // Calculate score change - compare first half of period to second half
    const midpoint = Math.floor(rows.length / 2);
    const firstHalfScore = rows.slice(0, midpoint).reduce((sum, row) => 
      sum + calculateDailyScore(parseInt(row[1]), parseInt(row[2]), parseInt(row[3]), parseInt(row[4]), parseInt(row[5])), 0);
    const secondHalfScore = rows.slice(midpoint).reduce((sum, row) => 
      sum + calculateDailyScore(parseInt(row[1]), parseInt(row[2]), parseInt(row[3]), parseInt(row[4]), parseInt(row[5])), 0);
    
    const scoreChange = firstHalfScore > 0 ? 
      Math.round(((secondHalfScore - firstHalfScore) / firstHalfScore) * 100) : 0;
    
    // Format response to match the social score format expected by the frontend
    const response = {
      score: totalScore,
      scoreChange: scoreChange,
      history: historyData,
      channel: {
        id: channelId,
        title: channelTitle
      },
      factors: {
        postViews: { 
          value: formatNumber(totalViews), 
          change: calculateChange(totalViews, prevTotalViews)
        },
        postClicks: { 
          value: formatNumber(totalLikes), 
          change: calculateChange(totalLikes, prevTotalLikes)
        },
        postInteractions: { 
          value: formatNumber(totalEngagements), 
          change: engagementsChange
        },
        postShares: { 
          value: formatNumber(totalShares), 
          change: calculateChange(totalShares, prevTotalShares)
        },
        followers: { 
          value: formatNumber(parseInt(channelStatistics?.subscriberCount || '0')), 
          change: calculateChange(totalSubscribersGained, prevTotalSubscribersGained)
        },
        postComments: { 
          value: formatNumber(totalComments), 
          change: calculateChange(totalComments, prevTotalComments)
        }
      },
      insights: {
        date: new Date().toLocaleDateString('en-US', { year: 'numeric', month: 'short', day: 'numeric' }),
        content: getInsightText(totalViews, engagementRate, totalSubscribersGained),
        metrics: {
          impressions: formatNumber(totalViews), // Using views as a proxy for impressions since impressions metric isn't available
          engagements: formatNumber(totalEngagements),
          reactions: formatNumber(totalLikes),
          comments: formatNumber(totalComments),
          shares: formatNumber(totalShares)
        }
      },
      performance: {
        engagements: {
          total: totalEngagements,
          change: engagementsChange,
          reactions: totalLikes,
          comments: totalComments,
          shares: totalShares
        },
        impressions: {
          total: totalViews, // Using views as a proxy for impressions
          change: viewsChange
        }
      },
      posting: {
        frequency: {
          total: videoCount,
          change: videoCountChange
        },
        followerGrowth: {
          total: netSubscribers,
          change: calculateChange(totalSubscribersGained, prevTotalSubscribersGained)
        }
      },
      radarData: [
        { subject: 'Engagement', A: Math.min(100, Math.round(engagementRate * 100)), fullMark: 100 },
        { subject: 'Consistency', A: Math.min(100, videoCount > 0 ? Math.round((videoCount / countDaysInPeriod(startDate, now)) * 100) : 0), fullMark: 100 },
        { subject: 'Growth', A: Math.min(100, calculatePercentile(netSubscribers, 0, 1000)), fullMark: 100 },
        { subject: 'Content', A: Math.min(100, calculatePercentile(totalViews, 0, 10000)), fullMark: 100 },
        { subject: 'Audience', A: Math.min(100, calculatePercentile(totalViews, 0, 20000)), fullMark: 100 }
      ]
    };
    
    return NextResponse.json(response);
    
  } catch (error: any) {
    console.error('Error in YouTube social-score endpoint:', error);
    
    // Handle token expiration or OAuth errors
    if (error.message?.includes('invalid_grant') || error.message?.includes('token expired')) {
      return NextResponse.json({ error: 'YouTube authorization expired. Please reconnect your account.' }, { status: 401 });
    }
    
    // Handle analytics access errors
    if (error.message?.includes('PERMISSION_DENIED') || error.message?.includes('analytics')) {
      return NextResponse.json({ error: 'Analytics access not available. Please reconnect your YouTube account with analytics permissions.' }, { status: 403 });
    }
    
    return NextResponse.json(
      { error: 'Failed to fetch YouTube social score data', details: error.message || 'Unknown error' }, 
      { status: 500 }
    );
  }
}

// Helper function to calculate daily score based on metrics
function calculateDailyScore(views: number, comments: number, likes: number, subscribers: number, shares: number): number {
  // Weighted score calculation
  const viewsWeight = 0.3;
  const commentsWeight = 0.2;
  const likesWeight = 0.2;
  const subscribersWeight = 0.2;
  const sharesWeight = 0.1;
  
  return Math.round(
    (views * viewsWeight) + 
    (comments * 10 * commentsWeight) + 
    (likes * 5 * likesWeight) + 
    (subscribers * 20 * subscribersWeight) + 
    (shares * 15 * sharesWeight)
  );
}

// Helper function to calculate total score
function calculateTotalScore(
  views: number, 
  comments: number, 
  likes: number, 
  subscribers: number, 
  engagementRate: number,
  minutesWatched: number
): number {
  // Base score from engagement
  const baseScore = (views / 100) + (comments * 10) + (likes * 5) + (subscribers * 20);
  
  // Adjust score based on engagement rate and watch time
  const engagementMultiplier = 1 + (engagementRate / 100);
  const watchTimeBonus = minutesWatched / 1000;
  
  return baseScore * engagementMultiplier + watchTimeBonus;
}

// Helper function to format numbers with k/M/B suffixes
function formatNumber(num: number): string {
  if (num === 0) return '0';
  if (num < 1000) return num.toString();
  
  const abbreviations = ['', 'k', 'M', 'B', 'T'];
  const tier = Math.floor(Math.log10(Math.abs(num)) / 3);
  
  const scaled = num / Math.pow(10, tier * 3);
  const formatted = scaled.toFixed(1);
  
  // Remove .0 suffix when the decimal is zero
  return (formatted.endsWith('.0') ? formatted.slice(0, -2) : formatted) + abbreviations[tier];
}

// Helper function to calculate percentage change
function calculateChange(current: number, previous: number): number {
  if (previous === 0) return current > 0 ? 100 : 0;
  return Math.round(((current - previous) / previous) * 100);
}

// Helper function to generate insight text based on analytics
function getInsightText(views: number, engagementRate: number, subscribers: number): string {
  if (views > 10000 && engagementRate > 5) {
    return 'Your audience is most active throughout the week, but try publishing when your audience is most active. Timing can significantly impact engagement rates.';
  } else if (subscribers > 100) {
    return 'Your posts that feature images or videos are crucial for engagement. Start incorporating high-quality visuals that reflect your brand\'s personality and enhance your content.';
  } else {
    return 'To build a stronger following, focus on posting consistently and engaging with comments. Regular interaction helps build a loyal community around your content.';
  }
}

// Helper function to count unique upload days
function countUniqueUploadDays(rows: any[]): number {
  const uniqueDates = new Set();
  rows.forEach(row => {
    if (row.dimensions && row.dimensions.length > 0) {
      uniqueDates.add(row.dimensions[0]);
    }
  });
  return uniqueDates.size;
}

// Helper function to count days in a period
function countDaysInPeriod(startDate: Date, endDate: Date): number {
  return Math.round((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)) + 1;
}

// Helper function to format date by dimension
function formatDateByDimension(dateStr: string, dimension: string): string {
  if (dimension === 'day') {
    // Format YYYY-MM-DD to day of week (e.g., "Mon")
    const date = new Date(dateStr);
    return date.toLocaleDateString('en-US', { weekday: 'short' });
  } else if (dimension === 'week') {
    // Format YYYY-MM-DD to "Week X"
    const date = new Date(dateStr);
    const weekNum = Math.ceil((date.getDate() + (date.getDay() + 1)) / 7);
    return `Week ${weekNum}`;
  } else if (dimension === 'month') {
    // Format YYYY-MM to "Month"
    const date = new Date(dateStr + '-01'); // Add day for parsing
    return date.toLocaleDateString('en-US', { month: 'short' });
  }
  return dateStr;
}

// Helper function to calculate percentile (0-100) based on value and range
function calculatePercentile(value: number, min: number, max: number): number {
  if (value <= min) return 0;
  if (value >= max) return 100;
  return Math.round(((value - min) / (max - min)) * 100);
} 