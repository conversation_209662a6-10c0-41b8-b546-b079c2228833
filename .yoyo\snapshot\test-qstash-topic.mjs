import { promises as fs } from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __dirname = path.dirname(fileURLToPath(import.meta.url));

async function main() {
  try {
    // Read and parse token from .env
    const envPath = path.resolve(__dirname, '.env');
    const envContent = await fs.readFile(envPath, 'utf-8');
    const tokenMatch = envContent.match(/QSTASH_TOKEN="(.+)"/);
    if (!tokenMatch) {
      console.error("Failed to find QSTASH_TOKEN in .env file");
      return;
    }
    const qstashToken = tokenMatch[1];
    
    // Step 1: Create a new QStash topic
    const topicName = `test-topic-${Date.now()}`;
    console.log(`Creating QStash topic: ${topicName}`);
    
    const createTopicResponse = await fetch('https://qstash.upstash.io/v2/topics', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${qstashToken}`
      },
      body: JSON.stringify({ name: topicName })
    });
    
    console.log(`Create topic response status: ${createTopicResponse.status} ${createTopicResponse.statusText}`);
    const createTopicBody = await createTopicResponse.text();
    console.log(`Create topic response body: ${createTopicBody}`);
    
    if (createTopicResponse.status !== 201 && createTopicResponse.status !== 200) {
      console.log("❌ Failed to create topic. Stopping test.");
      return;
    }
    
    // Parse the response to get the topic ID if successful
    let topicId;
    try {
      const topicData = JSON.parse(createTopicBody);
      topicId = topicData.topicId || topicData.id;
      console.log(`Topic created with ID: ${topicId}`);
    } catch (e) {
      console.log("❌ Failed to parse topic creation response.");
      return;
    }
    
    // Step 2: Add a URL to the topic
    const destinationUrl = "https://httpbin.org/post";
    console.log(`\nAdding URL to topic: ${destinationUrl}`);
    
    const addUrlResponse = await fetch(`https://qstash.upstash.io/v2/topics/${topicId}/endpoints`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${qstashToken}`
      },
      body: JSON.stringify({ url: destinationUrl })
    });
    
    console.log(`Add URL response status: ${addUrlResponse.status} ${addUrlResponse.statusText}`);
    const addUrlBody = await addUrlResponse.text();
    console.log(`Add URL response body: ${addUrlBody}`);
    
    if (addUrlResponse.status !== 201 && addUrlResponse.status !== 200) {
      console.log("❌ Failed to add URL to topic. Stopping test.");
      return;
    }
    
    // Step 3: Publish a message to the topic
    console.log(`\nPublishing message to topic: ${topicId}`);
    
    const publishResponse = await fetch(`https://qstash.upstash.io/v2/publish/topic/${topicId}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'text/plain',
        'Authorization': `Bearer ${qstashToken}`
      },
      body: "Hello World!"
    });
    
    console.log(`Publish response status: ${publishResponse.status} ${publishResponse.statusText}`);
    const publishBody = await publishResponse.text();
    console.log(`Publish response body: ${publishBody}`);
    
    if (publishResponse.status === 200 || publishResponse.status === 201) {
      console.log("\n✅ Success! Message published to QStash topic.");
      console.log("This confirms that your QStash account can publish messages.");
      console.log("\nRecommendations:");
      console.log("1. Use the topic-based approach instead of direct URL publishing");
      console.log("2. Configure your endpoints through the QStash console");
      console.log("3. Update your application to publish to topics rather than directly to URLs");
    } else {
      console.log("\n❌ Failed to publish message to topic.");
    }
  } catch (error) {
    console.error('Error:', error);
  }
}

main(); 