# Dependencies
/node_modules
node_modules/
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# Next.js build output
/.next/
/out/
/build

# Local Environment Variables
# Never commit secrets!
.env.local
.env.development.local
.env.test.local
.env.production.local
.env*

# Logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
logs
*.log
dev-debug.log

# Testing
/coverage

# Editor Directories and Files
.vscode*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
.idea
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# OS specific
.DS_Store
*.pem

# Vercel
.vercel

# TypeScript
*.tsbuildinfo
next-env.d.ts

# Task files
tasks.json
tasks/

# YoYo AI version control directory
.yoyo/
