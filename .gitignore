# Dependencies
/node_modules
node_modules/
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# Next.js build output
/.next/
/out/
/build

# Local Environment Variables
# Never commit secrets!
.env.local
.env.development.local
.env.test.local
.env.production.local
.env*

# Logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
logs
*.log
dev-debug.log

# Testing
/coverage

# Editor Directories and Files
.vscode*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
.idea
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# OS specific
.DS_Store
*.pem

# Vercel
.vercel

# TypeScript
*.tsbuildinfo
next-env.d.ts

# Task files
tasks.json
tasks/

# YoYo AI version control directory
.yoyo/

# MCP (Model Context Protocol) development tools
supabase-mcp/
supabase-mcp-server/
mcp-knowledge-graph/

# Development tools and temporary folders
BMAD-METHOD/
saasy-land/
ngrok/
backups/
memory-bank/
copycoder-prompts/
screenshot/
scripts/

# AI assistant configuration files
.cursor/
.roo/
.taskmaster/
.windsurfrules
.cursorrules
.roomodes

# Documentation and analysis files
*_IMPLEMENTATION.md
*_INTEGRATION*.md
*_SUMMARY.md
*_ANALYSIS*.md
*_DOCUMENTATION.md
*_TROUBLESHOOTING.md
*_COMPLETE.md
*_STATUS.md
*_SETUP.md
SPRINT_*.md
MCP_*.md
GOOGLE_*.md
FACEBOOK_*.md
AUTO_COMMENT_*.md
DATABASE_*.md
ERROR_*.md
SERVICE_*.md
SAAS_*.md
YOUTUBE_*.md
ZOD_*.md
API_*.md
ANALYSIS_REPORT.md
IMPLEMENTATION-SUMMARY.md
QUEUE-SETUP.md
UPSTASH-SETUP.md
PRD_ANALYSIS_STATUS.md

# Test files and scripts in root
test-*.js
test-*.mjs
auth-*.js
api-endpoint-test.js
auth-callback.js
auth-implementation.js
auth-test-simple.js
auth-test.js
list-tables.js
simple-browser-tools-server.js
start-browser-tools*.js
token-test.js
*.bat
*.ps1
temp.txt
server.log
types.log

# SQL files in root (keep only essential ones)
add-file-id-column.sql
add_id_token_column.sql
connected-accounts-schema.sql
create-test-table.sql
create-youtube-tokens-table.sql
fix-database*.sql
google-oauth-tokens-table.sql
supabase-schema.sql

# Markdown files in root (keep README.md)
facebook.md
privacypolicy.md
terms&conditions.md
qstash-fix-recommendations.md

# Other development files
browser-tools-setup.html
youtube_response.json
ngrok.zip
fix-browser-tools.ps1
setup-mcp.ps1
setup-upstash.bat

# Build artifacts
tsconfig.tsbuildinfo
next-env.d.ts

# Sprints folder (project management)
sprints/

# Database folder
database/

# Documentation folder (keep essential docs only)
docs/

# Screenshots
screenshot/
