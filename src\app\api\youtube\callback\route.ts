import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { google } from 'googleapis';

export async function GET(request: Request) {
  const url = new URL(request.url);
  const code = url.searchParams.get('code');
  const error = url.searchParams.get('error');
  const returnTo = url.searchParams.get('returnTo') || '/connect-youtube';

  if (error) {
    console.error('Error from Google OAuth:', error);
    return NextResponse.redirect(new URL(`/?error=google_auth_error`, request.url));
  }

  if (!code) {
    return NextResponse.redirect(new URL(`/?error=missing_params`, request.url));
  }

  try {
    // Initialize Supabase client
    const supabase = createRouteHandlerClient({ cookies });

    // Get the session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();

    if (sessionError) {
      throw new Error('Error getting session: ' + sessionError.message);
    }

    if (!session) {
      return NextResponse.redirect(new URL(`/?error=not_authenticated`, request.url));
    }

    // Set up OAuth2 client
    const oauth2Client = new google.auth.OAuth2(
      process.env.GOOGLE_CLIENT_ID,
      process.env.GOOGLE_CLIENT_SECRET,
      `${process.env.NEXT_PUBLIC_APP_URL}/api/youtube/callback`
    );

    // Get tokens from code
    const { tokens } = await oauth2Client.getToken(code);
    console.log('Received tokens from Google');

    // Get user info
    oauth2Client.setCredentials(tokens);
    const oauth2 = google.oauth2('v2');
    const userInfo = await oauth2.userinfo.get({ auth: oauth2Client });
    
    console.log('YouTube user info:', {
      email: userInfo.data.email,
      name: userInfo.data.name,
      picture: userInfo.data.picture ? 'Has picture' : 'No picture',
      verified_email: userInfo.data.verified_email,
      googleUserId: userInfo.data.id
    });

    // Store tokens in database - both connected_accounts and google_oauth_tokens for API compatibility
    const scopeString = 'https://www.googleapis.com/auth/youtube.readonly https://www.googleapis.com/auth/youtube.upload https://www.googleapis.com/auth/youtube.force-ssl https://www.googleapis.com/auth/youtube https://www.googleapis.com/auth/yt-analytics.readonly https://www.googleapis.com/auth/yt-analytics-monetary.readonly';

    // Store in connected_accounts table
    const { error: upsertError } = await supabase
      .from('connected_accounts')
      .upsert({
        user_id: session.user.id,
        provider: 'youtube',
        access_token: tokens.access_token,
        refresh_token: tokens.refresh_token,
        expires_at: tokens.expiry_date,
        provider_user_id: userInfo.data.id,
        provider_user_email: userInfo.data.email,
        provider_user_name: userInfo.data.name,
        scope: scopeString,
        updated_at: new Date().toISOString()
      });

    if (upsertError) {
      throw new Error('Error storing tokens in connected_accounts: ' + upsertError.message);
    }

    // Also store in google_oauth_tokens table for API compatibility
    const { error: oauthTokenError } = await supabase
      .from('google_oauth_tokens')
      .upsert({
        user_id: session.user.id,
        service_type: 'youtube',
        email: userInfo.data.email,
        access_token: tokens.access_token,
        refresh_token: tokens.refresh_token,
        expires_at: tokens.expiry_date ? Math.floor(tokens.expiry_date / 1000) : null,
        scope: scopeString,
        metadata: {
          provider: 'youtube',
          google_user_id: userInfo.data.id,
          channel_id: userInfo.data.id,
          channel_title: userInfo.data.name
        },
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      });

    if (oauthTokenError) {
      console.error('Error storing tokens in google_oauth_tokens:', oauthTokenError);
      // Don't throw here, as connected_accounts storage succeeded
    }

    // Redirect to success page or specified returnTo
    const redirectUrl = decodeURIComponent(returnTo) + '?youtube_connected=true';
    return NextResponse.redirect(new URL(redirectUrl, url.origin));

  } catch (error) {
    console.error('Error in YouTube callback:', error);
    return NextResponse.redirect(new URL(`/?error=callback_error`, request.url));
  }
} 