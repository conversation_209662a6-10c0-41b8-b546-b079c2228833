'use client';

import { useState, useEffect, useRef } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { useYouTubeConnections } from '@/lib/hooks/useYouTubeConnections';
import { Youtube, MoreVertical, Trash, RefreshCw, AlertTriangle, Plus } from 'lucide-react';
import { Button } from '@/components/ui/Button';
import ConnectYouTube from '@/components/ConnectYouTube';
import { useToast } from '@/components/ui/toast';
import { createClient } from '@/lib/supabase/client';

interface ConnectedYouTubeChannelsProps {
  className?: string;
}

export default function ConnectedYouTubeChannels({ className }: ConnectedYouTubeChannelsProps) {
  const { connections: youtubeConnections, isLoading, error, refetch } = useYouTubeConnections();
  const [imgErrors, setImgErrors] = useState<Record<string, boolean>>({});
  const [showMenus, setShowMenus] = useState<Record<string, boolean>>({});
  const [removingChannels, setRemovingChannels] = useState<Record<string, boolean>>({});
  
  const menuRefs = useRef<Record<string, HTMLDivElement | null>>({});
  const menuButtonRefs = useRef<Record<string, HTMLButtonElement | null>>({});
  const { toast } = useToast();
  
  // Close menu when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      Object.keys(showMenus).forEach(channelId => {
        const menuRef = menuRefs.current[channelId];
        const menuButtonRef = menuButtonRefs.current[channelId];
        
        if (showMenus[channelId] && 
            menuRef && !menuRef.contains(event.target as Node) &&
            menuButtonRef && !menuButtonRef.contains(event.target as Node)) {
          setShowMenus(prev => ({ ...prev, [channelId]: false }));
        }
      });
    }
    
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showMenus]);

  // Function to remove a specific YouTube channel
  async function removeYouTubeChannel(channelId: string) {
    const connection = youtubeConnections?.find(conn => conn.id === channelId);
    
    if (!connection) {
      console.error('Cannot remove YouTube channel: No connection found for ID:', channelId);
      toast({
        title: 'Error',
        description: 'Missing channel information. Please try again.',
        variant: 'destructive',
      });
      return;
    }
    
    try {
      setRemovingChannels(prev => ({ ...prev, [channelId]: true }));
      
      // Use the YouTube channel ID for removal
      const youtubeChannelId = connection.platform_account_id || connection.metadata?.channel_id;
      
      let endpoint = '/api/youtube/remove-channel?';
      if (youtubeChannelId) {
        endpoint += `accountId=${youtubeChannelId}`;
      } else {
        endpoint += `accountId=${channelId}`;
      }
      
      // Get Supabase session for authentication
      const supabase = createClient();
      const { data: { session }, error: sessionError } = await supabase.auth.getSession();

      if (sessionError || !session) {
        throw new Error('Authentication required. Please sign in again.');
      }

      console.log('Sending request to:', endpoint);
      const response = await fetch(endpoint, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
          'Content-Type': 'application/json'
        }
      });
      
      const result = await response.json();
      console.log('Channel removal API response:', result);
      
      if (!response.ok) {
        throw new Error(result.error || 'Failed to remove channel');
      }
      
      toast({
        title: 'Channel removed',
        description: 'Your YouTube channel has been disconnected.',
      });
      
      // Refetch the connections to update the UI
      await refetch();
      
    } catch (error) {
      console.error('Error removing YouTube channel:', error);
      toast({
        title: 'Error',
        description: 'Failed to remove YouTube channel. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setRemovingChannels(prev => ({ ...prev, [channelId]: false }));
      setShowMenus(prev => ({ ...prev, [channelId]: false }));
    }
  }

  // Show loading skeleton while fetching data
  if (isLoading) {
    return (
      <div className={`space-y-2 ${className}`}>
        <div className="flex items-center px-3 py-2.5 rounded-lg border border-gray-200">
          <div className="w-8 h-8 bg-gray-200 rounded-full animate-pulse mr-3"></div>
          <div className="flex-1">
            <div className="h-4 bg-gray-200 rounded animate-pulse mb-1 w-3/4"></div>
            <div className="h-3 bg-gray-200 rounded animate-pulse w-1/2"></div>
          </div>
        </div>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className={`${className}`}>
        <ConnectYouTube
          variant="outline"
          size="sm"
          className="w-full"
          returnTo="/publish"
          onSuccess={() => refetch()}
          onFail={(error) => toast({
            title: 'Connection Failed',
            description: error,
            variant: 'destructive'
          })}
        />
      </div>
    );
  }

  // If we have YouTube connections, display them
  if (youtubeConnections && youtubeConnections.length > 0) {
    return (
      <div className={`space-y-2 ${className}`}>
        {youtubeConnections.map((connection) => {
          const channelId = connection.id;
          const channelTitle = connection.metadata?.channel_title || connection.platform_account_name || 'YouTube Channel';
          const channelThumbnail = connection.metadata?.channel_thumbnail || connection.profile_picture_url;
          const showFallbackIcon = !channelThumbnail || imgErrors[channelId];
          
          return (
            <div key={channelId} className="relative">
              <div className="flex items-center px-3 py-2.5 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors duration-200">
                <Link 
                  href="/analyze/youtube"
                  className="flex items-center flex-1 overflow-hidden"
                >
                  {!showFallbackIcon ? (
                    <div className="w-8 h-8 mr-3 relative overflow-hidden rounded-full bg-gray-100">
                      <Image 
                        src={channelThumbnail} 
                        alt={channelTitle} 
                        width={32} 
                        height={32}
                        className="rounded-full object-cover"
                        onError={() => setImgErrors(prev => ({ ...prev, [channelId]: true }))}
                        unoptimized
                      />
                    </div>
                  ) : (
                    <div className="w-8 h-8 bg-red-600 rounded-full flex items-center justify-center mr-3">
                      <Youtube size={16} className="text-white" />
                    </div>
                  )}
                  <div className="flex-1 overflow-hidden">
                    <p className="font-medium text-gray-800 truncate">{channelTitle}</p>
                    <p className="text-xs text-gray-500">YouTube Channel</p>
                  </div>
                </Link>
                
                {/* Three dots menu button */}
                <button 
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    setShowMenus(prev => ({ ...prev, [channelId]: !prev[channelId] }));
                  }}
                  ref={(el) => { menuButtonRefs.current[channelId] = el; }}
                  className="p-1 rounded-full hover:bg-gray-200 transition-colors"
                >
                  <MoreVertical size={16} className="text-gray-500" />
                </button>
              </div>
              
              {/* Dropdown menu */}
              {showMenus[channelId] && (
                <div 
                  ref={(el) => { menuRefs.current[channelId] = el; }}
                  className="absolute right-0 top-full mt-1 w-48 bg-white rounded-md shadow-lg z-10 border border-gray-200"
                >
                  <div className="py-1">
                    <button
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        removeYouTubeChannel(channelId);
                      }}
                      disabled={removingChannels[channelId]}
                      className="flex w-full items-center px-4 py-2 text-sm text-red-600 hover:bg-red-50"
                    >
                      <Trash size={16} className="mr-2" />
                      {removingChannels[channelId] ? 'Removing...' : 'Remove channel'}
                    </button>
                  </div>
                </div>
              )}
            </div>
          );
        })}
        
        {/* Always show "Connect Another Channel" button */}
        <Link 
          href="/connect/youtube"
          className="flex items-center px-3 py-2.5 rounded-lg border border-dashed border-gray-300 hover:border-gray-400 hover:bg-gray-50 transition-colors duration-200 text-gray-600 hover:text-gray-800"
        >
          <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center mr-3">
            <Plus size={16} className="text-gray-500" />
          </div>
          <div className="flex-1">
            <p className="font-medium">Connect Another Channel</p>
            <p className="text-xs text-gray-500">Add more YouTube channels</p>
          </div>
        </Link>
      </div>
    );
  }

  // Fallback to the connect button if we have no channels
  return (
    <div className={`${className}`}>
      <ConnectYouTube
        variant="outline"
        size="sm"
        className="w-full"
        returnTo="/publish"
        onSuccess={() => refetch()}
        onFail={(error) => toast({
          title: 'Connection Failed',
          description: error,
          variant: 'destructive'
        })}
      />
    </div>
  );
}
