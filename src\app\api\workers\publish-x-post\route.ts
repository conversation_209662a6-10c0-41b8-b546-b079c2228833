/**
 * QStash Worker: Publish X (Twitter) Post
 * 
 * Handles scheduled X post publishing with idempotency and error handling
 * POST /api/workers/publish-x-post
 */

import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { XService } from '@/lib/services/xService';
import { verifySignatureAppRouter } from '@upstash/qstash/nextjs';

// ============================================================================
// Types and Interfaces
// ============================================================================

interface XPostJobPayload {
  postId: string;
  userId: string;
  connectionId: string;
  content: {
    text: string;
    mediaIds?: string[];
    replyToTweetId?: string;
    quoteTweetId?: string;
  };
  scheduledFor: string;
  jobId: string;
}

interface XPostJobResult {
  success: boolean;
  tweetId?: string;
  error?: string;
  publishedAt: string;
}

// ============================================================================
// Worker Handler
// ============================================================================

async function publishXPostWorker(req: NextRequest): Promise<NextResponse> {
  const startTime = Date.now();
  let jobPayload: XPostJobPayload | null = null;

  try {
    // Parse job payload
    jobPayload = await req.json() as XPostJobPayload;
    
    console.log(`[X Worker] Starting job ${jobPayload.jobId} for post ${jobPayload.postId}`);

    // Validate required fields
    if (!jobPayload.postId || !jobPayload.userId || !jobPayload.connectionId || !jobPayload.content?.text) {
      throw new Error('Missing required fields in job payload');
    }

    // Initialize Supabase client
    const supabase = createClient();

    // Check if post has already been published (idempotency)
    const { data: existingPost, error: fetchError } = await supabase
      .from('posts')
      .select('id, status, platform_post_id, published_at')
      .eq('id', jobPayload.postId)
      .single();

    if (fetchError) {
      throw new Error(`Failed to fetch post: ${fetchError.message}`);
    }

    // If already published, return success
    if (existingPost.status === 'published' && existingPost.platform_post_id) {
      console.log(`[X Worker] Post ${jobPayload.postId} already published: ${existingPost.platform_post_id}`);
      
      return NextResponse.json({
        success: true,
        tweetId: existingPost.platform_post_id,
        publishedAt: existingPost.published_at,
        message: 'Post already published (idempotent)',
        processingTimeMs: Date.now() - startTime
      });
    }

    // Update post status to publishing
    await supabase
      .from('posts')
      .update({ 
        status: 'publishing',
        updated_at: new Date().toISOString()
      })
      .eq('id', jobPayload.postId);

    // Initialize X service
    const xService = new XService({
      supabase,
      supabaseAdmin: supabase
    });

    // Validate connection health
    const healthResult = await xService.checkConnectionHealth(jobPayload.userId, jobPayload.connectionId);
    if (!healthResult.success) {
      throw new Error(`Connection health check failed: ${healthResult.error.message}`);
    }

    if (healthResult.data!.needsReauth) {
      throw new Error(`X connection needs re-authentication: ${healthResult.data!.reason}`);
    }

    // Determine post type and publish accordingly
    let publishResult;
    const { text, mediaIds, replyToTweetId, quoteTweetId } = jobPayload.content;

    if (mediaIds && mediaIds.length > 0) {
      // This is a media post - we need to handle media differently in the worker
      // For now, we'll treat it as a text post and log a warning
      console.warn(`[X Worker] Media posts not fully implemented in worker. Publishing as text post.`);
      publishResult = await xService.publishTextTweet(
        jobPayload.userId,
        jobPayload.connectionId,
        text,
        { replyToTweetId, quoteTweetId }
      );
    } else {
      // Text-only post
      publishResult = await xService.publishTextTweet(
        jobPayload.userId,
        jobPayload.connectionId,
        text,
        { replyToTweetId, quoteTweetId }
      );
    }

    if (!publishResult.success) {
      throw new Error(`Failed to publish tweet: ${publishResult.error.message}`);
    }

    const tweetData = publishResult.data!;
    const publishedAt = new Date().toISOString();

    // Update post with success status
    const { error: updateError } = await supabase
      .from('posts')
      .update({
        status: 'published',
        platform_post_id: tweetData.tweetId,
        published_at: publishedAt,
        updated_at: publishedAt,
        publish_metadata: {
          tweetId: tweetData.tweetId,
          authorId: tweetData.authorId,
          publishedViaWorker: true,
          jobId: jobPayload.jobId,
          processingTimeMs: Date.now() - startTime
        }
      })
      .eq('id', jobPayload.postId);

    if (updateError) {
      console.error(`[X Worker] Failed to update post status: ${updateError.message}`);
      // Don't throw here as the tweet was published successfully
    }

    console.log(`[X Worker] Successfully published post ${jobPayload.postId} as tweet ${tweetData.tweetId}`);

    return NextResponse.json({
      success: true,
      tweetId: tweetData.tweetId,
      publishedAt,
      processingTimeMs: Date.now() - startTime
    });

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    const processingTimeMs = Date.now() - startTime;

    console.error(`[X Worker] Job failed:`, {
      jobId: jobPayload?.jobId,
      postId: jobPayload?.postId,
      error: errorMessage,
      processingTimeMs
    });

    // Update post with error status if we have the postId
    if (jobPayload?.postId) {
      try {
        const supabase = createClient();
        await supabase
          .from('posts')
          .update({
            status: 'failed',
            last_error: errorMessage,
            last_error_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          })
          .eq('id', jobPayload.postId);
      } catch (updateError) {
        console.error(`[X Worker] Failed to update post error status:`, updateError);
      }
    }

    return NextResponse.json({
      success: false,
      error: errorMessage,
      processingTimeMs
    }, { status: 500 });
  }
}

// ============================================================================
// Route Handlers
// ============================================================================

/**
 * POST handler with QStash signature verification
 */
export const POST = verifySignatureAppRouter(publishXPostWorker);

/**
 * Handle unsupported methods
 */
export async function GET() {
  return NextResponse.json({
    success: false,
    error: {
      message: 'Method not allowed. This endpoint only accepts POST requests from QStash.',
      code: 'METHOD_NOT_ALLOWED'
    }
  }, { status: 405 });
}

export async function PUT() {
  return NextResponse.json({
    success: false,
    error: {
      message: 'Method not allowed. This endpoint only accepts POST requests from QStash.',
      code: 'METHOD_NOT_ALLOWED'
    }
  }, { status: 405 });
}

export async function DELETE() {
  return NextResponse.json({
    success: false,
    error: {
      message: 'Method not allowed. This endpoint only accepts POST requests from QStash.',
      code: 'METHOD_NOT_ALLOWED'
    }
  }, { status: 405 });
}
