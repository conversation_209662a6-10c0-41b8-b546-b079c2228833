/**
 * API v1 - Instagram Publish Route
 * 
 * Publishes content to Instagram Business accounts
 */

import { NextRequest } from 'next/server';
import { InstagramService } from '@/lib/services/instagramService';
import { 
  with<PERSON><PERSON>r<PERSON><PERSON><PERSON>, 
  requireAuth,
  ErrorFactory
} from '@/lib/error-handler';
import {
  validateRequestBody
} from '@/lib/validation';
import { withVersioning } from '@/lib/versioning/middleware';
import { z } from 'zod';

// ============================================================================
// Validation Schemas
// ============================================================================

const InstagramMediaSchema = z.object({
  image_url: z.string().url('Invalid image URL').optional(),
  video_url: z.string().url('Invalid video URL').optional(),
  media_type: z.enum(['IMAGE', 'VIDEO'], { message: 'Media type must be IMAGE or VIDEO' })
}).strict().refine(
  (data) => {
    // Must have either image_url or video_url based on media_type
    if (data.media_type === 'IMAGE' && !data.image_url) {
      return false;
    }
    if (data.media_type === 'VIDEO' && !data.video_url) {
      return false;
    }
    return true;
  },
  {
    message: 'Must provide image_url for IMAGE type or video_url for VIDEO type'
  }
);

const InstagramPublishSchema = z.object({
  integration_id: z.string().uuid('Invalid integration ID'),
  caption: z.string().max(2200, 'Caption too long for Instagram').optional(),
  media_type: z.enum(['IMAGE', 'VIDEO', 'CAROUSEL_ALBUM'], { message: 'Invalid media type' }),
  image_url: z.string().url('Invalid image URL').optional(),
  video_url: z.string().url('Invalid video URL').optional(),
  children: z.array(InstagramMediaSchema).min(2, 'Carousel must have at least 2 items').max(10, 'Carousel can have maximum 10 items').optional(),
  location_id: z.string().optional(),
  user_tags: z.array(z.object({
    username: z.string().min(1, 'Username required'),
    x: z.number().min(0).max(1, 'X coordinate must be between 0 and 1'),
    y: z.number().min(0).max(1, 'Y coordinate must be between 0 and 1')
  })).max(20, 'Maximum 20 user tags allowed').optional(),
  is_published: z.boolean().default(true)
}).strict().refine(
  (data) => {
    // Validate media requirements based on type
    if (data.media_type === 'IMAGE' && !data.image_url) {
      return false;
    }
    if (data.media_type === 'VIDEO' && !data.video_url) {
      return false;
    }
    if (data.media_type === 'CAROUSEL_ALBUM' && (!data.children || data.children.length < 2)) {
      return false;
    }
    return true;
  },
  {
    message: 'Media requirements not met for the specified media type'
  }
);

// ============================================================================
// Route Handler
// ============================================================================

/**
 * Internal handler for Instagram publishing
 */
async function instagramPublishHandler(req: NextRequest) {
  // 1. Authenticate user
  const { user } = await requireAuth(req);

  // 2. Validate request body
  const validatedData = await validateRequestBody(req, InstagramPublishSchema);

  // 3. Initialize Instagram service
  const { instagramService } = await import('@/lib/services');

  // 4. Prepare post data
  const postData = {
    caption: validatedData.caption,
    media_type: validatedData.media_type,
    image_url: validatedData.image_url,
    video_url: validatedData.video_url,
    children: validatedData.children,
    location_id: validatedData.location_id,
    user_tags: validatedData.user_tags,
    is_published: validatedData.is_published
  };

  // 5. Publish the post
  const publishResult = await instagramService.publishPost(
    validatedData.integration_id,
    postData
  );

  if (!publishResult.success) {
    throw publishResult.error;
  }

  const postResult = publishResult.data!;

  // 6. Return success response
  return {
    success: true,
    data: {
      platform_post_id: postResult.id,
      permalink: postResult.permalink,
      media_type: postResult.media_type,
      media_url: postResult.media_url,
      thumbnail_url: postResult.thumbnail_url,
      timestamp: postResult.timestamp,
      published: validatedData.is_published
    },
    metadata: {
      provider: 'instagram',
      integration_id: validatedData.integration_id,
      published_at: new Date().toISOString(),
      media_type: validatedData.media_type,
      has_caption: !!validatedData.caption,
      caption_length: validatedData.caption?.length || 0,
      has_location: !!validatedData.location_id,
      user_tags_count: validatedData.user_tags?.length || 0,
      is_carousel: validatedData.media_type === 'CAROUSEL_ALBUM',
      carousel_items: validatedData.children?.length || 0
    }
  };
}

/**
 * POST /api/v1/integrations/instagram/publish
 * Publish content to Instagram Business account
 */
export const POST = withVersioning(withErrorHandler(instagramPublishHandler));
