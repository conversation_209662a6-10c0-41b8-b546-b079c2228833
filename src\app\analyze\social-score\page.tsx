'use client'

import React, { useState, useEffect, useRef, useMemo, useCallback } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { 
  ArrowUp, 
  ArrowDown, 
  X, 
  Menu, 
  Info, 
  ChevronDown, 
  Search,
  BarChart,
  CheckCircle,
  Circle,
  TrendingUp,
  TrendingDown,
  ExternalLink,
  Calendar,
  ArrowRight,
  UserRound,
  Twitter,
  Check,
  RefreshCw
} from 'lucide-react'
import {
  LineChart, 
  Line, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip,
  ResponsiveContainer,
  AreaChart,
  Area,
  Bar,
  ReferenceLine,
  RadarChart,
  Radar,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis
} from 'recharts'
import Link from 'next/link'
import AnalyzeLayout from '@/components/layout/AnalyzeLayout'
import Image from 'next/image'
import { usePlatform } from '@/contexts/PlatformContext'
import { useYouTubeChannel } from '@/lib/hooks/useYouTubeChannel'
import { useToast } from '@/components/ui/toast'
import { useDemoState } from '@/contexts/DemoStateContext'
import { useGoogleAuth } from '@/lib/hooks/useGoogleAuth'
import { PlatformIcon } from '@/components/shared/PlatformIcon'
import YouTubeAnalyticsReconnection from '@/components/YouTubeAnalyticsReconnection'
import YouTubeThumbnail from '@/components/YouTubeThumbnail'
import { getYouTubeVideoId } from '@/components/YouTubeThumbnail'
import YouTubeInsightsCard from '@/components/YouTubeInsightsCard'
import YouTubeReconnectionModal from '@/components/YouTubeReconnectionModal'

// Type definition for SocialScoreData
type SocialScoreData = {
  score: number;
  scoreChange: number;
  history: Array<{ name: string; score: number }>;
  factors: Record<string, { value: string; change: number }>;
  insights: any;
  performance: any;
  posting: any;
  radarData: Array<{ subject: string; A: number; fullMark: number }>;
}

// Ensure the useYouTubeAuth hook is defined if it's not already imported
// This should be placed somewhere after existing imports but before the component definitions

// Add after line 36 (PlatformIcon import)
// Since useYouTubeAuth isn't available, let's create a simple implementation
const useYouTubeAuth = () => {
  return {
    isConnected: true,
    // Add other properties that might be needed
    reconnect: () => Promise.resolve(true)
  };
};

// Sample score history data with the correct type
const scoreHistoryData = [
  { name: 'Jan', score: 750 },
  { name: 'Feb', score: 770 },
  { name: 'Mar', score: 790 },
  { name: 'Apr', score: 785 },
  { name: 'May', score: 800 },
  { name: 'Jun', score: 810 },
  { name: 'Jul', score: 802 }
];

// Sample data for the radar chart
const radarData = [
  { subject: 'Engagement', A: 85, fullMark: 100 },
  { subject: 'Consistency', A: 76, fullMark: 100 },
  { subject: 'Growth', A: 68, fullMark: 100 },
  { subject: 'Content', A: 92, fullMark: 100 },
  { subject: 'Audience', A: 73, fullMark: 100 }
];

// Chart configuration to fix maximum update depth exceeded error
const CHART_CONFIG = {
  isAnimationActive: false,
  animationBegin: 0,
  animationDuration: 0,
  animationEasing: 'linear'
};

// Sample data for social factors
const socialFactorsData = {
  postViews: { value: '9.2k', change: 12 },
  postClicks: { value: '1.5k', change: 8 },
  postInteractions: { value: '2.8k', change: 15 },
  postShares: { value: '620', change: 18 },
  followers: { value: '1.9k', change: 6 },
  postComments: { value: '285', change: 10 }
};

// Sample data for social insights
const socialInsightsData = {
  date: 'Oct 28, 2023',
  content: 'Your posts with questions in the caption get 25% more comments. Try incorporating more questions to boost engagement.',
  metrics: {
    impressions: '1.8M',
    engagements: '135',
    reactions: '98',
    comments: '15',
    shares: '22'
  }
};

// Sample data for performance trends
const performanceTrendsData = {
  engagements: { 
    total: 245, 
    change: 15.8, 
    reactions: 185, 
    comments: 32, 
    shares: 28 
  },
  impressions: { 
    total: 12500, 
    change: 18.5 
  }
};

// Sample data for posting consistency
const postingConsistencyData = {
  frequency: { total: 8, change: 33.3 },
  followerGrowth: { total: 42, change: 27.3 }
};

// Define valid scenario types
type ScenarioType = 'mediumPerformance' | 'highPerformance' | 'lowPerformance' | 'newAccount';

// Helper function to generate history data
function generateHistoryData(dateRange: string, baseScore: number, scenario: ScenarioType): Array<{name: string; score: number}> {
  const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
  const daysOfWeek = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
  let dates: string[] = [];
  let scores: number[] = [];
  let varianceFactor = 0;
  
  switch(scenario) {
    case 'highPerformance':
      varianceFactor = 5;
      break;
    case 'mediumPerformance':
      varianceFactor = 15;
      break;
    case 'lowPerformance':
      varianceFactor = 25;
      break;
    case 'newAccount':
      varianceFactor = 30;
      break;
  }
  
  if (dateRange.includes('Daily')) {
    dates = daysOfWeek;
  } else if (dateRange.includes('Weekly')) {
    dates = ['Week 1', 'Week 2', 'Week 3', 'Week 4'];
  } else {
    dates = months.slice(0, 7);
  }
  
  // Generate score data with trend based on scenario
  let currentScore = baseScore - (30 * dates.length);
  const upwardTrend = scenario === 'highPerformance' || scenario === 'mediumPerformance';
  const trendFactor = upwardTrend ? 30 : -10;
  
  for (let i = 0; i < dates.length; i++) {
    currentScore += trendFactor + (Math.random() * varianceFactor * 2 - varianceFactor);
    scores.push(Math.round(currentScore));
  }
  
  return dates.map((date, i) => ({
    name: date,
    score: scores[i]
  }));
}

// Function to get date range based on selection
const getDateRangeText = (selection: string): string => {
  const today = new Date();
  const formatDate = (date: Date): string => {
    return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' });
  };
  
  let startDate = new Date();
  
  switch(selection) {
    case 'Weekly overview':
      startDate.setDate(today.getDate() - 7);
      break;
    case 'Last 30 days':
      startDate.setDate(today.getDate() - 30);
      break;
    case 'Last 90 days':
      startDate.setDate(today.getDate() - 90);
      break;
    case 'Last 6 months':
      startDate.setMonth(today.getMonth() - 6);
      break;
    case 'Last one year':
      startDate.setFullYear(today.getFullYear() - 1);
      break;
    default:
      startDate.setDate(today.getDate() - 7);
  }
  
  return `For ${formatDate(startDate)} - ${formatDate(today)}`;
};

// Fetch social score data from API
async function fetchSocialScoreData(dateRange: string, channelName: string, scenario: ScenarioType = 'mediumPerformance'): Promise<SocialScoreData> {
  // For demo, we're returning mock data based on the scenario
  // In a real app, this would make a fetch request to your API
  
  // Mock data logic based on scenario
  let socialScore = 0;
  let scoreChange = 0;
  
  switch(scenario) {
    case 'highPerformance':
      socialScore = 850 + Math.floor(Math.random() * 50);
      scoreChange = 15 + Math.floor(Math.random() * 10);
      break;
    case 'mediumPerformance':
      socialScore = 650 + Math.floor(Math.random() * 100);
      scoreChange = 5 + Math.floor(Math.random() * 15);
      break;
    case 'lowPerformance':
      socialScore = 450 + Math.floor(Math.random() * 100);
      scoreChange = -10 - Math.floor(Math.random() * 15);
      break;
    case 'newAccount':
    default:
      socialScore = 500 + Math.floor(Math.random() * 100);
      scoreChange = 0;
      break;
  }
  
  // Generate history data
  const history = generateHistoryData(dateRange, socialScore, scenario);
  
  // Return the data
  return {
    score: socialScore,
    scoreChange: scoreChange,
    history: history,
    factors: socialFactorsData,
    insights: socialInsightsData,
    performance: performanceTrendsData,
    posting: postingConsistencyData,
    radarData: radarData
  };
}

// Add this before the ConnectedState component
const YouTubeVideoTitle = ({ title, videoId }: { title: string | null | undefined, videoId: string | null | undefined }) => {
  // Track if title has loaded
  const [hasLoaded, setHasLoaded] = useState(false);
  // Store a persistent version of the title
  const [displayTitle, setDisplayTitle] = useState('Loading YouTube Title...');
  
  // Update the display title when the actual title changes
  useEffect(() => {
    // Only update if we have a valid title
    if (typeof title === 'string' && title.length > 0) {
      setDisplayTitle(title);
      setHasLoaded(true);
    }
  }, [title, videoId]);
  
  return (
    <h4 
      className={`text-lg font-semibold text-gray-900 mb-2 transition-opacity duration-300 ${hasLoaded ? 'opacity-100' : 'opacity-70'}`}
      data-testid="video-title"
    >
      {displayTitle}
    </h4>
  );
};

export default function SocialScoreInsightsPage() {
  const { userState } = useDemoState();
  const router = useRouter();
  const searchParams = useSearchParams();

  // Render different content based on user state
  const renderContent = () => {
    switch (userState) {
      case 'new':
        return <NewUserState />;
      case 'no-accounts':
        return <NoAccountsState />;
      case 'connected':
      default:
        return <ConnectedState />;
    }
  };

  return (
    <AnalyzeLayout>
      <div className="space-y-6">
        <div className="flex flex-col space-y-2">
          <h1 className="text-2xl font-bold text-gray-900">Social score and insights</h1>
          <p className="text-gray-500">Track your social media performance and get actionable insights</p>
        </div>
        
        {renderContent()}
      </div>
    </AnalyzeLayout>
  )
}

// New User State Component
function NewUserState() {
  return (
    <div className="bg-white rounded-xl border border-gray-200 p-8 text-center shadow-sm transition-all hover:shadow-md">
      <div className="max-w-md mx-auto">
        <div className="w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6 shadow-inner">
          <BarChart size={32} className="text-blue-600" />
        </div>
        <h2 className="text-2xl font-bold text-gray-900 mb-4">Welcome to Social Score Analytics</h2>
        <p className="text-gray-600 mb-8 leading-relaxed">
          Connect your social media accounts to start tracking your social score, engagement metrics, and audience growth.
        </p>
        <button className="px-8 py-3 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-colors shadow-sm hover:shadow-md transform hover:-translate-y-0.5 transition-transform">
          Connect Your Accounts
        </button>
      </div>
    </div>
  )
}

// No Accounts State Component
function NoAccountsState() {
  return (
    <div className="bg-white rounded-xl border border-gray-200 p-8 text-center shadow-sm transition-all hover:shadow-md">
      <div className="max-w-md mx-auto">
        <div className="w-20 h-20 bg-amber-100 rounded-full flex items-center justify-center mx-auto mb-6 shadow-inner">
          <Info size={32} className="text-amber-600" />
        </div>
        <h2 className="text-2xl font-bold text-gray-900 mb-4">Connect Your Social Accounts</h2>
        <p className="text-gray-600 mb-8 leading-relaxed">
          To view your Social Score, you need to connect at least one social media account. Your score will be calculated based on your engagement, consistency, and growth.
        </p>
        <button className="px-8 py-3 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-colors shadow-sm hover:shadow-md transform hover:-translate-y-0.5 transition-transform">
          Connect Accounts
        </button>
        <p className="mt-6 text-sm text-gray-500">
          You can connect accounts from Facebook, Instagram, Twitter, LinkedIn, and more.
        </p>
      </div>
    </div>
  )
}

// Connected State Component
function ConnectedState() {
  const [dateRange, setDateRange] = useState('Weekly overview')
  const [showDatePicker, setShowDatePicker] = useState(false)
  const [showScoreInfo, setShowScoreInfo] = useState(false)
  const [selectedChannel, setSelectedChannel] = useState('')
  const [selectedPlatform, setSelectedPlatform] = useState('')
  const [showChannelSelector, setShowChannelSelector] = useState(false)
  const [loading, setLoading] = useState(true)
  const [socialScore, setSocialScore] = useState(802)
  const [scoreChange, setScoreChange] = useState(8)
  const [scoreHistory, setScoreHistory] = useState(scoreHistoryData)
  const [factorsData, setFactorsData] = useState<any>(null)
  const [performanceData, setPerformanceData] = useState<any>(null)
  const [postingData, setPostingData] = useState<any>(null)
  const [radarChartData, setRadarChartData] = useState(radarData)
  const [needsAnalyticsPermission, setNeedsAnalyticsPermission] = useState(false)
  const [showYouTubeReconnectModal, setShowYouTubeReconnectModal] = useState(false)
  const [hasYouTubeConnectionError, setHasYouTubeConnectionError] = useState(false)
  const { platforms } = usePlatform()
  const { channelInfo: youtubeChannel } = useYouTubeChannel()
  const { isConnected: isYouTubeConnected } = useYouTubeAuth()
  const scoreInfoRef = useRef<HTMLDivElement>(null)
  const datePickerRef = useRef<HTMLDivElement>(null)
  const channelSelectorRef = useRef<HTMLDivElement>(null)
  const videoIdRef = useRef<string | null>(null)
  const initializedRef = useRef(false)
  const hasSetInitialChannelRef = useRef(false)
  const { toast } = useToast()
  const [analyticsPermissionDismissed, setAnalyticsPermissionDismissed] = useState(false)
    
  // Initialize insightsData with a loading state
  const [insightsData, setInsightsData] = useState<any>({
    publishedAt: null,
    videoId: null,
    title: null,
    content: 'Loading YouTube content...',
    metrics: {
      impressions: 0,
      engagements: 0,
      comments: 0,
      shares: 0
    },
    hasError: false
  })

  // Filter connected platforms
  const connectedPlatforms = platforms.filter((p: any) => p.status === 'connected')
  const hasConnectedPlatforms = connectedPlatforms.length > 0

  // Memoize channel list calculation to prevent unnecessary recalculations
  const channelsList = useMemo(() => {
    const list = [];
    
    // Add YouTube channel if connected
    if (isYouTubeConnected && youtubeChannel) {
      list.push({
        name: youtubeChannel.title || 'YouTube Channel', // Add default name if title is undefined
        platform: 'youtube',
        id: youtubeChannel.channelId || youtubeChannel.id
      });
    }
    
    // Add other connected platforms as generic entries
    connectedPlatforms.forEach(platform => {
      if (platform.name.toLowerCase() !== 'youtube') {
        list.push({
          name: `${platform.name} Account`,
          platform: platform.name.toLowerCase()
        });
      }
    });
    
    // Add "All channels" option at the beginning
    if (list.length > 0) {
      list.unshift({
        name: 'All channels',
        platform: 'all'
      });
    }
    
    return list;
  }, [isYouTubeConnected, youtubeChannel, connectedPlatforms]);

  // Only set initial channel when component mounts
  useEffect(() => {
    if (!hasSetInitialChannelRef.current && channelsList.length > 0) {
      hasSetInitialChannelRef.current = true;
      setSelectedChannel(channelsList[0].name);
      setSelectedPlatform(channelsList[0].platform);
    }
  }, [channelsList]);

  // Function to fetch YouTube analytics data directly
  const fetchYouTubeAnalyticsData = useCallback(async () => {
    if (selectedPlatform !== 'youtube') return;

    try {
      console.log(`Fetching real YouTube analytics data for timeframe: ${dateRange}`);
      setLoading(true);

      const response = await fetch(`/api/youtube/social-score?timeframe=${encodeURIComponent(dateRange)}`);

      if (!response.ok) {
        const errorData = await response.json();
        console.error('YouTube social score API error:', errorData);

        // If analytics permission is missing, show a specific message
        if (response.status === 403 && errorData.error?.includes('analytics')) {
          setNeedsAnalyticsPermission(true);
          toast({
            title: 'Analytics permission required',
            description: 'Please reconnect your YouTube account with analytics permissions.',
            variant: 'destructive'
          });
        } else {
          toast({
            title: 'Error loading YouTube data',
            description: errorData.error || 'Failed to load YouTube analytics',
            variant: 'destructive'
          });
        }
        return null;
      }

      const data = await response.json();
      console.log('Received YouTube social score data:', data);

      // Clear the analytics permission flag if we successfully got data
      setNeedsAnalyticsPermission(false);

      return data;
    } catch (error) {
      console.error('Failed to fetch YouTube analytics:', error);
      toast({
        title: 'Error loading YouTube data',
        description: 'Failed to load YouTube analytics data. Please try again later.',
        variant: 'destructive'
      });
      return null;
    } finally {
      setLoading(false);
    }
  }, [dateRange, selectedPlatform, toast]);

  // Fetch data when selected channel or date range changes
  useEffect(() => {
    if (!selectedChannel) return; // Don't fetch if no channel is selected

    // Fetch social score data based on selected date range and channel
    const fetchData = async () => {
      setLoading(true);
      try {
        // For YouTube channels, use the real YouTube analytics API
        if (selectedPlatform === 'youtube') {
          console.log(`Fetching YouTube social score data for ${selectedChannel} with timeframe: ${dateRange}`);

          // Directly fetch the real YouTube analytics data
          const data = await fetchYouTubeAnalyticsData();

          if (data) {
            // If we got real data, use it but don't overwrite insights
            // that will be handled separately by fetchYouTubeInsights
            updateStateWithData(data, true);
          } else {
            // If fetch failed, fall back to mock data
            console.log('Falling back to mock data with highPerformance scenario');
            const fallbackData = await fetchSocialScoreData(dateRange, selectedChannel, 'highPerformance');
            updateStateWithData(fallbackData, true);
          }
        } else {
          // For other platforms, use the mock data as before
          let scenario: ScenarioType = 'mediumPerformance';

          // If this is a new account with limited history, use the newAccount scenario
          if (selectedChannel.includes('New') || selectedChannel.includes('recently connected')) {
            scenario = 'newAccount';
            console.log('Using new account scenario for recently connected channel');
          }

          console.log(`Fetching social score data for ${selectedChannel} with scenario: ${scenario}`);
          const data = await fetchSocialScoreData(dateRange, selectedChannel, scenario);
          updateStateWithData(data, false);
        }
      } catch (error) {
        console.error('Failed to fetch social score data:', error);
        toast({
          title: 'Error loading social score data',
          description: 'Please try again later',
          variant: 'destructive'
        });
      } finally {
        setLoading(false);
      }
    };

    // Helper function to update state with data
    const updateStateWithData = (data: any, skipInsights: boolean) => {
      setSocialScore(data.score);
      setScoreChange(data.scoreChange);
      setScoreHistory(data.history);
      setRadarChartData(data.radarData);
      setFactorsData(data.factors);

      // Only set insights data if not fetching YouTube insights separately
      if (!skipInsights || !data.insights) {
        console.log('Setting insights data from social score data', data.insights);
        setInsightsData(data.insights);
      } else {
        console.log('Skipping insights data update, will be handled by fetchYouTubeInsights');
      }

      // Fix for compatibility with UI - ensure subscribers data is available in performanceData
      const performanceWithSubscribers = { ...data.performance };
      if (!performanceWithSubscribers.subscribers && data.posting?.followerGrowth) {
        performanceWithSubscribers.subscribers = data.posting.followerGrowth;
        console.log('Added missing subscribers field to performanceData', performanceWithSubscribers);
      }

      setPerformanceData(performanceWithSubscribers);
      setPostingData(data.posting);

      // Debug logs for data structure
      console.log('Performance Data Structure:', performanceWithSubscribers);
      console.log('Posting Data Structure:', data.posting);
    };

    fetchData();
  }, [dateRange, selectedChannel, selectedPlatform]);

  // Calculate date range text
  const dateRangeText = useMemo(() => getDateRangeText(dateRange), [dateRange]);

  // Handle clicks outside dropdown menus
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (scoreInfoRef.current && !scoreInfoRef.current.contains(event.target as Node)) {
        setShowScoreInfo(false);
      }
      
      if (datePickerRef.current && !datePickerRef.current.contains(event.target as Node)) {
        setShowDatePicker(false);
      }
      
      if (channelSelectorRef.current && !channelSelectorRef.current.contains(event.target as Node)) {
        setShowChannelSelector(false);
      }
    }
    
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Function to get platform icon style based on platform name
  const getPlatformColor = (platform: string) => {
    const platformLower = platform.toLowerCase();
    if (platformLower.includes('facebook')) return 'bg-blue-600';
    if (platformLower.includes('instagram')) return 'bg-purple-600';
    if (platformLower.includes('twitter')) return 'bg-blue-400';
    if (platformLower.includes('linkedin')) return 'bg-blue-700';
    if (platformLower.includes('youtube')) return 'bg-red-600';
    if (platformLower.includes('tiktok')) return 'bg-black';
    if (platformLower.includes('all')) return 'bg-gray-600';
    return 'bg-gray-500';
  };

  // Function to extract YouTube video ID from various sources
  const extractYouTubeVideoId = (video: any) => {
    if (!video) return null;
    
    // Check for direct videoId property
    if (video.videoId) return video.videoId;
    
    // Check for id property formats
    if (video.id) {
      if (typeof video.id === 'string') return video.id;
      if (video.id.videoId) return video.id.videoId;
    }
    
    // Check for URL in various properties
    if (video.snippet?.resourceId?.videoId) return video.snippet.resourceId.videoId;
    
    // Try to extract from URLs
    const urlFields = [
      video.url,
      video.link,
      video.videoUrl,
      video.snippet?.thumbnails?.default?.url
    ];
    
    for (const field of urlFields) {
      if (field) {
        const videoId = getYouTubeVideoId(field);
        if (videoId) return videoId;
      }
    }
    
    return null;
  };

  // Function to fetch YouTube post insights - updated to handle permission errors better
  const fetchYouTubeInsights = async () => {
    if (!youtubeChannel?.id && !youtubeChannel?.channelId) {
      console.log('No YouTube channel ID available for fetching insights');
      return;
    }
    
    const channelId = youtubeChannel.id || youtubeChannel.channelId;
    
    try {
      console.log('Fetching YouTube post insights for most engaging content...');
      setLoading(true);
      setHasYouTubeConnectionError(false); // Reset connection error state
      
      // Get time before fetch for debugging
      const startTime = Date.now();
      
      // Modified to fetch most engaging videos for the specific timeframe
      // Add a special flag for Weekly timeframe to ensure it works properly
      const isWeeklyTimeframe = dateRange === 'Weekly overview';
      
      const response = await fetch(
        `/api/youtube/videos?channelId=${channelId}&maxResults=50&sortBy=engagement&timeframe=${encodeURIComponent(dateRange)}${isWeeklyTimeframe ? '&weekly=true' : ''}`
      );
      
      console.log(`Fetch completed in ${Date.now() - startTime}ms for timeframe: ${dateRange}`);
      
      if (!response.ok) {
        const errorData = await response.json();
        console.error(`Error fetching YouTube videos: ${response.status}`, errorData);
        
        // Check for token errors
        if (response.status === 401 || 
            (errorData.error && 
             (errorData.error === 'invalid_token' || 
              errorData.error === 'invalid_grant' || 
              errorData.error.includes('token')))) {
          console.log('YouTube token error detected, showing reconnection UI');
          setHasYouTubeConnectionError(true);
          setShowYouTubeReconnectModal(true);
          
          // Set fallback insights data
          if ((window as any).__fetchingYouTubeInsights === true) {
            setInsightsData({
              publishedAt: '2023-04-14T12:00:00Z',
              videoId: 'dQw4w9WgXcQ',
              title: 'YouTube Connection Required',
              content: 'Your YouTube connection needs to be refreshed. Please reconnect your account to view insights.',
              metrics: {
                impressions: 0,
                engagements: 0,
                comments: 0,
                shares: 0
              },
              hasError: true
            });
          }
          setLoading(false);
          return;
        }
        
        // Check if this is a permissions issue
        if (response.status === 403 && errorData.error?.includes('permission')) {
          setNeedsAnalyticsPermission(true);
        }
        
        // Use mock data as fallback
        console.log('YouTube insights fetch failed, using placeholder data');
        
        // Only update state if our insights fetch hasn't been cancelled
        if ((window as any).__fetchingYouTubeInsights === true) {
          setInsightsData({
            publishedAt: '2023-04-14T12:00:00Z',
            videoId: 'dQw4w9WgXcQ', // Example video ID as fallback
            title: 'YouTube Video - Data Unavailable',
            content: 'We couldn\'t load this video\'s data. Try refreshing the page or reconnecting your YouTube account.',
            metrics: {
              impressions: 8,
              engagements: 1,
              comments: 1,
              shares: 0
            },
            hasError: true
          });
        } else {
          console.log('Skipping insights update because fetch was cancelled');
        }
        
        setLoading(false);
        return;
      }
      
      const data = await response.json();
      console.log('Received YouTube videos:', data);
      
      // Check if insights fetch was cancelled while waiting for response
      if ((window as any).__fetchingYouTubeInsights !== true) {
        console.log('Skipping YouTube insights processing - fetch was cancelled');
        setLoading(false);
        return;
      }
      
      // Check if we have meaningful data
      if (!data || !data.items || data.items.length === 0) {
        console.log('No videos found in API response, using placeholder');
        // No videos found, use placeholder
        setInsightsData({
          publishedAt: '2023-04-14T12:00:00Z',
          videoId: 'dQw4w9WgXcQ', // Fallback example
          title: 'YouTube Video - Data Unavailable',
          content: 'We couldn\'t load this video\'s data. Try refreshing the page or reconnecting your YouTube account.',
          metrics: {
            impressions: 8,
            engagements: 1,
            comments: 1,
            shares: 0
          },
          hasError: true
        });
        setLoading(false);
        return;
      }
      
      // If we have videos, find the most engaging one (API should already be sorted by viewCount)
      const sortedVideos = [...data.items];
      
      // Verify we have actual videos with statistics
      const validVideos = sortedVideos.filter(video => 
        video && video.statistics && (video.statistics.viewCount !== undefined)
      );
      
      console.log(`Found ${validVideos.length} videos with statistics out of ${sortedVideos.length} total videos`);
      
      if (validVideos.length === 0) {
        console.log('No videos with valid statistics found, using placeholder');
        // No videos found, use placeholder
        setInsightsData({
          publishedAt: '2023-04-14T12:00:00Z',
          videoId: 'dQw4w9WgXcQ', // Fallback example
          title: 'YouTube Video - Data Unavailable',
          content: 'We couldn\'t load this video\'s data. Try refreshing the page or reconnecting your YouTube account.',
          metrics: {
            impressions: 8,
            engagements: 1,
            comments: 1,
            shares: 0
          },
          hasError: true
        });
        setLoading(false);
        return;
      }
      
      // Get the most engaging video (should be first in the sorted list)
      const topVideo = validVideos[0];
      console.log('Most engaging video found:', topVideo.snippet?.title);
      
      // Log all available data for debugging
      console.debug('Video data available:', {
        snippet: topVideo.snippet,
        title: topVideo.snippet?.title,
        description: topVideo.snippet?.description,
        statistics: topVideo.statistics
      });
      
      // Try different methods to extract the video ID
      let videoId = null;
      
      // First try to get from resourceId if it's a playlist item
      if (topVideo.snippet?.resourceId?.videoId) {
        videoId = topVideo.snippet.resourceId.videoId;
        console.log('Found video ID from resourceId:', videoId);
      } 
      // Then try the regular id
      else if (topVideo.id) {
        if (typeof topVideo.id === 'string') {
          videoId = topVideo.id;
          console.log('Found video ID from string id:', videoId);
        } else if (topVideo.id.videoId) {
          videoId = topVideo.id.videoId;
          console.log('Found video ID from id.videoId:', videoId);
        } else if (topVideo.id.kind && topVideo.id.kind.includes('youtube#video')) {
          // Sometimes the ID is in a different format
          videoId = topVideo.id.videoId || topVideo.id;
          console.log('Found video ID from id with youtube#video kind:', videoId);
        }
      }
      
      // If we still don't have a video ID, try to extract it from URLs
      if (!videoId) {
        // Check if there's a thumbnail URL to extract from
        if (topVideo.snippet?.thumbnails?.default?.url) {
          const thumbnailUrl = topVideo.snippet.thumbnails.default.url;
          const match = thumbnailUrl.match(/vi\/([a-zA-Z0-9_-]{11})/);
          if (match && match[1]) {
            videoId = match[1];
            console.log('Extracted video ID from thumbnail URL:', videoId);
          }
        }
      }
      
      // Last resort, try the full extraction function
      if (!videoId) {
        videoId = extractYouTubeVideoId(topVideo);
        console.log('Extracted video ID using full extraction function:', videoId);
      }
      
      // If we still don't have a video ID, use a fallback
      if (!videoId) {
        console.warn('Could not extract video ID from response, using fallback');
        videoId = 'dQw4w9WgXcQ'; // Fallback video ID
      }
      
      console.log('Final video ID being used:', videoId);
      
      // Store in ref for persistence
      videoIdRef.current = videoId;
      
      // Get accurate statistics from the video data
      // Extract the real statistics from the first video
      let viewCount = 0;
      let likeCount = 0;
      let commentCount = 0;
      
      // Try to get the actual statistics from the video data
      if (topVideo.statistics) {
        viewCount = parseInt(topVideo.statistics.viewCount || '0');
        likeCount = parseInt(topVideo.statistics.likeCount || '0');
        commentCount = parseInt(topVideo.statistics.commentCount || '0');
        console.log('Video statistics found:', { viewCount, likeCount, commentCount });
      } else {
        console.warn('No statistics found in video data, using estimates');
        // Fallback to some reasonable values based on channel size
        viewCount = Math.floor(Math.random() * 5000) + 1000;
        likeCount = Math.floor(viewCount * 0.1);
        commentCount = Math.floor(viewCount * 0.02);
      }
      
      const totalEngagements = likeCount + commentCount;
      const shareEstimate = Math.floor(viewCount * 0.01); // Estimate shares as 1% of views
      
      // For cases with zero metrics, enforce minimum display values for better UX
      const displayViewCount = viewCount > 0 ? viewCount : 0;
      const displayLikeCount = likeCount > 0 ? likeCount : 0;
      const displayCommentCount = commentCount > 0 ? commentCount : 0;
      const displayEngagements = totalEngagements > 0 ? totalEngagements : 0;
      const displayShares = shareEstimate > 0 ? shareEstimate : 0;
      
      // Format the insights data with accurate metrics
      const videoTitle = topVideo.snippet?.title || 'YouTube Video';
      console.log('Setting video title to:', videoTitle, 'with metrics:', {
        views: displayViewCount,
        engagements: displayEngagements,
        likes: displayLikeCount,
        comments: displayCommentCount,
        shares: displayShares
      });
      
      // Final check to ensure we're still the active insights fetch
      if ((window as any).__fetchingYouTubeInsights !== true) {
        console.log('Cancelling insights data update - fetch was cancelled during processing');
        setLoading(false);
        return;
      }
      
      console.log('FINAL INSIGHTS UPDATE: Setting insights data with video', videoTitle);
      
      setInsightsData({
        publishedAt: topVideo.snippet?.publishedAt || '2023-04-14T12:00:00Z',
        videoId: videoId,
        title: videoTitle,
        content: topVideo.snippet?.description ? 
          (topVideo.snippet.description.substring(0, 150) + '...') : 
          'No description available',
        metrics: {
          impressions: displayViewCount,
          engagements: displayEngagements,
          comments: displayCommentCount,
          shares: displayShares
        },
        hasError: false
      });
      
      // Verify insightsData was set correctly
      console.log('InsightsData set with title:', videoTitle, 'and videoId:', videoId);
    } catch (error) {
      console.error('Error fetching YouTube insights:', error);
      
      // Check if this is a token error
      const errorMessage = String(error);
      if (errorMessage.includes('invalid_token') || 
          errorMessage.includes('invalid_grant') || 
          errorMessage.includes('token has been expired')) {
        console.log('Token error detected in YouTube insights fetch');
        setHasYouTubeConnectionError(true);
        setShowYouTubeReconnectModal(true);
      }
      
      // Use placeholder on error
      if ((window as any).__fetchingYouTubeInsights === true) {
        setInsightsData({
          publishedAt: '2023-04-14T12:00:00Z',
          videoId: 'dQw4w9WgXcQ', // Fallback example
          title: 'YouTube Video - Data Unavailable',
          content: 'We couldn\'t load this video\'s data. Try refreshing the page or reconnecting your YouTube account.',
          metrics: {
            impressions: 8,
            engagements: 1,
            comments: 1,
            shares: 0
          },
          hasError: true
        });
      } else {
        console.log('Skipping error fallback update - fetch was cancelled');
      }
    } finally {
      setLoading(false);
    }
  };

  // Helper to format large numbers with K, M suffix
  const formatNumber = (num: string | number) => {
    num = typeof num === 'string' ? parseInt(num) : num;
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  // Your insights section - enhanced to match reference image
  useEffect(() => {
    // Flag to track if we're actively fetching insights
    const insightsRef = { current: false };
    
    if (selectedPlatform === 'youtube' && (youtubeChannel?.id || youtubeChannel?.channelId)) {
      insightsRef.current = true;
      console.log('Triggering dedicated YouTube insights fetch');
      
      // Set a flag that insights are being actively fetched
      (window as any).__fetchingYouTubeInsights = true;
      
      fetchYouTubeInsights()
        .finally(() => {
          // Clear the flag when done
          insightsRef.current = false;
          (window as any).__fetchingYouTubeInsights = false;
        });
    }
    
    // Cleanup function
    return () => {
      // If this effect is cleaned up while fetching, update the flag
      if (insightsRef.current) {
        console.log('Cleaning up YouTube insights fetch');
        (window as any).__fetchingYouTubeInsights = false;
      }
    };
  }, [youtubeChannel, selectedPlatform, dateRange]);

  // Debug effect to log insights data changes
  useEffect(() => {
    if (insightsData) {
      console.log('InsightsData updated:', {
        title: insightsData.title,
        videoId: insightsData.videoId,
        date: insightsData.publishedAt,
        source: (window as any).__fetchingYouTubeInsights ? 'direct insights fetch' : 'general data fetch'
      });
    }
  }, [insightsData]);

  // Add a function to handle YouTube reconnection
  const handleYouTubeReconnect = useCallback(() => {
    console.log('Showing YouTube reconnection modal');
    setShowYouTubeReconnectModal(true);
  }, []);

  return (
    <div className="space-y-6">
      {/* Header section */}
      <div className="border-b border-gray-200 pb-4 mb-6 flex flex-wrap gap-4 items-center justify-between">
        <h2 className="text-xl font-semibold text-gray-900">Social performance score <Info size={16} className="inline ml-1 text-gray-400 cursor-pointer" onClick={() => setShowScoreInfo(!showScoreInfo)} /></h2>
        
        <div className="flex items-center space-x-3">
          {/* Test YouTube analytics button - only show if YouTube is connected */}
          {isYouTubeConnected && youtubeChannel && (
            <button
              onClick={async () => {
                try {
                  toast({
                    title: 'Refreshing YouTube analytics data...',
                    description: 'Please wait while we fetch the latest data',
                  });
                  
                  // Directly use our fetchYouTubeAnalyticsData function
                  const data = await fetchYouTubeAnalyticsData();
                  
                  if (data) {
                    // Update the UI with the fetched data
                    setSocialScore(data.score);
                    setScoreChange(data.scoreChange);
                    setScoreHistory(data.history);
                    setRadarChartData(data.radarData);
                    setFactorsData(data.factors);
                    setInsightsData(data.insights);
                    setPerformanceData(data.performance);
                    setPostingData(data.posting);
                    
                    toast({
                      title: 'Analytics data refreshed',
                      description: 'Your YouTube analytics data has been updated',
                      variant: 'default',
                    });
                    setNeedsAnalyticsPermission(false);
                  } else {
                    // The fetchYouTubeAnalyticsData function already handles error toasts
                    console.log('Failed to refresh analytics data');
                  }
                } catch (error) {
                  console.error('Error refreshing YouTube analytics:', error);
                  toast({
                    title: 'Refresh error',
                    description: 'Could not refresh YouTube analytics data. Please try again later.',
                    variant: 'destructive',
                  });
                }
              }}
              className="px-3 py-1 text-xs bg-blue-50 hover:bg-blue-100 text-blue-600 rounded flex items-center"
            >
              <svg className="w-3 h-3 mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              Refresh Analytics
            </button>
          )}
          
          <Link href="/analyze/social-score/history" className="text-sm text-blue-600 hover:text-blue-800 transition-colors">
            Score history →
          </Link>
        </div>
      </div>
      
      {/* YouTube Analytics Permission Warning - show if needed and not dismissed */}
      {needsAnalyticsPermission && !analyticsPermissionDismissed && (
        <div className="mb-6">
          <YouTubeAnalyticsReconnection 
            onSuccess={() => {
              setNeedsAnalyticsPermission(false);
              toast({
                title: "Reconnection initiated",
                description: "Redirecting to Google for authentication...",
              });
            }}
            onCancel={() => {
              setAnalyticsPermissionDismissed(true);
              toast({
                title: "Analytics permissions skipped",
                description: "You can reconnect later from your account settings.",
              });
            }}
          />
        </div>
      )}

      {/* Filters row */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
        <div className="flex items-center space-x-4">
          <div className="relative" ref={datePickerRef}>
            <button
              onClick={() => setShowDatePicker(!showDatePicker)}
              className="flex items-center space-x-2 text-sm font-medium text-gray-700 hover:text-gray-900 bg-white border border-gray-300 rounded-md px-4 py-2 shadow-sm hover:bg-gray-50 transition-colors"
            >
              <Calendar size={16} />
              <span>{dateRange}</span>
              <ChevronDown size={16} />
            </button>
            
            {showDatePicker && (
              <div className="absolute mt-2 w-56 bg-white border border-gray-200 rounded-lg shadow-lg z-20">
                {['Weekly overview', 'Last 30 days', 'Last 90 days', 'Last 6 months', 'Last one year'].map((option) => (
                  <button
                    key={option}
                    onClick={() => {
                      setDateRange(option);
                      setShowDatePicker(false);
                    }}
                    className={`block w-full text-left px-4 py-3 text-sm hover:bg-gray-50 transition-colors ${dateRange === option ? 'text-blue-600 font-medium' : 'text-gray-700'}`}
                  >
                    {option}
                  </button>
                ))}
              </div>
            )}
          </div>
          
          <div className="relative" ref={channelSelectorRef}>
            <button
              onClick={() => setShowChannelSelector(!showChannelSelector)}
              className="flex items-center space-x-2 text-sm font-medium text-gray-700 hover:text-gray-900 bg-white border border-gray-300 rounded-md px-4 py-2 shadow-sm hover:bg-gray-50 transition-colors"
            >
              {/* Show YouTube thumbnail in the header if available */}
              {selectedPlatform === 'youtube' && youtubeChannel?.thumbnail ? (
                <div className="w-5 h-5 rounded-full overflow-hidden flex items-center justify-center">
                  <Image
                    src={youtubeChannel.thumbnail}
                    alt={selectedChannel}
                    width={20}
                    height={20}
                    className="object-cover"
                  />
                </div>
              ) : (
                <div className={`w-5 h-5 rounded-full ${getPlatformColor(selectedPlatform)} flex items-center justify-center`}>
                  <PlatformIcon platform={selectedPlatform} size={12} className="text-white" />
                </div>
              )}
              <span>{selectedChannel || 'All channels'}</span>
              <ChevronDown size={16} />
            </button>
            
            {showChannelSelector && (
              <div className="absolute mt-2 w-72 bg-white border border-gray-200 rounded-lg shadow-lg z-20">
                {hasConnectedPlatforms ? (
                  channelsList.map((channel) => (
                    <button
                      key={`${channel.platform}-${channel.name}`}
                      onClick={() => {
                        setSelectedChannel(channel.name);
                        setSelectedPlatform(channel.platform);
                        setShowChannelSelector(false);
                      }}
                      className={`flex items-center w-full text-left px-4 py-3 text-sm hover:bg-gray-50 transition-colors ${selectedChannel === channel.name ? 'text-blue-600 font-medium' : 'text-gray-700'}`}
                    >
                      {/* Display YouTube channel thumbnail if available */}
                      {channel.platform === 'youtube' && youtubeChannel?.thumbnail ? (
                        <div className="w-6 h-6 rounded-full overflow-hidden flex items-center justify-center mr-3">
                          <Image
                            src={youtubeChannel.thumbnail}
                            alt={channel.name}
                            width={24}
                            height={24}
                            className="object-cover"
                          />
                        </div>
                      ) : (
                        <div className={`w-6 h-6 rounded-full ${getPlatformColor(channel.platform)} flex items-center justify-center mr-3`}>
                          <PlatformIcon platform={channel.platform} size={14} className="text-white" />
                        </div>
                      )}
                      <div className="flex flex-col flex-1">
                        <span>{channel.name}</span>
                        {channel.platform === 'youtube' && youtubeChannel && (
                          <span className="text-xs text-gray-500 mt-0.5">
                            {youtubeChannel.customUrl || 'YouTube Channel'}
                          </span>
                        )}
                      </div>
                      {selectedChannel === channel.name && (
                        <CheckCircle size={16} className="text-blue-600 ml-2" />
                      )}
                    </button>
                  ))
                ) : (
                  <div className="px-4 py-3 text-sm text-gray-500">No connected accounts</div>
                )}
                <div className="border-t border-gray-100 mt-1 pt-1">
                  <Link
                    href="/settings/integrations"
                    className="w-full text-left px-4 py-3 text-sm text-blue-600 hover:bg-gray-50 transition-colors flex items-center"
                  >
                    <div className="w-6 h-6 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 mr-3">
                      <span>+</span>
                    </div>
                    <span>Connect another account</span>
                  </Link>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      <p className="text-sm text-gray-500 mb-6 md:hidden">{dateRangeText}</p>

      {/* Social Score Card */}
      <div className="bg-white border border-gray-200 rounded-xl p-6 shadow-sm hover:shadow-md transition-all">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-2">
            <h2 className="text-xl font-semibold text-gray-900">Social performance score</h2>
            <div className="relative" ref={scoreInfoRef}>
              <button 
                onClick={() => setShowScoreInfo(!showScoreInfo)}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <Info size={18} />
              </button>
              
              {showScoreInfo && (
                <div className="absolute left-0 mt-2 w-80 p-4 bg-white border border-gray-200 rounded-lg shadow-lg z-20 text-sm">
                  <p className="font-semibold mb-2 text-gray-900">About Social Score</p>
                  <p className="text-gray-600 mb-3 leading-relaxed">
                    Your social score is calculated based on engagement, reach, consistency, and growth across all your connected platforms.
                  </p>
                  <div className="border p-3 rounded-lg mb-3 bg-gray-50">
                    <div className="mb-2 font-medium text-gray-900">Score Ranges:</div>
                    <div className="flex flex-col space-y-3">
                      <div className="p-2 bg-white rounded-md border border-gray-100">
                        <div className="font-medium text-gray-900">800-1,000</div>
                        <div className="text-green-600 flex items-center">
                          <CheckCircle size={14} className="mr-1" />
                          <span>Excellent job! Keep it up!</span>
                      </div>
                      </div>
                      <div className="p-2 bg-white rounded-md border border-gray-100">
                        <div className="font-medium text-gray-900">650-799</div>
                        <div className="text-blue-600 flex items-center">
                          <TrendingUp size={14} className="mr-1" />
                          <span>A solid score! Check our suggestions to improve.</span>
                      </div>
                      </div>
                      <div className="p-2 bg-white rounded-md border border-gray-100">
                        <div className="font-medium text-gray-900">500-649</div>
                        <div className="text-amber-600 flex items-center">
                          <Circle size={14} className="mr-1" />
                          <span>An OK result. Focus on the fundamentals.</span>
                    </div>
                  </div>
                      <div className="p-2 bg-white rounded-md border border-gray-100">
                        <div className="font-medium text-gray-900">0-499</div>
                        <div className="text-red-600 flex items-center">
                          <TrendingDown size={14} className="mr-1" />
                          <span>Bummer! Posting more often should boost your results.</span>
                        </div>
                      </div>
                    </div>
                  </div>
                  <p className="text-gray-600 leading-relaxed">
                    A score above 750 is considered excellent, 500-750 is good, and below 500 needs improvement.
                  </p>
                </div>
              )}
            </div>
          </div>
          
          <Link href="/analyze/social-score/history" className="text-sm text-blue-600 hover:text-blue-700 hover:underline flex items-center transition-colors">
            <span>Score history</span>
            <ArrowRight size={14} className="ml-1" />
          </Link>
        </div>
        
        <div className="flex items-baseline space-x-3 mb-3">
          <div className="text-5xl font-bold text-gray-900">{socialScore}</div>
          <div className={`flex items-center ${scoreChange >= 0 ? 'text-green-600' : 'text-red-600'} font-medium`}>
            {scoreChange >= 0 ? (
              <ArrowUp size={18} className="mr-1" />
            ) : (
              <ArrowDown size={18} className="mr-1" />
            )}
            <span>{scoreChange >= 0 ? '+' : ''}{scoreChange}%</span>
          </div>
        </div>
        
        <div className="text-sm text-gray-600 mb-8 bg-gray-50 p-3 rounded-lg border border-gray-100">
          {socialScore >= 800 ? (
            <div className="flex items-start">
              <div className="bg-green-100 p-1 rounded-full mr-2 mt-0.5">
                <CheckCircle size={14} className="text-green-600" />
              </div>
              <div>
                <span className="font-medium text-gray-900">Doing great!</span><br />
              Over the past few months, your score has consistently improved, and it&apos;s even higher than your average earlier this year!
              </div>
            </div>
          ) : socialScore >= 650 ? (
            <div className="flex items-start">
              <div className="bg-blue-100 p-1 rounded-full mr-2 mt-0.5">
                <TrendingUp size={14} className="text-blue-600" />
              </div>
              <div>
                <span className="font-medium text-gray-900">Good job!</span><br />
              Your social performance is solid. Check out our recommendations below to push your score even higher.
              </div>
            </div>
          ) : socialScore >= 500 ? (
            <div className="flex items-start">
              <div className="bg-amber-100 p-1 rounded-full mr-2 mt-0.5">
                <Circle size={14} className="text-amber-600" />
              </div>
              <div>
                <span className="font-medium text-gray-900">Making progress!</span><br />
              Your social performance is acceptable, but there&apos;s room for improvement. Focus on the fundamentals.
              </div>
            </div>
          ) : (
            <div className="flex items-start">
              <div className="bg-red-100 p-1 rounded-full mr-2 mt-0.5">
                <TrendingDown size={14} className="text-red-600" />
              </div>
              <div>
                <span className="font-medium text-gray-900">Let&apos;s improve this!</span><br />
              Your social performance needs attention. Posting more consistently and engaging with your audience will help boost your score.
              </div>
            </div>
          )}
        </div>
        
        {loading ? (
          <div className="h-60 mb-4 bg-gray-100 animate-pulse rounded-lg"></div>
        ) : (
          <div className="h-60 mb-4">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={scoreHistory} margin={{ top: 10, right: 20, bottom: 10, left: 10 }}>
                <defs>
                  <linearGradient id="scoreGradient" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="5%" stopColor="#4F46E5" stopOpacity={0.8}/>
                    <stop offset="95%" stopColor="#4F46E5" stopOpacity={0.1}/>
                  </linearGradient>
                </defs>
                <CartesianGrid strokeDasharray="3 3" vertical={false} stroke="#f0f0f0" />
                <XAxis 
                  dataKey="name" 
                  tickLine={false} 
                  axisLine={false} 
                  tick={{ fill: '#6B7280', fontSize: 12 }}
                  dy={10}
                />
                <YAxis 
                  domain={[0, 1000]} 
                  tickLine={false} 
                  axisLine={false} 
                  tick={{ fill: '#6B7280', fontSize: 12 }}
                  dx={-10}
                  tickCount={5}
                  tickFormatter={(value) => value === 0 ? '0' : value}
                />
                <Tooltip 
                  formatter={(value) => [`${value}`, 'Score']}
                  labelFormatter={(label) => `Date: ${label}`}
                  contentStyle={{ 
                    backgroundColor: 'white', 
                    borderRadius: '8px', 
                    border: '1px solid #e5e7eb',
                    boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)'
                  }}
                  itemStyle={{ color: '#4F46E5' }}
                  isAnimationActive={false}
                />
                <Area 
                  type="monotone" 
                  dataKey="score" 
                  stroke="#4F46E5" 
                  strokeWidth={3}
                  fillOpacity={1}
                  fill="url(#scoreGradient)"
                  isAnimationActive={false}
                />
                <ReferenceLine y={500} stroke="#FCD34D" strokeDasharray="3 3" />
                <ReferenceLine y={650} stroke="#60A5FA" strokeDasharray="3 3" />
                <ReferenceLine y={800} stroke="#34D399" strokeDasharray="3 3" />
              </LineChart>
            </ResponsiveContainer>
          </div>
        )}
      </div>
      
      {/* Factors Section */}
      <div className="bg-white border border-gray-200 rounded-xl p-6 shadow-sm hover:shadow-md transition-all mt-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-6">Factors influencing your score over the past 8 weeks</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {factorsData ? (
            <>
              <div className="bg-white border border-gray-100 rounded-xl p-5 shadow-sm hover:shadow transition-all">
                <div className="flex items-center space-x-3 mb-3">
                  <div className="w-10 h-10 rounded-full bg-blue-50 flex items-center justify-center text-blue-600">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M8 18L12 22L16 18" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      <path d="M12 2V22" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
            </div>
                  <span className="font-medium text-gray-900">Post Views</span>
                </div>
                <div className="pl-13">
                  <div className="text-3xl font-bold text-gray-900">{factorsData?.postViews?.value || 0}</div>
                  <div className={`flex items-center ${factorsData?.postViews?.change >= 0 ? 'text-green-600' : 'text-red-600'} text-sm mt-1`}>
                    {factorsData?.postViews?.change >= 0 ? (
                      <ArrowUp size={14} className="mr-1" />
                    ) : (
                      <ArrowDown size={14} className="mr-1" />
                    )}
                    <span>{factorsData?.postViews?.change >= 0 ? '+' : ''}{factorsData?.postViews?.change || 0}% from last period</span>
                  </div>
                </div>
              </div>
              
              <div className="bg-white border border-gray-100 rounded-xl p-5 shadow-sm hover:shadow transition-all">
                <div className="flex items-center space-x-3 mb-3">
                  <div className="w-10 h-10 rounded-full bg-indigo-50 flex items-center justify-center text-indigo-600">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M18 13V19C18 19.5304 17.7893 20.0391 17.4142 20.4142C17.0391 20.7893 16.5304 21 16 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V8C3 7.46957 3.21071 6.96086 3.58579 6.58579C3.96086 6.21071 4.46957 6 5 6H11" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      <path d="M15 3H21V9" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      <path d="M10 14L21 3" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                  </div>
                  <span className="font-medium text-gray-900">Post Clicks</span>
                </div>
                <div className="pl-13">
                  <div className="text-3xl font-bold text-gray-900">{factorsData?.postClicks?.value || 0}</div>
                  <div className={`flex items-center ${factorsData?.postClicks?.change >= 0 ? 'text-green-600' : 'text-red-600'} text-sm mt-1`}>
                    {factorsData?.postClicks?.change >= 0 ? (
                      <ArrowUp size={14} className="mr-1" />
                    ) : (
                      <ArrowDown size={14} className="mr-1" />
                    )}
                    <span>{factorsData?.postClicks?.change >= 0 ? '+' : ''}{factorsData?.postClicks?.change || 0}% from last period</span>
                  </div>
                </div>
              </div>
              
              <div className="bg-white border border-gray-100 rounded-xl p-5 shadow-sm hover:shadow transition-all">
                <div className="flex items-center space-x-3 mb-3">
                  <div className="w-10 h-10 rounded-full bg-purple-50 flex items-center justify-center text-purple-600">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M21 11.5C21.0034 12.8199 20.6951 14.1219 20.1 15.3C19.3944 16.7118 18.3098 17.8992 16.9674 18.7293C15.6251 19.5594 14.0782 19.9994 12.5 20C11.1801 20.0035 9.87812 19.6951 8.7 19.1L3 21L4.9 15.3C4.30493 14.1219 3.99656 12.8199 4 11.5C4.00061 9.92179 4.44061 8.37488 5.27072 7.03258C6.10083 5.69028 7.28825 4.6056 8.7 3.90003C9.87812 3.30496 11.1801 2.99659 12.5 3.00003H13C15.0843 3.11502 17.053 3.99479 18.5291 5.47089C20.0052 6.94699 20.885 8.91568 21 11V11.5Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                  </div>
                  <span className="font-medium text-gray-900">Post Interactions</span>
                </div>
                <div className="pl-13">
                  <div className="text-3xl font-bold text-gray-900">{factorsData?.postInteractions?.value || 0}</div>
                  <div className={`flex items-center ${factorsData?.postInteractions?.change >= 0 ? 'text-green-600' : 'text-red-600'} text-sm mt-1`}>
                    {factorsData?.postInteractions?.change >= 0 ? (
                      <ArrowUp size={14} className="mr-1" />
                    ) : (
                      <ArrowDown size={14} className="mr-1" />
                    )}
                    <span>{factorsData?.postInteractions?.change >= 0 ? '+' : ''}{factorsData?.postInteractions?.change || 0}% from last period</span>
                  </div>
                </div>
              </div>
              
              <div className="bg-white border border-gray-100 rounded-xl p-5 shadow-sm hover:shadow transition-all">
                <div className="flex items-center space-x-3 mb-3">
                  <div className="w-10 h-10 rounded-full bg-green-50 flex items-center justify-center text-green-600">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M4 12V20C4 20.5304 4.21071 21.0391 4.58579 21.4142C4.96086 21.7893 5.46957 22 6 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      <path d="M16 6L12 2L8 6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      <path d="M12 2V15" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                  </div>
                  <span className="font-medium text-gray-900">Post Shares</span>
                </div>
                <div className="pl-13">
                  <div className="text-3xl font-bold text-gray-900">{factorsData?.postShares?.value || 0}</div>
                  <div className={`flex items-center ${factorsData?.postShares?.change >= 0 ? 'text-green-600' : 'text-red-600'} text-sm mt-1`}>
                    {factorsData?.postShares?.change >= 0 ? (
                      <ArrowUp size={14} className="mr-1" />
                    ) : (
                      <ArrowDown size={14} className="mr-1" />
                    )}
                    <span>{factorsData?.postShares?.change >= 0 ? '+' : ''}{factorsData?.postShares?.change || 0}% from last period</span>
                  </div>
                </div>
              </div>
              
              <div className="bg-white border border-gray-100 rounded-xl p-5 shadow-sm hover:shadow transition-all">
                <div className="flex items-center space-x-3 mb-3">
                  <div className="w-10 h-10 rounded-full bg-pink-50 flex items-center justify-center text-pink-600">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M17 21V19C17 17.9391 16.5786 16.9217 15.8284 16.1716C15.0783 15.4214 14.0609 15 13 15H5C3.93913 15 2.92172 15.4214 2.17157 16.1716C1.42143 16.9217 1 17.9391 1 19V21" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      <path d="M9 11C11.2091 11 13 9.20914 13 7C13 4.79086 11.2091 3 9 3C6.79086 3 5 4.79086 5 7C5 9.20914 6.79086 11 9 11Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      <path d="M23 21V19C22.9993 18.1137 22.7044 17.2528 22.1614 16.5523C21.6184 15.8519 20.8581 15.3516 20 15.13" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      <path d="M16 3.13C16.8604 3.35031 17.623 3.85071 18.1676 4.55232C18.7122 5.25392 19.0078 6.11683 19.0078 7.005C19.0078 7.89318 18.7122 8.75608 18.1676 9.45769C17.623 10.1593 16.8604 10.6597 16 10.88" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                  </div>
                  <span className="font-medium text-gray-900">Followers</span>
                </div>
                <div className="pl-13">
                  <div className="text-3xl font-bold text-gray-900">{factorsData?.followers?.value || 0}</div>
                  <div className={`flex items-center ${factorsData?.followers?.change >= 0 ? 'text-green-600' : 'text-red-600'} text-sm mt-1`}>
                    {factorsData?.followers?.change >= 0 ? (
                      <ArrowUp size={14} className="mr-1" />
                    ) : (
                      <ArrowDown size={14} className="mr-1" />
                    )}
                    <span>{factorsData?.followers?.change >= 0 ? '+' : ''}{factorsData?.followers?.change || 0}% from last period</span>
                  </div>
                </div>
              </div>
              
              <div className="bg-white border border-gray-100 rounded-xl p-5 shadow-sm hover:shadow transition-all">
                <div className="flex items-center space-x-3 mb-3">
                  <div className="w-10 h-10 rounded-full bg-amber-50 flex items-center justify-center text-amber-600">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M21 15C21 15.5304 20.7893 16.0391 20.4142 16.4142C20.0391 16.7893 19.5304 17 19 17H7L3 21V5C3 4.46957 3.21071 3.96086 3.58579 3.58579C3.96086 3.21071 4.46957 3 5 3H19C19.5304 3 20.0391 3.21071 20.4142 3.58579C20.7893 3.96086 21 4.46957 21 5V15Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                  </div>
                  <span className="font-medium text-gray-900">Post Comments</span>
                </div>
                <div className="pl-13">
                  <div className="text-3xl font-bold text-gray-900">{factorsData?.postComments?.value || 0}</div>
                  <div className={`flex items-center ${factorsData?.postComments?.change >= 0 ? 'text-green-600' : 'text-red-600'} text-sm mt-1`}>
                    {factorsData?.postComments?.change >= 0 ? (
                      <ArrowUp size={14} className="mr-1" />
                    ) : (
                      <ArrowDown size={14} className="mr-1" />
                    )}
                    <span>{factorsData?.postComments?.change >= 0 ? '+' : ''}{factorsData?.postComments?.change || 0}% from last period</span>
                  </div>
                </div>
              </div>
            </>
          ) : (
            // Fallback to static data if API data is not available
            <>
              <div className="bg-white border border-gray-100 rounded-xl p-5 shadow-sm hover:shadow transition-all">
                <div className="flex items-center space-x-3 mb-3">
                  <div className="w-10 h-10 rounded-full bg-blue-50 flex items-center justify-center text-blue-600">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M8 18L12 22L16 18" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      <path d="M12 2V22" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                  </div>
                  <span className="font-medium text-gray-900">Post Views</span>
                </div>
                <div className="pl-13">
                  <div className="text-3xl font-bold text-gray-900">12.4k</div>
                  <div className="flex items-center text-green-600 text-sm mt-1">
                    <ArrowUp size={14} className="mr-1" />
                    <span>+18% from last period</span>
                  </div>
                </div>
              </div>
              
              <div className="bg-white border border-gray-100 rounded-xl p-5 shadow-sm hover:shadow transition-all">
                <div className="flex items-center space-x-3 mb-3">
                  <div className="w-10 h-10 rounded-full bg-indigo-50 flex items-center justify-center text-indigo-600">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M18 13V19C18 19.5304 17.7893 20.0391 17.4142 20.4142C17.0391 20.7893 16.5304 21 16 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V8C3 7.46957 3.21071 6.96086 3.58579 6.58579C3.96086 6.21071 4.46957 6 5 6H11" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      <path d="M15 3H21V9" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      <path d="M10 14L21 3" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                  </div>
                  <span className="font-medium text-gray-900">Post Clicks</span>
                </div>
                <div className="pl-13">
                  <div className="text-3xl font-bold text-gray-900">1.8k</div>
                  <div className="flex items-center text-green-600 text-sm mt-1">
                    <ArrowUp size={14} className="mr-1" />
                    <span>+12% from last period</span>
                  </div>
                </div>
              </div>
              
              <div className="bg-white border border-gray-100 rounded-xl p-5 shadow-sm hover:shadow transition-all">
                <div className="flex items-center space-x-3 mb-3">
                  <div className="w-10 h-10 rounded-full bg-purple-50 flex items-center justify-center text-purple-600">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M21 11.5C21.0034 12.8199 20.6951 14.1219 20.1 15.3C19.3944 16.7118 18.3098 17.8992 16.9674 18.7293C15.6251 19.5594 14.0782 19.9994 12.5 20C11.1801 20.0035 9.87812 19.6951 8.7 19.1L3 21L4.9 15.3C4.30493 14.1219 3.99656 12.8199 4 11.5C4.00061 9.92179 4.44061 8.37488 5.27072 7.03258C6.10083 5.69028 7.28825 4.6056 8.7 3.90003C9.87812 3.30496 11.1801 2.99659 12.5 3.00003H13C15.0843 3.11502 17.053 3.99479 18.5291 5.47089C20.0052 6.94699 20.885 8.91568 21 11V11.5Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                  </div>
                  <span className="font-medium text-gray-900">Post Interactions</span>
                </div>
                <div className="pl-13">
                  <div className="text-3xl font-bold text-gray-900">3.2k</div>
                  <div className="flex items-center text-green-600 text-sm mt-1">
                    <ArrowUp size={14} className="mr-1" />
                    <span>+24% from last period</span>
                  </div>
                </div>
              </div>
              
              <div className="bg-white border border-gray-100 rounded-xl p-5 shadow-sm hover:shadow transition-all">
                <div className="flex items-center space-x-3 mb-3">
                  <div className="w-10 h-10 rounded-full bg-green-50 flex items-center justify-center text-green-600">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M4 12V20C4 20.5304 4.21071 21.0391 4.58579 21.4142C4.96086 21.7893 5.46957 22 6 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      <path d="M16 6L12 2L8 6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      <path d="M12 2V15" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                  </div>
                  <span className="font-medium text-gray-900">Post Shares</span>
                </div>
                <div className="pl-13">
                  <div className="text-3xl font-bold text-gray-900">845</div>
                  <div className="flex items-center text-green-600 text-sm mt-1">
                    <ArrowUp size={14} className="mr-1" />
                    <span>+32% from last period</span>
                  </div>
                </div>
              </div>
              
              <div className="bg-white border border-gray-100 rounded-xl p-5 shadow-sm hover:shadow transition-all">
                <div className="flex items-center space-x-3 mb-3">
                  <div className="w-10 h-10 rounded-full bg-pink-50 flex items-center justify-center text-pink-600">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M17 21V19C17 17.9391 16.5786 16.9217 15.8284 16.1716C15.0783 15.4214 14.0609 15 13 15H5C3.93913 15 2.92172 15.4214 2.17157 16.1716C1.42143 16.9217 1 17.9391 1 19V21" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      <path d="M9 11C11.2091 11 13 9.20914 13 7C13 4.79086 11.2091 3 9 3C6.79086 3 5 4.79086 5 7C5 9.20914 6.79086 11 9 11Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      <path d="M23 21V19C22.9993 18.1137 22.7044 17.2528 22.1614 16.5523C21.6184 15.8519 20.8581 15.3516 20 15.13" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      <path d="M16 3.13C16.8604 3.35031 17.623 3.85071 18.1676 4.55232C18.7122 5.25392 19.0078 6.11683 19.0078 7.005C19.0078 7.89318 18.7122 8.75608 18.1676 9.45769C17.623 10.1593 16.8604 10.6597 16 10.88" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                  </div>
                  <span className="font-medium text-gray-900">Followers</span>
                </div>
                <div className="pl-13">
                  <div className="text-3xl font-bold text-gray-900">2.4k</div>
                  <div className="text-3xl font-bold text-gray-900">{factorsData?.followers?.value || 0}</div>
                  <div className={`flex items-center ${factorsData?.followers?.change >= 0 ? 'text-green-600' : 'text-red-600'} text-sm mt-1`}>
                    {factorsData?.followers?.change >= 0 ? (
                      <ArrowUp size={14} className="mr-1" />
                    ) : (
                      <ArrowDown size={14} className="mr-1" />
                    )}
                    <span>{factorsData?.followers?.change >= 0 ? '+' : ''}{factorsData?.followers?.change || 0}% from last period</span>
                  </div>
                </div>
              </div>
              
              <div className="bg-white border border-gray-100 rounded-xl p-5 shadow-sm hover:shadow transition-all">
                <div className="flex items-center space-x-3 mb-3">
                  <div className="w-10 h-10 rounded-full bg-amber-50 flex items-center justify-center text-amber-600">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M21 15C21 15.5304 20.7893 16.0391 20.4142 16.4142C20.0391 16.7893 19.5304 17 19 17H7L3 21V5C3 4.46957 3.21071 3.96086 3.58579 3.58579C3.96086 3.21071 4.46957 3 5 3H19C19.5304 3 20.0391 3.21071 20.4142 3.58579C20.7893 3.96086 21 4.46957 21 5V15Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                  </div>
                  <span className="font-medium text-gray-900">Post Comments</span>
                </div>
                <div className="pl-13">
                  <div className="text-3xl font-bold text-gray-900">{factorsData?.postComments?.value || 0}</div>
                  <div className={`flex items-center ${factorsData?.postComments?.change >= 0 ? 'text-green-600' : 'text-red-600'} text-sm mt-1`}>
                    {factorsData?.postComments?.change >= 0 ? (
                      <ArrowUp size={14} className="mr-1" />
                    ) : (
                      <ArrowDown size={14} className="mr-1" />
                    )}
                    <span>{factorsData?.postComments?.change >= 0 ? '+' : ''}{factorsData?.postComments?.change || 0}% from last period</span>
                  </div>
                </div>
              </div>
            </>
          )}
        </div>
      </div>
      
      {/* Recommendations section - similar to reference image */}
      <div className="mt-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-6">Recommendations to grow your brand</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="bg-white border border-gray-200 rounded-xl p-6 shadow-sm hover:shadow-md transition-all">
            <div className="flex items-start mb-4">
              <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 mr-3 flex-shrink-0">
                <Check size={16} />
              </div>
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">Visual Content Enhancement</h3>
                <p className="text-gray-700 text-sm">
                  Your posts that feature images or videos are crucial for engagement. Start incorporating high-quality visuals that reflect your brand&apos;s personality and enhance your content.
                </p>
              </div>
            </div>
          </div>
          
          <div className="bg-white border border-gray-200 rounded-xl p-6 shadow-sm hover:shadow-md transition-all">
            <div className="flex items-start mb-4">
              <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 mr-3 flex-shrink-0">
                <Check size={16} />
              </div>
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">Experiment with Post Timing</h3>
                <p className="text-gray-700 text-sm">
                  Your audience is most active throughout the week, but try publishing when your audience is most active. Timing can significantly impact engagement rates.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Performance Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
        <div className="bg-white border border-gray-200 rounded-xl p-6 shadow-sm hover:shadow-md transition-all">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-gray-900">Total engagements</h3>
            <button className="text-gray-400 hover:text-gray-600 transition-colors">
              <Info size={18} />
            </button>
          </div>
          
          <div className="mb-4">
            <div className="text-4xl font-bold text-gray-900">{performanceData?.engagements?.total || 0}</div>
            <div className={`flex items-center ${(performanceData?.engagements?.change || 0) >= 0 ? 'text-green-600' : 'text-red-600'} text-sm mt-1`}>
              {(performanceData?.engagements?.change || 0) >= 0 ? (
                <ArrowUp size={16} className="mr-1" />
              ) : (
                <ArrowDown size={16} className="mr-1" />
              )}
              <span>{(performanceData?.engagements?.change || 0) >= 0 ? '+' : ''}{performanceData?.engagements?.change || 0}% from last week</span>
            </div>
          </div>
          
          <div className="h-32 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg flex items-center justify-center text-gray-500 text-sm mb-4">
            <div className="flex flex-col items-center">
              <BarChart size={24} className="mb-2 text-blue-500" />
              <span>Engagement trend chart</span>
            </div>
          </div>
          
          <div className="mt-4 text-sm text-gray-600 bg-gray-50 p-3 rounded-lg border border-gray-100 mb-6">
            Your total engagement reached {performanceData?.engagements?.total || 0}, averaging to {Math.round((performanceData?.engagements?.total || 0) / (postingData?.frequency?.total || 1))} interactions per post. That&apos;s fantastic engagement, especially for your content volume!
          </div>
          
          <div className="grid grid-cols-3 gap-4">
            <div className="bg-white p-3 rounded-lg border border-gray-100 shadow-sm">
              <div className="text-sm text-gray-500 mb-1">Reactions</div>
              <div className="font-semibold text-xl text-gray-900">{performanceData?.engagements?.reactions || 0}</div>
            </div>
            <div className="bg-white p-3 rounded-lg border border-gray-100 shadow-sm">
              <div className="text-sm text-gray-500 mb-1">Comments</div>
              <div className="font-semibold text-xl text-gray-900">{performanceData?.engagements?.comments || 0}</div>
            </div>
            <div className="bg-white p-3 rounded-lg border border-gray-100 shadow-sm">
              <div className="text-sm text-gray-500 mb-1">Shares</div>
              <div className="font-semibold text-xl text-gray-900">{performanceData?.engagements?.shares || 0}</div>
            </div>
          </div>
        </div>
        
        <div className="bg-white border border-gray-200 rounded-xl p-6 shadow-sm hover:shadow-md transition-all">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-gray-900">Impressions</h3>
            <button className="text-gray-400 hover:text-gray-600 transition-colors">
              <Info size={18} />
            </button>
          </div>
          
          <div className="mb-4">
            <div className="text-4xl font-bold text-gray-900">{performanceData?.impressions?.total || 0}</div>
            <div className={`flex items-center ${(performanceData?.impressions?.change || 0) >= 0 ? 'text-green-600' : 'text-red-600'} text-sm mt-1`}>
              {(performanceData?.impressions?.change || 0) > 0 ? (
                <>
                  <ArrowUp size={16} className="mr-1" />
                  <span>+{performanceData?.impressions?.change}% from last week</span>
                </>
              ) : (performanceData?.impressions?.change || 0) < 0 ? (
                <>
                  <ArrowDown size={16} className="mr-1" />
                  <span>{performanceData?.impressions?.change}% from last week</span>
                </>
              ) : (
                <span>No change from last week</span>
              )}
            </div>
          </div>
          
          <div className="h-32 bg-gray-50 rounded-lg flex items-center justify-center text-gray-500 text-sm mb-4">
            {(performanceData?.impressions?.total || 0) > 0 ? (
              <div className="flex flex-col items-center">
                <BarChart size={24} className="mb-2 text-blue-500" />
                <span>Impressions trend chart</span>
              </div>
            ) : (
              <div className="flex flex-col items-center">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="mb-2 text-gray-400">
                  <path d="M10 14L12 12M12 12L14 10M12 12L10 10M12 12L14 14M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
                <span>No impression data available</span>
              </div>
            )}
          </div>
          
          <div className="mt-4 text-sm text-gray-600 bg-gray-50 p-3 rounded-lg border border-gray-100 mb-6">
            {(performanceData?.impressions?.total || 0) > 0 ? (
              `Your content received ${performanceData?.impressions?.total} impressions this week. Great visibility!`
            ) : (
              `It seems there was no recorded impressions this week. Let's focus on boosting visibility for your posts!`
            )}
          </div>
        </div>
      </div>
      
      {/* Performance Breakdown */}
      <div className="bg-white border border-gray-200 rounded-xl p-6 shadow-sm hover:shadow-md transition-all mt-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-6">Performance Breakdown</h2>
        <div className="bg-gradient-to-br from-gray-50 to-blue-50 rounded-lg p-4">
        <ResponsiveContainer width="100%" height={400}>
            <RadarChart outerRadius={150} data={radarChartData}>
              <PolarGrid stroke="#e5e7eb" />
              <PolarAngleAxis dataKey="subject" tick={{ fill: '#4B5563', fontSize: 12 }} />
              <PolarRadiusAxis angle={30} domain={[0, 100]} tick={{ fill: '#4B5563', fontSize: 12 }} />
              <Radar name="Your Score" dataKey="A" stroke="#4F46E5" fill="#4F46E5" fillOpacity={0.6} isAnimationActive={false} />
              <Radar name="Industry Average" dataKey="fullMark" stroke="#10B981" fill="#10B981" fillOpacity={0.4} isAnimationActive={false} />
              <Tooltip 
                contentStyle={{ 
                  backgroundColor: 'white', 
                  borderRadius: '8px', 
                  border: '1px solid #e5e7eb',
                  boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)'
                }}
                isAnimationActive={false}
              />
          </RadarChart>
        </ResponsiveContainer>
        </div>
      </div>

      {/* Add debug buttons (after line with "Permission warning dismissed") */}
      <div className="mt-2 flex gap-2">
        <button
          onClick={() => {
            // Force fallback video ID if current one isn't working
            const fallbackVideoId = 'dQw4w9WgXcQ'; // This is a well-known video that should always have thumbnails
            videoIdRef.current = fallbackVideoId;
            setInsightsData((prevData: any) => ({
              ...prevData,
              videoId: fallbackVideoId,
              title: 'Fallback Video (Debug Mode)'
            }));
          }}
          className="px-2 py-1 bg-orange-600 text-white rounded text-xs"
        >
          Use Fallback Video
        </button>
        <button
          onClick={() => {
            const currentVideoId = insightsData?.videoId || videoIdRef.current;
            if (currentVideoId) {
              // Force refresh by clearing and setting new data with same ID
              setInsightsData((prevData: any) => ({
                ...prevData,
                videoId: null
              }));
              // Set timeout to avoid React batch updates
              setTimeout(() => {
                setInsightsData((prevData: any) => ({
                  ...prevData,
                  videoId: currentVideoId
                }));
              }, 100);
            }
          }}
          className="px-2 py-1 bg-blue-600 text-white rounded text-xs"
        >
          Refresh Thumbnail
        </button>
        <button
          onClick={() => {
            // Show info about the video ID being used
            const videoId = insightsData?.videoId || videoIdRef.current || 'dQw4w9WgXcQ';
            console.log('Using video ID:', videoId);
            console.log('Using YouTubeThumbnail component with improved error handling');
          }}
          className="px-2 py-1 bg-green-600 text-white rounded text-xs"
        >
          Test Thumbnail
        </button>
        <button
          onClick={() => {
            setNeedsAnalyticsPermission(!needsAnalyticsPermission);
          }}
          className="px-2 py-1 bg-purple-600 text-white rounded text-xs"
        >
          Toggle Analytics Warning
        </button>
        <button
          onClick={() => {
            setAnalyticsPermissionDismissed(false);
            setNeedsAnalyticsPermission(true);
          }}
          className="px-2 py-1 bg-indigo-600 text-white rounded text-xs"
        >
          Show Analytics Warning
        </button>
      </div>
      
      {/* YouTube Insights Section - Shows the most engaging YouTube content */}
      
      {/* Posting Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
        <div className="bg-white border border-gray-200 rounded-xl p-6 shadow-sm hover:shadow-md transition-all">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-gray-900">Posting frequency</h3>
          </div>
          
          <div className="mb-4">
            <div className="text-4xl font-bold text-gray-900">{postingData?.frequency?.total || 0}</div>
            <div className={`flex items-center ${(postingData?.frequency?.change || 0) >= 0 ? 'text-green-600' : 'text-red-600'} text-sm mt-1`}>
              {(postingData?.frequency?.change || 0) > 0 ? (
                <>
                  <ArrowUp size={16} className="mr-1" />
                  <span>+{postingData?.frequency?.change}% from last week</span>
                </>
              ) : (postingData?.frequency?.change || 0) < 0 ? (
                <>
                  <ArrowDown size={16} className="mr-1" />
                  <span>{postingData?.frequency?.change}% from last week</span>
                </>
              ) : (
                <span>No change from last week</span>
              )}
            </div>
          </div>
          
          <div className="h-32 bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg flex items-center justify-center text-gray-500 text-sm mb-4">
            <div className="flex flex-col items-center">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="mb-2 text-green-500">
                <path d="M8 7V3M16 7V3M7 11H17M5 21H19C20.1046 21 21 20.1046 21 19V7C21 5.89543 20.1046 5 19 5H5C3.89543 5 3 5.89543 3 7V19C3 20.1046 3.89543 21 5 21Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
              <span>Posting frequency chart</span>
            </div>
          </div>
          
          <div className="mt-4 text-sm text-gray-600 bg-gray-50 p-3 rounded-lg border border-gray-100">
            {(postingData?.frequency?.total || 0) > 0 ? 
              `You posted ${postingData?.frequency?.total} ${postingData?.frequency?.total === 1 ? 'time' : 'times'} this week, which is ${(postingData?.frequency?.total || 0) >= 3 ? 'right within' : 'below'} the recommended range. ${(postingData?.frequency?.total || 0) >= 3 ? 'Keep up the consistency!' : 'Try to post more frequently for better engagement.'}`
              : 'You haven\'t posted this week. Consistent posting is key to maintaining audience engagement and growth.'}
          </div>
        </div>
        
        <div className="bg-white border border-gray-200 rounded-xl p-6 shadow-sm hover:shadow-md transition-all">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-gray-900">Follower growth</h3>
          </div>
          
          <div className="mb-4">
            <div className="text-4xl font-bold text-gray-900">{postingData?.followerGrowth?.total || 0}</div>
            <div className={`flex items-center ${(postingData?.followerGrowth?.change || 0) >= 0 ? 'text-green-600' : 'text-red-600'} text-sm mt-1`}>
              {(postingData?.followerGrowth?.change || 0) > 0 ? (
                <>
                  <ArrowUp size={16} className="mr-1" />
                  <span>+{postingData?.followerGrowth?.change}% from last week</span>
                </>
              ) : (postingData?.followerGrowth?.change || 0) < 0 ? (
                <>
                  <ArrowDown size={16} className="mr-1" />
                  <span>{postingData?.followerGrowth?.change}% from last week</span>
                </>
              ) : (
                <span>No change from last week</span>
              )}
            </div>
          </div>
          
          <div className="h-32 bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg flex items-center justify-center text-gray-500 text-sm mb-4">
            <div className="flex flex-col items-center">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="mb-2 text-purple-500">
                <path d="M16 21V19C16 17.9391 15.5786 16.9217 14.8284 16.1716C14.0783 15.4214 13.0609 15 12 15H5C3.93913 15 2.92172 15.4214 2.17157 16.1716C1.42143 16.9217 1 17.9391 1 19V21M17 7C17 9.20914 15.2091 11 13 11C10.7909 11 9 9.20914 9 7C9 4.79086 10.7909 3 13 3C15.2091 3 17 4.79086 17 7ZM23 21V19C22.9986 18.1137 22.7022 17.2528 22.1496 16.5523C21.597 15.8519 20.8209 15.3516 19.9487 15.1312M16.9187 3.13835C17.8021 3.35764 18.5787 3.85959 19.1318 4.56156C19.6849 5.26352 19.9812 6.12565 19.9812 7.01464C19.9812 7.89318 19.6849 8.75608 19.1318 9.45769C18.5787 10.1593 17.8021 10.6597 16.9187 10.8909" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
              <span>Follower growth chart</span>
            </div>
          </div>
          
          <div className="mt-4 text-sm text-gray-600 bg-gray-50 p-3 rounded-lg border border-gray-100">
            {(postingData?.followerGrowth?.total || 0) > 0 
              ? `This week saw an increase of ${postingData?.followerGrowth?.total} ${postingData?.followerGrowth?.total === 1 ? 'follower' : 'followers'}! Well done! To nurture these new relationships, interact with active accounts, show appreciation by responding to comments, liking their posts, and offering thoughtful, personalized responses.`
              : (postingData?.followerGrowth?.total || 0) < 0 
              ? `This week your channel lost ${Math.abs(postingData?.followerGrowth?.total || 0)} ${Math.abs(postingData?.followerGrowth?.total || 0) === 1 ? 'follower' : 'followers'}. Consider reviewing your content strategy and engagement approach to reconnect with your audience.`
              : 'Your follower count remained stable this week. To stimulate growth, try engaging more with your community and posting content that encourages sharing.'}
          </div>
        </div>
      </div>
      
      {/* YouTube Reconnection Modal */}
      {showYouTubeReconnectModal && (
        <YouTubeReconnectionModal
          onClose={() => setShowYouTubeReconnectModal(false)}
          returnTo={`/analyze/social-score`}
        />
      )}
    </div>
  )
}

// Missing component definition
const Clock = ({ size = 24, className = "" }: { size?: number, className?: string }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      className={className}
    >
      <circle cx="12" cy="12" r="10" />
      <polyline points="12 6 12 12 16 14" />
    </svg>
  );
}; 