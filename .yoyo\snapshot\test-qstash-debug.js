require('dotenv').config();
const https = require('https');

console.log('=== QStash Debug Test ===');

// Load the token from .env
const fs = require('fs');
const path = require('path');
const envPath = path.resolve('.env');
let envVars = {};

if (fs.existsSync(envPath)) {
  console.log(`Found .env file at: ${envPath}`);
  const envContent = fs.readFileSync(envPath, 'utf8');
  
  // Parse .env file manually
  envContent.split('\n').forEach(line => {
    if (line.trim() && !line.startsWith('#')) {
      const match = line.match(/^\s*([\w.-]+)\s*=\s*"?([^"]*)"?\s*$/);
      if (match) {
        const key = match[1];
        const value = match[2].trim();
        envVars[key] = value;
      }
    }
  });
  
  console.log('Parsed environment variables:');
  Object.keys(envVars).forEach(key => {
    if (key.startsWith('QSTASH') || key === 'NGROK_URL') {
      const displayValue = key.includes('TOKEN') || key.includes('KEY') 
        ? `${envVars[key].substring(0, 10)}...` 
        : envVars[key];
      console.log(`- ${key}: ${displayValue}`);
    }
  });
}

// Get token from various sources
let qstashToken = process.env.QSTASH_TOKEN || envVars.QSTASH_TOKEN;
console.log(`\nToken from process.env: ${process.env.QSTASH_TOKEN ? 'Found (length: ' + process.env.QSTASH_TOKEN.length + ')' : 'Not found'}`);
console.log(`Token from parsed .env: ${envVars.QSTASH_TOKEN ? 'Found (length: ' + envVars.QSTASH_TOKEN.length + ')' : 'Not found'}`);
console.log(`Final token used: ${qstashToken ? 'Found (length: ' + qstashToken.length + ')' : 'Not found'}`);

if (!qstashToken) {
  console.error('QSTASH_TOKEN not found from any source. Please check your .env file.');
  process.exit(1);
}

// Get ngrok URL
let ngrokUrl = process.env.NGROK_URL || envVars.NGROK_URL;
console.log(`\nNGROK_URL from process.env: ${process.env.NGROK_URL || 'Not found'}`);
console.log(`NGROK_URL from parsed .env: ${envVars.NGROK_URL || 'Not found'}`);
console.log(`Final NGROK_URL used: ${ngrokUrl}`);

if (!ngrokUrl) {
  console.error('NGROK_URL not found from any source. Please check your .env file.');
  process.exit(1);
}

// Clean up the ngrok URL
console.log(`\nOriginal NGROK_URL: ${ngrokUrl}`);

// Remove quotes if they exist
ngrokUrl = ngrokUrl.replace(/^"|"$/g, '');
console.log(`After removing quotes: ${ngrokUrl}`);

// Remove trailing slash if present
if (ngrokUrl.endsWith('/')) {
  ngrokUrl = ngrokUrl.slice(0, -1);
  console.log(`After removing trailing slash: ${ngrokUrl}`);
}

// Ensure ngrokUrl starts with https:// or http://
if (!ngrokUrl.startsWith('http://') && !ngrokUrl.startsWith('https://')) {
  ngrokUrl = 'https://' + ngrokUrl;
  console.log(`After ensuring https prefix: ${ngrokUrl}`);
}

console.log(`\nFinal NGROK_URL: ${ngrokUrl}`);

// Create test post ID
const postId = 'cf43776a-54bd-4bfd-bec4-b6f230463edd';
console.log(`\nTest post ID: ${postId}`);

// Create destination URL
const destinationUrl = `${ngrokUrl}/api/posts/publish-callback`;
console.log(`Destination URL: ${destinationUrl}`);

// Create and log the exact request we'll send
const requestOptions = {
  hostname: 'qstash.upstash.io',
  port: 443,
  path: '/v2/publish',
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${qstashToken}`,
    'Upstash-Forward-Url': destinationUrl
  }
};

console.log('\nRequest details:');
console.log(`URL: https://${requestOptions.hostname}${requestOptions.path}`);
console.log('Headers:');
Object.keys(requestOptions.headers).forEach(key => {
  if (key === 'Authorization') {
    console.log(`- ${key}: Bearer ${qstashToken.substring(0, 10)}...`);
  } else {
    console.log(`- ${key}: ${requestOptions.headers[key]}`);
  }
});

// Create the message payload
const postData = JSON.stringify({
  postId: postId,
  timestamp: new Date().toISOString()
});

console.log(`\nPayload: ${postData}`);

console.log('\nSending request to QStash...');

// Send the request
const req = https.request(requestOptions, (res) => {
  console.log(`\nResponse status code: ${res.statusCode}`);
  console.log('Response headers:');
  Object.keys(res.headers).forEach(key => {
    console.log(`- ${key}: ${res.headers[key]}`);
  });
  
  let responseData = '';
  res.on('data', (chunk) => {
    responseData += chunk;
  });
  
  res.on('end', () => {
    console.log(`\nResponse body: ${responseData}`);
    try {
      const jsonResponse = JSON.parse(responseData);
      console.log('Parsed JSON response:');
      console.log(JSON.stringify(jsonResponse, null, 2));
    } catch (e) {
      console.log('Response is not valid JSON');
    }
    
    if (res.statusCode === 200 || res.statusCode === 201) {
      console.log('\nMessage published successfully to QStash!');
    } else {
      console.log('\nFailed to publish message to QStash.');
    }
  });
});

req.on('error', (error) => {
  console.error(`\nRequest error: ${error.message}`);
});

// Send the request
console.log('\nSending the request...');
req.write(postData);
req.end(); 