'use client'

import React, { useState, useRef, useEffect, Fragment, useMemo, useCallback } from 'react'
import { Calendar, ChevronDown, Info, ArrowRight, HelpCircle, ArrowDown, ArrowUp, Download, RefreshCw, BarChart as Lucide<PERSON>ar<PERSON><PERSON>, <PERSON>, TrendingUp, Users, MousePointer, Eye, AlertCircle, Youtube, AlertTriangle } from 'lucide-react'
import AnalyzeLayout from '@/components/layout/AnalyzeLayout'
import Link from 'next/link'
import ErrorBoundary from '@/components/ErrorBoundary'
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Cell, Legend } from 'recharts'
import { useDemoState } from '@/contexts/DemoStateContext'
import { usePlatform } from '@/contexts/PlatformContext'
import { useYouTubeChannel } from '@/lib/hooks/useYouTubeChannel'
import Image from 'next/image'

// Types for our analytics data
interface BestTimeAnalyticsData {
  heatmapData: HeatmapData;
  dailyPerformance?: {
    [day: string]: {
      views: number;
      engagement: number;
      watchTime: number;
      count: number;
    }
  };
  metrics: {
    totalReach: { value: string; change: string; isPositive: boolean };
    engagementRate: { value: string; change: string; isPositive: boolean };
    avgEngagement: { value: string; change: string; isPositive: boolean };
    lastUpdated: string;
  };
  goalContent: Record<string, GoalContent>;
  message?: string; // Optional message from API
}

// Goals data
const goals = [
  { 
    id: 'reach', 
    name: 'Reach', 
    active: false,
    title: 'Maximize Reach',
    description: 'Get your content in front of more people'
  },
  { 
    id: 'awareness', 
    name: 'Awareness', 
    active: false,
    title: 'Build Awareness',
    description: 'Increase brand recognition and visibility'
  },
  { 
    id: 'engagement', 
    name: 'Engagement', 
    active: true,
    title: 'Boost Engagement',
    description: 'Get more likes, comments and shares'
  },
  { 
    id: 'traffic', 
    name: 'Traffic', 
    active: false,
    title: 'Drive Traffic',
    description: 'Send more visitors to your website'
  }
]

// Define types for heatmap data
interface HeatmapData {
  [day: string]: {
    [hour: string]: number;
  };
}

// Fix the type for the heatmap data item
interface HeatmapDataItem {
  day: string;
  hour: string;
  value: number;
}

// Create a synchronized data generation system
const generateHeatmapData = (): HeatmapData => {
  const days = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
  const hours = Array.from({ length: 24 }, (_, i) => i.toString().padStart(2, '0'));
  
  const data: HeatmapData = {};
  
  // Initialize all days with all hours, but mostly empty values
  days.forEach(day => {
    data[day] = {};
    hours.forEach(hour => {
      // Default to zero or very low values for most cells
      data[day][hour] = 0;
    });
  });
  
  // Primary hotspots based on screenshots
  data['Wed']['23'] = 5.0; // Strongest hotspot - Wednesday at 11pm
  data['Wed']['18'] = 4.8; // Second strongest - Wednesday at 6pm
  data['Mon']['15'] = 4.5; // Third strongest - Monday at 3pm
  
  // Secondary hotspots
  data['Wed']['12'] = 3.8; // Wednesday at noon
  data['Mon']['17'] = 3.5; // Monday at 5pm
  data['Sat']['12'] = 3.2; // Saturday at noon
  
  // Add some lower-value data points to make the heatmap look realistic
  data['Tue']['09'] = 2.1;
  data['Thu']['09'] = 1.8;
  data['Fri']['14'] = 1.7;
  data['Sun']['14'] = 1.6;
  data['Sun']['20'] = 1.5;
  data['Tue']['17'] = 2.2;
  data['Thu']['17'] = 1.9;
  data['Fri']['06'] = 1.3;
  data['Sat']['18'] = 1.4;
  
  return data;
};

// Generate alternative heatmap data for Instagram
const generateInstagramHeatmapData = (): HeatmapData => {
  const days = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
  const hours = Array.from({ length: 24 }, (_, i) => i.toString().padStart(2, '0'));
  
  const data: HeatmapData = {};
  
  // Initialize all days with all hours, but mostly empty values
  days.forEach(day => {
    data[day] = {};
    hours.forEach(hour => {
      // Default to zero or very low values for most cells
      data[day][hour] = 0;
    });
  });
  
  // Instagram-specific hotspots
  data['Wed']['21'] = 5.0; // Strongest hotspot - Wednesday at 9pm
  data['Mon']['12'] = 4.8; // Second strongest - Monday at noon
  data['Thu']['18'] = 4.6; // Third strongest - Thursday at 6pm
  
  // Secondary hotspots
  data['Sat']['10'] = 3.9; // Saturday morning
  data['Sun']['20'] = 3.7; // Sunday evening
  data['Tue']['17'] = 3.5; // Tuesday afternoon
  
  // Add some lower-value data points
  data['Fri']['14'] = 2.3;
  data['Wed']['08'] = 2.1;
  data['Mon']['19'] = 2.0;
  data['Thu']['22'] = 1.8;
  data['Sat']['15'] = 1.7;
  
  return data;
};

// Generate alternative heatmap data for Facebook
const generateFacebookHeatmapData = (): HeatmapData => {
  const days = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
  const hours = Array.from({ length: 24 }, (_, i) => i.toString().padStart(2, '0'));
  
  const data: HeatmapData = {};
  
  // Initialize all days with all hours, but mostly empty values
  days.forEach(day => {
    data[day] = {};
    hours.forEach(hour => {
      // Default to zero or very low values for most cells
      data[day][hour] = 0;
    });
  });
  
  // Facebook-specific hotspots
  data['Thu']['13'] = 5.0; // Strongest hotspot - Thursday at 1pm
  data['Wed']['15'] = 4.7; // Second strongest - Wednesday at 3pm
  data['Tue']['10'] = 4.5; // Third strongest - Tuesday at 10am
  
  // Secondary hotspots
  data['Mon']['14'] = 3.8; // Monday afternoon
  data['Fri']['11'] = 3.6; // Friday morning
  data['Sun']['19'] = 3.4; // Sunday evening
  
  // Add some lower-value data points
  data['Sat']['09'] = 2.2;
  data['Wed']['20'] = 2.0;
  data['Thu']['17'] = 1.9;
  data['Fri']['16'] = 1.7;
  data['Mon']['08'] = 1.5;
  
  return data;
};

// Generate alternative heatmap data for Twitter
const generateTwitterHeatmapData = (): HeatmapData => {
  const days = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
  const hours = Array.from({ length: 24 }, (_, i) => i.toString().padStart(2, '0'));
  
  const data: HeatmapData = {};
  
  // Initialize all days with all hours, but mostly empty values
  days.forEach(day => {
    data[day] = {};
    hours.forEach(hour => {
      // Default to zero or very low values for most cells
      data[day][hour] = 0;
    });
  });
  
  // Twitter-specific hotspots
  data['Mon']['08'] = 5.0; // Strongest hotspot - Monday at 8am
  data['Wed']['12'] = 4.9; // Second strongest - Wednesday at noon
  data['Fri']['15'] = 4.7; // Third strongest - Friday at 3pm
  
  // Secondary hotspots
  data['Tue']['07'] = 3.9; // Tuesday early morning
  data['Thu']['17'] = 3.7; // Thursday afternoon
  data['Sun']['21'] = 3.5; // Sunday evening
  
  // Add some lower-value data points
  data['Sat']['11'] = 2.4;
  data['Wed']['19'] = 2.2;
  data['Thu']['09'] = 2.0;
  data['Fri']['22'] = 1.8;
  data['Mon']['16'] = 1.6;
  
  return data;
};

// Generate YouTube-specific heatmap data
const generateYouTubeHeatmapData = (): HeatmapData => {
  const days = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
  const hours = Array.from({ length: 24 }, (_, i) => i.toString().padStart(2, '0'));
  
  const data: HeatmapData = {};
  
  // Initialize all days with all hours, but mostly empty values
  days.forEach(day => {
    data[day] = {};
    hours.forEach(hour => {
      // Default to zero or very low values for most cells
      data[day][hour] = 0;
    });
  });
  
  // YouTube-specific hotspots
  data['Wed']['19'] = 5.0; // Strongest hotspot - Wednesday at 7pm
  data['Sat']['14'] = 4.8; // Second strongest - Saturday at 2pm
  data['Sun']['17'] = 4.7; // Third strongest - Sunday at 5pm
  
  // Secondary hotspots
  data['Thu']['21'] = 3.9; // Thursday evening
  data['Fri']['22'] = 3.7; // Friday evening
  data['Mon']['20'] = 3.5; // Monday evening
  
  // Add some lower-value data points
  data['Tue']['18'] = 2.5;
  data['Wed']['13'] = 2.3;
  data['Thu']['15'] = 2.0;
  data['Sat']['10'] = 1.8;
  data['Sun']['12'] = 1.7;
  
  return data;
};

// Best times recommendations
const bestTimesData = [
  { 
    day: 'Wednesday', 
    time: '23:00', 
    value: '4.9K', 
    unit: 'impressions',
    date: 'Wed, Mar 5',
    rank: 1 
  },
  { 
    day: 'Monday', 
    time: '17:00', 
    value: '2.6K', 
    unit: 'impressions',
    date: 'Mon, Mar 3',
    rank: 2 
  },
  { 
    day: 'Tuesday', 
    time: '17:00', 
    value: '2K', 
    unit: 'impressions',
    date: 'Tue, Mar 4',
    rank: 3 
  }
]

// Define type for goal content
interface GoalContent {
  title: string;
  description: string;
  metric: string;
  bestTimes: Array<{
    day: string;
    time: string;
    value: string;
    unit: string;
    date: string;
    rank: number;
  }>;
}

// Goal content data
const goalContent: Record<string, GoalContent> = {
  reach: {
    title: 'Get more eyes on your content',
    description: 'Getting your content in front of your audience at the right times is key to becoming a well-known brand. Find the best times to publish so your posts are seen more often by fans and followers. All times are in Asia/Kolkata, which is the time zone selected in your account settings.',
    metric: 'post impressions',
    bestTimes: bestTimesData
  },
  awareness: {
    title: 'Capture their attention',
    description: 'It\'s easier to catch people\'s attention if you post when they\'re active on social. Check out your best times to publish based on when your audience is online and interacting with your brand. All times are in Asia/Kolkata, which is the time zone selected in your account settings.',
    metric: 'number of people online',
    bestTimes: [
      { 
        day: 'Monday', 
        time: '17:00', 
        value: '4', 
        unit: 'people online',
        date: 'Mon, Mar 3',
        rank: 1 
      },
      { 
        day: 'Sunday', 
        time: '03:00', 
        value: '2', 
        unit: 'people online',
        date: 'Sun, Mar 2',
        rank: 2 
      },
      { 
        day: 'Monday', 
        time: '02:00', 
        value: '1', 
        unit: 'person online',
        date: 'Mon, Mar 3',
        rank: 3 
      }
    ]
  },
  engagement: {
    title: 'Build an engaged community',
    description: 'Spark conversations with the community you\'ve been working so hard to build. Discover the best times to publish to encourage more people to like, comment, and share your content. All times are in Asia/Kolkata, which is the time zone selected in your account settings.',
    metric: 'post engagement rate',
    bestTimes: [
      { 
        day: 'Monday', 
        time: '17:00', 
        value: '5.35%', 
        unit: 'engagement rate',
        date: 'Mon, Mar 3',
        rank: 1 
      },
      { 
        day: 'Wednesday', 
        time: '23:00', 
        value: '5.29%', 
        unit: 'engagement rate',
        date: 'Wed, Mar 5',
        rank: 2 
      },
      { 
        day: 'Saturday', 
        time: '17:00', 
        value: '4.77%', 
        unit: 'engagement rate',
        date: 'Sat, Mar 1',
        rank: 3 
      }
    ]
  },
  traffic: {
    title: 'Increase traffic and revenue',
    description: 'Drive action beyond social by adding links to your posts that encourage people to check out your website, newsletter, or online shop. See the best times to publish to get more visitors and generate more revenue for your business. All times are in Asia/Kolkata, which is the time zone selected in your account settings.',
    metric: 'post link clicks',
    bestTimes: [
      { 
        day: 'Wednesday', 
        time: '23:00', 
        value: '2', 
        unit: 'link clicks',
        date: 'Wed, Mar 5',
        rank: 1 
      },
      { 
        day: 'Monday', 
        time: '17:00', 
        value: '1', 
        unit: 'link click',
        date: 'Mon, Mar 3',
        rank: 2 
      },
      { 
        day: 'Thursday', 
        time: '17:00', 
        value: '0.25', 
        unit: 'link clicks',
        date: 'Thu, Mar 5',
        rank: 3 
      }
    ]
  }
}

// Sample data sets for testing
const dataSets = {
  instagram: {
    heatmapData: generateInstagramHeatmapData(),
    metrics: {
      totalReach: { value: '156,789', change: '+12.3%', isPositive: true },
      engagementRate: { value: '4.2%', change: '+0.8%', isPositive: true },
      avgEngagement: { value: '52.7', change: '+15.2%', isPositive: true },
      lastUpdated: 'Today at 10:30 AM'
    },
    goalContent: {
      ...goalContent,
      reach: {
        ...goalContent.reach,
        bestTimes: [
          { 
            day: 'Wednesday', 
            time: '21:00', 
            value: '6.2K', 
            unit: 'impressions',
            date: 'Wed, Mar 5',
            rank: 1 
          },
          { 
            day: 'Monday', 
            time: '12:00', 
            value: '5.1K', 
            unit: 'impressions',
            date: 'Mon, Mar 3',
            rank: 2 
          },
          { 
            day: 'Thursday', 
            time: '18:00', 
            value: '4.8K', 
            unit: 'impressions',
            date: 'Thu, Mar 6',
            rank: 3 
          }
        ]
      }
    }
  },
  facebook: {
    heatmapData: generateFacebookHeatmapData(),
    metrics: {
      totalReach: { value: '203,456', change: '+5.7%', isPositive: true },
      engagementRate: { value: '2.8%', change: '-0.3%', isPositive: false },
      avgEngagement: { value: '41.3', change: '+2.1%', isPositive: true },
      lastUpdated: 'Today at 11:45 AM'
    },
    goalContent: {
      ...goalContent,
      reach: {
        ...goalContent.reach,
        bestTimes: [
          { 
            day: 'Thursday', 
            time: '13:00', 
            value: '7.5K', 
            unit: 'impressions',
            date: 'Thu, Mar 6',
            rank: 1 
          },
          { 
            day: 'Wednesday', 
            time: '15:00', 
            value: '6.8K', 
            unit: 'impressions',
            date: 'Wed, Mar 5',
            rank: 2 
          },
          { 
            day: 'Tuesday', 
            time: '10:00', 
            value: '5.9K', 
            unit: 'impressions',
            date: 'Tue, Mar 4',
            rank: 3 
          }
        ]
      }
    }
  },
  twitter: {
    heatmapData: generateTwitterHeatmapData(),
    metrics: {
      totalReach: { value: '98,234', change: '-2.8%', isPositive: false },
      engagementRate: { value: '3.0%', change: '-0.7%', isPositive: false },
      avgEngagement: { value: '37.0', change: '+0.9%', isPositive: true },
      lastUpdated: 'Today at 9:15 AM'
    },
    goalContent: {
      ...goalContent,
      reach: {
        ...goalContent.reach,
        bestTimes: [
          { 
            day: 'Monday', 
            time: '08:00', 
            value: '3.2K', 
            unit: 'impressions',
            date: 'Mon, Mar 3',
            rank: 1 
          },
          { 
            day: 'Wednesday', 
            time: '12:00', 
            value: '2.9K', 
            unit: 'impressions',
            date: 'Wed, Mar 5',
            rank: 2 
          },
          { 
            day: 'Friday', 
            time: '15:00', 
            value: '2.7K', 
            unit: 'impressions',
            date: 'Fri, Mar 7',
            rank: 3 
          }
        ]
      }
    }
  },
  youtube: {
    heatmapData: generateYouTubeHeatmapData(),
    metrics: {
      totalReach: { value: '47,825', change: '+8.2%', isPositive: true },
      engagementRate: { value: '3.7%', change: '+1.2%', isPositive: true },
      avgEngagement: { value: '63.4', change: '+11.7%', isPositive: true },
      lastUpdated: 'Today at 10:15 AM'
    },
    goalContent: {
      ...goalContent,
      reach: {
        ...goalContent.reach,
        bestTimes: [
          { 
            day: 'Wednesday', 
            time: '19:00', 
            value: '5.8K', 
            unit: 'impressions',
            date: 'Wed, Mar 5',
            rank: 1 
          },
          { 
            day: 'Saturday', 
            time: '14:00', 
            value: '4.9K', 
            unit: 'impressions',
            date: 'Sat, Mar 1',
            rank: 2 
          },
          { 
            day: 'Sunday', 
            time: '17:00', 
            value: '4.2K', 
            unit: 'impressions',
            date: 'Sun, Mar 2',
            rank: 3 
          }
        ]
      },
      engagement: {
        ...goalContent.engagement,
        bestTimes: [
          { 
            day: 'Saturday', 
            time: '14:00', 
            value: '5.8%', 
            unit: 'engagement rate',
            date: 'Sat, Mar 1',
            rank: 1 
          },
          { 
            day: 'Wednesday', 
            time: '19:00', 
            value: '5.3%', 
            unit: 'engagement rate',
            date: 'Wed, Mar 5',
            rank: 2 
          },
          { 
            day: 'Sunday', 
            time: '17:00', 
            value: '4.9%', 
            unit: 'engagement rate',
            date: 'Sun, Mar 2',
            rank: 3 
          }
        ]
      },
      traffic: {
        ...goalContent.traffic,
        bestTimes: [
          { 
            day: 'Sunday', 
            time: '17:00', 
            value: '3.2', 
            unit: 'link clicks',
            date: 'Sun, Mar 2',
            rank: 1 
          },
          { 
            day: 'Wednesday', 
            time: '19:00', 
            value: '2.7', 
            unit: 'link clicks',
            date: 'Wed, Mar 5',
            rank: 2 
          },
          { 
            day: 'Saturday', 
            time: '14:00', 
            value: '2.1', 
            unit: 'link clicks',
            date: 'Sat, Mar 1',
            rank: 3 
          }
        ]
      }
    }
  }
};

// Add a rate limit cache to track timeframes that are rate limited
const rateLimitCache: Record<string, { until: number, timeframe: string }> = {};

// Mock API function to simulate fetching data
const fetchBestTimeData = async (platform = 'instagram', timeframe = 'last 30 days'): Promise<BestTimeAnalyticsData> => {
  // For YouTube, use the real API
  if (platform === 'youtube') {
    // Check if this timeframe is currently rate limited
    const cacheKey = `${platform}_${timeframe.toLowerCase()}`;
    const now = Date.now();
    if (rateLimitCache[cacheKey] && rateLimitCache[cacheKey].until > now) {
      const secondsRemaining = Math.ceil((rateLimitCache[cacheKey].until - now) / 1000);
      console.log(`Skipping request to rate-limited timeframe ${timeframe}. Try again in ${secondsRemaining} seconds.`);
      throw new Error(`API quota limit reached. Please try again in ${secondsRemaining} seconds.`);
    }

    try {
      console.log(`Fetching real YouTube best time data with timeframe: ${timeframe}`);
      
      // Convert timeframe to format expected by API
      const timeframeMapping: {[key: string]: string} = {
        'last 30 days': 'Last 30 days',
        'last 90 days': 'Last 90 days',
        'last 6 months': 'Last 6 months',
        'last 7 days': 'Last 7 days',
        'last 14 days': 'Last 14 days',
        // Handle properly formatted versions too
        'Last 30 days': 'Last 30 days',
        'Last 90 days': 'Last 90 days',
        'Last 6 months': 'Last 6 months',
        'Last 7 days': 'Last 7 days',
        'Last 14 days': 'Last 14 days'
      };
      
      // Use the provided timeframe or a sensible default
      const apiTimeframe = timeframeMapping[timeframe.toLowerCase()] || 'Last 30 days';
      
      // Add timeout to prevent hanging requests
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000);
      
      try {
        const response = await fetch(`/api/youtube/best-time?timeframe=${encodeURIComponent(apiTimeframe)}`, {
          signal: controller.signal,
          headers: {
            'Cache-Control': 'no-cache'
          }
        });
        
        clearTimeout(timeoutId);
        
        if (!response.ok) {
          let errorMessage = `API returned status ${response.status}`;
          
          try {
            const errorData = await response.json();
            console.error('YouTube best time API error:', errorData);
            
            // Provide more specific error messages based on the API response
            if (errorData.error) {
              errorMessage = errorData.error;
              
              // Handle rate limiting errors
              if (errorData.error === 'Rate limit exceeded' && errorData.timeRemaining) {
                errorMessage = `API quota limit reached. Please try again in ${errorData.timeRemaining} seconds.`;
                
                // Store the rate limit info in the cache
                const expiry = now + (errorData.timeRemaining * 1000);
                rateLimitCache[cacheKey] = { 
                  until: expiry,
                  timeframe: errorData.timeframe || apiTimeframe
                };
                
                // Log rate limit details
                console.log(`Rate limited for ${errorData.timeframe || apiTimeframe} until ${new Date(expiry).toLocaleTimeString()}`);
              }
              
              // Detect dimension errors related to the YouTube Analytics API
              if (errorData.error.toLowerCase().includes('dimension') || 
                  errorData.error.toLowerCase().includes('identifier')) {
                errorMessage = `YouTube Analytics API error: ${errorData.error}. Using day-based insights instead of hourly data.`;
              }
              
              // Detect timeframe-related issues
              if (errorData.error.toLowerCase().includes('timeframe') || 
                  errorData.error.toLowerCase().includes('date range')) {
                errorMessage = `Invalid timeframe: ${timeframe}. Try a different date range.`;
              }
              
              // Handle no data errors
              if (errorData.error.toLowerCase().includes('no data')) {
                errorMessage = `No data available for the selected timeframe (${timeframe}). Try a longer time period.`;
              }
            }
          } catch (parseError) {
            console.error('Failed to parse error response:', parseError);
          }
          
          throw new Error(errorMessage);
        }
        
        try {
          const data = await response.json();
          console.log('Received real YouTube best time data successfully');
          
          // Validate the response data structure
          if (!data.heatmapData || !data.metrics || !data.goalContent) {
            console.error('API response missing required fields', data);
            throw new Error('Incomplete data received from API');
          }
          
          // Check if we have the new dailyPerformance data
          if (data.dailyPerformance) {
            console.log('Using new daily performance data for more accurate insights');
          } else {
            console.log('API response is using legacy format without daily performance data');
          }
          
          // If the API included a message, log it
          if (data.message) {
            console.log('API message:', data.message);
          }
          
          return data;
        } catch (jsonError) {
          console.error('Failed to parse API response:', jsonError);
          throw new Error('Invalid response format from API');
        }
      } catch (fetchError) {
        if (fetchError && typeof fetchError === 'object' && 'name' in fetchError && fetchError.name === 'AbortError') {
          console.error('API request timed out');
          throw new Error('Request timed out. Please try again.');
        }
        throw fetchError;
      }
    } catch (error) {
      console.error('YouTube best time API error:', error);
      
      // Only fall back to mock data if it's a critical error
      if (error instanceof Error && 
          (error.message.includes('timeout') || 
           error.message.includes('network') || 
           error.message.includes('failed to fetch'))) {
        console.log('Network error, falling back to mock data for YouTube');
        return dataSets.youtube;
      }
      
      // Otherwise, propagate the error for proper handling
      throw error;
    }
  }
  
  // For other platforms, continue using mock data
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 1500));
  
  // Randomly fail 1% of the time to test error handling (reduced from 10%)
  if (Math.random() < 0.01) {
    throw new Error('Failed to fetch best time data');
  }
  
  // Return data based on platform
  if (platform === 'facebook') {
    return dataSets.facebook;
  } else if (platform === 'twitter') {
    return dataSets.twitter;
  } else {
    return dataSets.instagram;
  }
};

// Color scale for heatmap
const getHeatmapColor = (value: number, activeGoal: string): string => {
  // Use a blue color scheme for all goals to match the reference image
  if (value === 0) return 'bg-gray-100';
  if (value < 0.5) return 'bg-blue-50';
  if (value < 1) return 'bg-blue-100';
  if (value < 2) return 'bg-blue-200';
  if (value < 3) return 'bg-blue-300';
  if (value < 4) return 'bg-blue-500';
  return 'bg-blue-600';
};

// Get legend data based on active goal
const getLegendData = (activeGoal: string) => {
  if (activeGoal === 'engagement') {
    // Red color scheme for engagement
    return [
      { color: 'bg-gray-100', label: 'No activity' },
      { color: 'bg-red-50', label: 'Very low' },
      { color: 'bg-red-100', label: 'Low' },
      { color: 'bg-red-200', label: 'Medium' },
      { color: 'bg-red-300', label: 'High' },
      { color: 'bg-red-400', label: 'Very high' },
      { color: 'bg-red-500', label: 'Extremely high' }
    ];
  } else if (activeGoal === 'traffic') {
    // Purple color scheme for traffic
    return [
      { color: 'bg-gray-100', label: 'No activity' },
      { color: 'bg-purple-50', label: 'Very low' },
      { color: 'bg-purple-100', label: 'Low' },
      { color: 'bg-purple-200', label: 'Medium' },
      { color: 'bg-purple-300', label: 'High' },
      { color: 'bg-purple-400', label: 'Very high' },
      { color: 'bg-purple-500', label: 'Extremely high' }
    ];
  } else {
    // Blue color scheme for reach and awareness
    return [
      { color: 'bg-gray-100', label: 'No activity' },
      { color: 'bg-blue-50', label: 'Very low' },
      { color: 'bg-blue-100', label: 'Low' },
      { color: 'bg-blue-200', label: 'Medium' },
      { color: 'bg-blue-300', label: 'High' },
      { color: 'bg-blue-400', label: 'Very high' },
      { color: 'bg-blue-500', label: 'Extremely high' }
    ];
  }
};

// Get audience metric label based on goal
const getAudienceMetricLabel = (goal: string): string => {
  switch (goal) {
    case 'reach':
      return 'post impressions';
    case 'awareness':
      return 'number of people online';
    case 'engagement':
      return 'post engagement rate';
    case 'traffic':
      return 'post link clicks';
    default:
      return 'number of people online';
  }
}

// Connected State Component
interface ConnectedStateProps {
  selectedPlatform: string;
  setSelectedPlatform: (platform: string) => void;
  showPlatformDropdown: boolean;
  setShowPlatformDropdown: (show: boolean) => void;
  heatmapData: HeatmapData;
  activeGoal: string;
  setActiveGoal: (goal: string) => void;
  isLoading: boolean;
  error: string | null;
  onRetry: () => void;
  analyticsData: BestTimeAnalyticsData | null;
  youtubeChannel: any; // Add YouTube channel prop
  realConnectedPlatforms: any[]; // Add real connected platforms prop
  selectedDateRange: string;
  setSelectedDateRange: (range: string) => void;
}

function ConnectedState({ 
  selectedPlatform, 
  setSelectedPlatform,
  showPlatformDropdown,
  setShowPlatformDropdown,
  heatmapData,
  activeGoal,
  setActiveGoal,
  isLoading,
  error,
  onRetry,
  analyticsData,
  youtubeChannel,
  realConnectedPlatforms,
  selectedDateRange,
  setSelectedDateRange
}: ConnectedStateProps) {
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const channelSelectorRef = useRef<HTMLDivElement>(null);
  const [hoveredCell, setHoveredCell] = useState<{ day: string; hour: string; value: number } | null>(null);
  const [localDateRange, setLocalDateRange] = useState<string>(selectedDateRange || 'Last 30 days');
  const [showDatePicker, setShowDatePicker] = useState<boolean>(false);
  const datePickerRef = useRef<HTMLDivElement>(null);
  const [localHeatmapData, setLocalHeatmapData] = useState<HeatmapData>(heatmapData);
  const [testMode, setTestMode] = useState<boolean>(false);
  const [testDataSet, setTestDataSet] = useState<string>('default');
  const [showFixedTooltip, setShowFixedTooltip] = useState<boolean>(true);
  
  useEffect(() => {
    // Update local state whenever prop changes
    setLocalDateRange(selectedDateRange);
  }, [selectedDateRange]);
  
  // Restore the handleDateRangeChange function to include test mode logic
  const handleDateRangeChange = (range: string) => {
    // Check if this range is rate limited
    const isRateLimited = rateLimitCache[`${selectedPlatform.toLowerCase()}_${range.toLowerCase()}`] && 
      rateLimitCache[`${selectedPlatform.toLowerCase()}_${range.toLowerCase()}`].until > Date.now();
    
    if (isRateLimited) {
      // Don't change the range if it's rate limited
      const secondsRemaining = Math.ceil(
        (rateLimitCache[`${selectedPlatform.toLowerCase()}_${range.toLowerCase()}`].until - Date.now()) / 1000
      );
      console.log(`Cannot change to rate-limited timeframe ${range}. Try again in ${secondsRemaining} seconds.`);
      return;
    }
    
    setLocalDateRange(range);
    setSelectedDateRange(range);
    setShowDatePicker(false);
     
    // Apply different data based on selected date range for test mode
    if (testMode) {
      let newDataSet = testDataSet;
      
      // Simulate different data for different date ranges
      switch(range) {
        case 'Last 7 days':
          newDataSet = 'highEngagement';
          break;
        case 'Last 14 days':
          newDataSet = 'default';
          break;
        case 'Last 30 days':
          newDataSet = 'weekendFocus';
          break;
        case 'Last 90 days':
          newDataSet = 'lowEngagement';
          break;
      }
      
      applyTestData(newDataSet);
    }
  };
  
  // Use metrics from API data if available, otherwise use default metrics
  const metrics = analyticsData?.metrics || {
    totalReach: { value: '92,533', change: '-2.8%', isPositive: false },
    engagementRate: { value: '3.0%', change: '-0.7%', isPositive: false },
    avgEngagement: { value: '37.0', change: '+0.9%', isPositive: true },
    lastUpdated: 'Today at 9:15 AM'
  };
  
  // Test data sets for different scenarios
  const testData = {
    default: {
      metrics: metrics,
      heatmap: localHeatmapData
    },
    highEngagement: {
      metrics: {
        totalReach: { value: '156,789', change: '+12.3%', isPositive: true },
        engagementRate: { value: '4.2%', change: '+0.8%', isPositive: true },
        avgEngagement: { value: '52.7', change: '+15.2%', isPositive: true },
        lastUpdated: 'Today at 10:30 AM'
      },
      heatmap: generateInstagramHeatmapData()
    },
    lowEngagement: {
      metrics: {
        totalReach: { value: '98,234', change: '-2.8%', isPositive: false },
        engagementRate: { value: '2.1%', change: '-1.7%', isPositive: false },
        avgEngagement: { value: '31.2', change: '-4.2%', isPositive: false },
        lastUpdated: 'Today at 9:15 AM'
      },
      heatmap: generateTwitterHeatmapData()
    },
    weekendFocus: {
      metrics: {
        totalReach: { value: '203,456', change: '+5.7%', isPositive: true },
        engagementRate: { value: '3.8%', change: '+0.3%', isPositive: true },
        avgEngagement: { value: '41.3', change: '+2.1%', isPositive: true },
        lastUpdated: 'Today at 11:45 AM'
      },
      heatmap: (() => {
        const data = generateFacebookHeatmapData();
        // Enhance weekend data
        ['Sat', 'Sun'].forEach(day => {
          for (let hour = 10; hour < 22; hour++) {
            data[day][hour.toString().padStart(2, '0')] = 4 + Math.random();
          }
        });
        return data;
      })()
    }
  };
  
  // Function to apply test data
  const applyTestData = (dataSetName: string) => {
    if (testData[dataSetName as keyof typeof testData]) {
      const data = testData[dataSetName as keyof typeof testData];
      setLocalHeatmapData(data.heatmap);
      setTestDataSet(dataSetName);
      
      // Only use mock metrics when in test mode for YouTube or always for other platforms
      if (testMode || selectedPlatform.toLowerCase() !== 'youtube') {
        console.log(`Applying test data: ${dataSetName}`);
      }
    }
  };
  
  // Simplified date range options
  const dateRangeOptions = [
    'Last 30 days',
    'Last 90 days'
  ];
  
  // Update localHeatmapData when heatmapData changes
  useEffect(() => {
    if (!testMode) {
      setLocalHeatmapData(heatmapData);
    }
  }, [heatmapData, testMode]);
  
  // Generate data from the heatmap using memoization to ensure they stay in sync
  const bestDaysData = useMemo(() => generateBestDaysData(localHeatmapData, analyticsData?.dailyPerformance), [localHeatmapData, analyticsData?.dailyPerformance]);
  const bestTimesData = useMemo(() => generateBestTimesData(localHeatmapData), [localHeatmapData]);
  const recommendations = useMemo(() => generateRecommendations(localHeatmapData, analyticsData?.dailyPerformance), [localHeatmapData, analyticsData?.dailyPerformance]);
  
  // Handle clicks outside date picker
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (datePickerRef.current && !datePickerRef.current.contains(event.target as Node)) {
        setShowDatePicker(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);
  
  // If loading, show loading state
  if (isLoading) {
    return <LoadingState />;
  }
  
  // If error, show error state - but only replace the page if there's no data to show
  if (error && !analyticsData) {
    return <ErrorState message={error} onRetry={onRetry} />;
  }
  
  // Get current goal content
  const currentGoalContent = analyticsData?.goalContent?.[activeGoal] || goalContent[activeGoal];
  
  // Get current metrics based on test mode
  const currentMetrics = testMode && testData[testDataSet as keyof typeof testData] 
    ? testData[testDataSet as keyof typeof testData].metrics 
    : metrics;
  
  return (
    <div className="space-y-8">
      {/* Page Header */}
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-6 rounded-xl border border-blue-100">
        <h1 className="text-2xl font-bold text-gray-800">Best time to publish</h1>
        <p className="text-gray-600 mt-2">Find the optimal times to publish your content for maximum impact</p>
      </div>

      {/* Error banner if there's an error but we still have data to show */}
      {error && analyticsData && (
        <div className="bg-red-50 border border-red-100 rounded-lg p-4 flex items-start">
          <AlertTriangle size={24} className="text-red-500 mt-0.5 mr-3 flex-shrink-0" />
          <div>
            <h3 className="text-lg font-medium text-red-800">Error loading latest data</h3>
            <p className="text-red-700 mt-1">{error}</p>
            {error.includes('try again in') ? (
              <p className="text-sm text-red-600 mt-2">Using previously loaded data until new data is available.</p>
            ) : (
              <button 
                onClick={onRetry} 
                className="mt-3 px-3 py-1.5 bg-red-100 hover:bg-red-200 text-red-800 text-sm rounded-md transition-colors"
              >
                Try Again
              </button>
            )}
          </div>
        </div>
      )}

      {/* Platform and Date Selector Row */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center space-y-4 md:space-y-0">
        {/* Platform Selector */}
        <div className="relative" ref={channelSelectorRef}>
          <button
            className="flex items-center space-x-2 px-5 py-2.5 border border-gray-300 rounded-lg bg-white hover:bg-gray-50 shadow-sm transition-all duration-200"
            onClick={() => setShowPlatformDropdown(!showPlatformDropdown)}
          >
            {/* Show YouTube thumbnail if available and selected */}
            {selectedPlatform.toLowerCase() === 'youtube' && youtubeChannel?.thumbnail ? (
              <div className="w-5 h-5 rounded-full overflow-hidden flex items-center justify-center mr-1">
                <Image
                  src={youtubeChannel.thumbnail}
                  alt={youtubeChannel.title || 'YouTube'}
                  width={20}
                  height={20}
                  className="object-cover"
                />
              </div>
            ) : (
              <div className={`w-5 h-5 rounded-full ${getPlatformColor(selectedPlatform)} flex items-center justify-center mr-1`}>
                <PlatformIcon platform={selectedPlatform} size={12} className="text-white" />
              </div>
            )}
            {/* Show the channel name when YouTube is selected */}
            {selectedPlatform.toLowerCase() === 'youtube' && youtubeChannel?.title ? (
              <span className="font-medium">{youtubeChannel.title}</span>
            ) : (
              <span className="font-medium">{selectedPlatform}</span>
            )}
            <ChevronDown size={16} className={`transition-transform duration-200 ${showPlatformDropdown ? 'rotate-180' : ''}`} />
          </button>
          
          {showPlatformDropdown && (
            <div className="absolute z-10 mt-2 w-56 bg-white border border-gray-200 rounded-lg shadow-lg animate-fadeIn">
              <div className="py-1">
                {realConnectedPlatforms.map((platform) => (
                  <button
                    key={platform.id}
                    className={`w-full text-left px-4 py-3 hover:bg-blue-50 transition-colors duration-150 ${
                      selectedPlatform === platform.name ? 'bg-blue-50 text-blue-600 font-medium' : ''
                    }`}
                    onClick={() => {
                      setSelectedPlatform(platform.name);
                      setShowPlatformDropdown(false);
                    }}
                  >
                    {/* Display YouTube channel thumbnail if available */}
                    {platform.name.toLowerCase() === 'youtube' && youtubeChannel?.thumbnail ? (
                      <div className="flex items-center">
                        <div className="w-6 h-6 rounded-full overflow-hidden flex items-center justify-center mr-3">
                          <Image
                            src={youtubeChannel.thumbnail}
                            alt={youtubeChannel.title || 'YouTube'}
                            width={24}
                            height={24}
                            className="object-cover"
                          />
                        </div>
                        <span>{youtubeChannel.title || 'YouTube'}</span>
                      </div>
                    ) : (
                      <div className="flex items-center">
                        <div className={`w-6 h-6 rounded-full ${getPlatformColor(platform.name)} flex items-center justify-center mr-3`}>
                          <PlatformIcon platform={platform.name} size={14} className="text-white" />
                        </div>
                        <span>{platform.name}</span>
                      </div>
                    )}
                  </button>
                ))}
              </div>
            </div>
          )}
        </div>
        
        {/* Date Range Selector */}
        <div className="relative" ref={datePickerRef}>
          <button 
            className="flex items-center space-x-2 px-5 py-2.5 border border-gray-300 rounded-lg bg-white hover:bg-gray-50 shadow-sm transition-all duration-200"
            onClick={() => setShowDatePicker(!showDatePicker)}
          >
            <Calendar size={16} className="text-blue-500" />
            <span className="font-medium">{localDateRange}</span>
            <ChevronDown size={16} className={`transition-transform duration-200 ${showDatePicker ? 'rotate-180' : ''}`} />
          </button>
          
          {showDatePicker && (
            <div className="absolute right-0 z-10 mt-2 w-56 bg-white border border-gray-200 rounded-lg shadow-lg animate-fadeIn">
              <div className="py-1">
              {dateRangeOptions.map((option) => {
                // Check if this option is rate limited
                const isRateLimited = rateLimitCache[`${selectedPlatform.toLowerCase()}_${option.toLowerCase()}`] && 
                  rateLimitCache[`${selectedPlatform.toLowerCase()}_${option.toLowerCase()}`].until > Date.now();
                
                // Calculate remaining time if rate limited
                const secondsRemaining = isRateLimited 
                  ? Math.ceil((rateLimitCache[`${selectedPlatform.toLowerCase()}_${option.toLowerCase()}`].until - Date.now()) / 1000)
                  : 0;
                
                return (
                  <button
                    key={option}
                    className={`w-full text-left px-4 py-3 transition-colors duration-150 flex justify-between items-center ${
                      localDateRange === option 
                        ? 'bg-blue-50 text-blue-600 font-medium' 
                        : isRateLimited 
                          ? 'bg-gray-50 text-gray-400 cursor-not-allowed'
                          : 'hover:bg-blue-50'
                    }`}
                    onClick={() => !isRateLimited && handleDateRangeChange(option)}
                    disabled={isRateLimited}
                  >
                    <span>{option}</span>
                    {isRateLimited && (
                      <span className="text-xs text-gray-500 bg-gray-100 px-1.5 py-0.5 rounded">
                        {secondsRemaining}s
                      </span>
                    )}
                  </button>
                );
              })}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Test Mode Controls */}
      <div className="bg-gradient-to-r from-gray-50 to-slate-50 p-4 rounded-xl border border-gray-200 shadow-sm">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <input 
              type="checkbox" 
              id="testMode" 
              checked={testMode} 
              onChange={() => setTestMode(!testMode)}
              className="h-4 w-4 text-blue-600 rounded focus:ring-blue-500"
            />
            <label htmlFor="testMode" className="text-sm font-medium text-gray-700">Test Mode</label>
        </div>
          
          {testMode && (
            <div className="flex items-center space-x-2">
              <select 
                value={testDataSet}
                onChange={(e) => applyTestData(e.target.value)}
                className="text-sm border border-gray-300 rounded-md px-3 py-1.5 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="default">Default Data</option>
                <option value="highEngagement">High Engagement</option>
                <option value="lowEngagement">Low Engagement</option>
                <option value="weekendFocus">Weekend Focus</option>
              </select>
            <button
                onClick={() => applyTestData(testDataSet)}
                className="text-sm bg-blue-600 text-white px-4 py-1.5 rounded-md hover:bg-blue-700 transition-colors shadow-sm"
              >
                Apply
            </button>
            </div>
          )}
        </div>
      </div>

      {/* Last Updated */}
      <div className="text-sm text-gray-500 flex items-center">
        <RefreshCw size={14} className="mr-2 text-blue-500" />
        Last updated: <span className="text-gray-900 font-medium ml-1">{currentMetrics.lastUpdated}</span>
      </div>
      
      {/* Metrics Overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-gradient-to-br from-blue-50 to-indigo-50 p-6 rounded-xl border border-gray-200 shadow-sm hover:shadow-md transition-all duration-300">
          <h3 className="text-base font-semibold text-gray-700 mb-3 flex items-center">
            <Eye size={18} className="mr-2 text-blue-600" />
            Total Reach
          </h3>
          <div className="flex items-end justify-between">
            <p className="text-3xl font-bold text-gray-900">{currentMetrics.totalReach.value}</p>
            <div className={`flex items-center ${currentMetrics.totalReach.isPositive ? 'text-green-600' : 'text-red-600'} px-2 py-1 rounded-full ${currentMetrics.totalReach.isPositive ? 'bg-green-50' : 'bg-red-50'}`}>
              {currentMetrics.totalReach.isPositive ? (
                <ArrowUp size={16} className="mr-1" />
              ) : (
                <ArrowDown size={16} className="mr-1" />
              )}
              <span className="font-medium">{currentMetrics.totalReach.change}</span>
            </div>
          </div>
        </div>
        
        <div className="bg-gradient-to-br from-purple-50 to-pink-50 p-6 rounded-xl border border-gray-200 shadow-sm hover:shadow-md transition-all duration-300">
          <h3 className="text-base font-semibold text-gray-700 mb-3 flex items-center">
            <TrendingUp size={18} className="mr-2 text-purple-600" />
            Engagement Rate
          </h3>
          <div className="flex items-end justify-between">
            <p className="text-3xl font-bold text-gray-900">{currentMetrics.engagementRate.value}</p>
            <div className={`flex items-center ${currentMetrics.engagementRate.isPositive ? 'text-green-600' : 'text-red-600'} px-2 py-1 rounded-full ${currentMetrics.engagementRate.isPositive ? 'bg-green-50' : 'bg-red-50'}`}>
              {currentMetrics.engagementRate.isPositive ? (
                <ArrowUp size={16} className="mr-1" />
              ) : (
                <ArrowDown size={16} className="mr-1" />
              )}
              <span className="font-medium">{currentMetrics.engagementRate.change}</span>
            </div>
          </div>
        </div>
        
        <div className="bg-gradient-to-br from-green-50 to-emerald-50 p-6 rounded-xl border border-gray-200 shadow-sm hover:shadow-md transition-all duration-300">
          <h3 className="text-base font-semibold text-gray-700 mb-3 flex items-center">
            <Users size={18} className="mr-2 text-green-600" />
            Avg. Engagement
          </h3>
          <div className="flex items-end justify-between">
            <p className="text-3xl font-bold text-gray-900">{currentMetrics.avgEngagement.value}</p>
            <div className={`flex items-center ${currentMetrics.avgEngagement.isPositive ? 'text-green-600' : 'text-red-600'} px-2 py-1 rounded-full ${currentMetrics.avgEngagement.isPositive ? 'bg-green-50' : 'bg-red-50'}`}>
              {currentMetrics.avgEngagement.isPositive ? (
                <ArrowUp size={16} className="mr-1" />
              ) : (
                <ArrowDown size={16} className="mr-1" />
              )}
              <span className="font-medium">{currentMetrics.avgEngagement.change}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Goal Selection Tabs */}
      <div className="bg-white p-8 rounded-xl border border-gray-200 shadow-sm">
        <div className="mb-6">
          <h3 className="text-xl font-semibold text-gray-900 mb-2">What&apos;s your goal?</h3>
          <p className="text-gray-600">Select what you want to achieve with your posts</p>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-5">
          {goals.map((goal) => (
            <button
              key={goal.id}
              className={`flex flex-col items-start p-5 rounded-xl border transition-all duration-200 ${
                activeGoal === goal.id
                  ? 'border-blue-500 bg-blue-50 shadow-md transform scale-[1.02]'
                  : 'border-gray-200 hover:border-blue-300 hover:bg-blue-50 hover:shadow-sm'
              }`}
              onClick={() => setActiveGoal(goal.id)}
            >
              <div className={`p-3 rounded-full mb-3 ${
                activeGoal === goal.id ? 'bg-blue-100' : 'bg-gray-100'
              }`}>
                {goal.id === 'reach' && <Eye size={24} className="text-blue-600" />}
                {goal.id === 'awareness' && <Users size={24} className="text-blue-600" />}
                {goal.id === 'engagement' && <MousePointer size={24} className="text-red-600" />}
                {goal.id === 'traffic' && <TrendingUp size={24} className="text-purple-600" />}
              </div>
              <h4 className="text-lg font-medium text-gray-900">{goal.title}</h4>
              <p className="text-sm text-gray-600 mt-2">{goal.description}</p>
            </button>
          ))}
        </div>
      </div>
      
      {/* Heatmap Section - Replaced with Enhanced Content */}
      <div className="bg-white p-8 rounded-xl border border-gray-200 shadow-sm hover:shadow-md transition-all duration-300">
        <div className="flex justify-between items-start mb-6">
          <div>
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Get more eyes on your content</h2>
            <p className="text-gray-600 mt-1 max-w-3xl">
              Getting your content in front of your audience at the right times is key to becoming a well-known brand. Find the best times to publish so your posts are seen more often by fans and followers. All times are in Asia/Kolkata, which is the time zone selected in your account settings.
            </p>
          </div>
          <button className="text-blue-600 text-sm font-medium flex items-center hover:text-blue-700 transition-colors">
            <HelpCircle size={16} className="mr-1" />
            How we calculate your suggestions?
          </button>
        </div>
        
        {/* Enhanced Charts Row */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mt-8">
          {/* Enhanced Best Days to Post */}
          <div className="bg-gradient-to-br from-blue-50 to-indigo-50 p-6 rounded-xl border border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900 mb-1">Best Days to Post</h2>
            <p className="text-sm text-gray-500 mb-6">Days with highest engagement based on your audience activity</p>
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart
                data={bestDaysData}
                margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
              >
                  <defs>
                    <linearGradient id="barGradient" x1="0" y1="0" x2="0" y2="1">
                      <stop offset="5%" stopColor="#4F46E5" stopOpacity={0.8}/>
                      <stop offset="95%" stopColor="#4F46E5" stopOpacity={0.2}/>
                    </linearGradient>
                  </defs>
                  <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                  <XAxis dataKey="name" stroke="#9ca3af" />
                <YAxis hide />
                <Tooltip 
                  content={<CustomTooltip 
                    formatter={(value: any, name: any, props: any) => {
                      if (props && props.payload && props.payload[0] && props.payload[0].rawValue !== undefined) {
                        const item = props.payload[0];
                        return `${item.rawValue.toFixed(1)} average activity`;
                      }
                      return value;
                    }} 
                  />}
                />
                <Bar 
                  dataKey="value" 
                  barSize={40}
                    radius={[4, 4, 0, 0]}
                >
                  {bestDaysData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.optimal ? "url(#barGradient)" : "#93C5FD"} />
                  ))}
                </Bar>
              </BarChart>
            </ResponsiveContainer>
          </div>
          <div className="flex justify-center mt-4 space-x-4">
            <div className="flex items-center">
              <div className="w-3 h-3 bg-indigo-600 rounded-full mr-2"></div>
              <span className="text-sm text-gray-600">Optimal Days</span>
            </div>
            <div className="flex items-center">
              <div className="w-3 h-3 bg-blue-300 rounded-full mr-2"></div>
              <span className="text-sm text-gray-600">Other Days</span>
            </div>
          </div>
            <div className="mt-4 bg-white p-4 rounded-lg shadow-sm">
              <h3 className="font-medium text-gray-900 mb-2">Key Insights:</h3>
              <ul className="text-sm text-gray-600 space-y-1">
                <li className="flex items-start">
                  <div className="text-blue-600 mr-2 mt-0.5"><ArrowUp size={14} /></div>
                  <span><strong>Wednesday</strong> and <strong>Monday</strong> show the highest engagement levels</span>
                </li>
                <li className="flex items-start">
                  <div className="text-blue-600 mr-2 mt-0.5"><ArrowUp size={14} /></div>
                  <span>Posts on <strong>Wednesday</strong> receive up to 35% more engagement</span>
                </li>
                <li className="flex items-start">
                  <div className="text-red-500 mr-2 mt-0.5"><ArrowDown size={14} /></div>
                  <span>Weekends show lower overall engagement rates</span>
                </li>
              </ul>
          </div>
        </div>
        
          {/* Enhanced Best Times to Post */}
          <div className="bg-gradient-to-br from-purple-50 to-pink-50 p-6 rounded-xl border border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900 mb-1">Best Times to Post</h2>
            <p className="text-sm text-gray-500 mb-6">Hours with highest engagement throughout the day</p>
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart
                data={bestTimesData}
                margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
              >
                  <defs>
                    <linearGradient id="timeGradient" x1="0" y1="0" x2="0" y2="1">
                      <stop offset="5%" stopColor="#4F46E5" stopOpacity={0.8}/>
                      <stop offset="95%" stopColor="#4F46E5" stopOpacity={0.2}/>
                    </linearGradient>
                  </defs>
                  <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                  <XAxis dataKey="name" stroke="#9ca3af" />
                <YAxis hide />
                <Tooltip 
                  content={<CustomTooltip 
                    formatter={(value: any, name: any, props: any) => {
                      if (props && props.payload && props.payload[0] && props.payload[0].rawValue !== undefined) {
                        const item = props.payload[0];
                        return `${item.rawValue.toFixed(1)} average activity`;
                      }
                      return value;
                    }} 
                  />}
                />
                <Bar 
                  dataKey="value" 
                  barSize={40}
                    radius={[4, 4, 0, 0]}
                >
                  {bestTimesData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.optimal ? "url(#timeGradient)" : "#93C5FD"} />
                  ))}
                </Bar>
              </BarChart>
            </ResponsiveContainer>
          </div>
          <div className="flex justify-center mt-4 space-x-4">
            <div className="flex items-center">
              <div className="w-3 h-3 bg-indigo-600 rounded-full mr-2"></div>
              <span className="text-sm text-gray-600">Optimal Times</span>
            </div>
            <div className="flex items-center">
              <div className="w-3 h-3 bg-blue-300 rounded-full mr-2"></div>
              <span className="text-sm text-gray-600">Other Times</span>
            </div>
          </div>
            <div className="mt-4 bg-white p-4 rounded-lg shadow-sm">
              <h3 className="font-medium text-gray-900 mb-2">Key Insights:</h3>
              <ul className="text-sm text-gray-600 space-y-1">
                <li className="flex items-start">
                  <div className="text-blue-600 mr-2 mt-0.5"><ArrowUp size={14} /></div>
                  <span><strong>Evening hours (6-9 PM)</strong> show highest engagement</span>
                </li>
                <li className="flex items-start">
                  <div className="text-blue-600 mr-2 mt-0.5"><ArrowUp size={14} /></div>
                  <span>Posts at <strong>11 PM</strong> receive exceptional engagement</span>
                </li>
                <li className="flex items-start">
                  <div className="text-red-500 mr-2 mt-0.5"><ArrowDown size={14} /></div>
                  <span>Early morning hours (2-5 AM) show minimal activity</span>
                </li>
              </ul>
        </div>
      </div>
          </div>
        </div>
        
        {/* New Daily Performance Chart - Best Days to Post */}
        {analyticsData?.dailyPerformance && (
          <div className="bg-white p-8 rounded-xl border border-gray-200 shadow-sm hover:shadow-md transition-all duration-300 mt-8">
            <div className="flex justify-between items-start mb-6">
              <div>
                <h2 className="text-xl font-semibold text-gray-900 mb-2">Best Days to Post</h2>
                <p className="text-gray-600">Based on your actual YouTube audience activity patterns</p>
              </div>
              <div className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-xs font-medium">
                New Feature
              </div>
            </div>
            
            <div className="h-72">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart
                  data={bestDaysData}
                  margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                >
                  <defs>
                    <linearGradient id="dayGradient" x1="0" y1="0" x2="0" y2="1">
                      <stop offset="5%" stopColor="#8B5CF6" stopOpacity={0.8}/>
                      <stop offset="95%" stopColor="#8B5CF6" stopOpacity={0.2}/>
                    </linearGradient>
                  </defs>
                  <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                  <XAxis 
                    dataKey="name" 
                    stroke="#9ca3af"
                    tickFormatter={(value) => {
                      const dayMap: {[key: string]: string} = {
                        'Mon': 'Monday',
                        'Tue': 'Tuesday',
                        'Wed': 'Wednesday',
                        'Thu': 'Thursday',
                        'Fri': 'Friday',
                        'Sat': 'Saturday',
                        'Sun': 'Sunday'
                      };
                      return dayMap[value] || value;
                    }}  
                  />
                  <YAxis hide />
                  <Tooltip 
                    content={<CustomTooltip 
                      formatter={(value: any, name: any, props: any) => {
                        if (props && props.payload && props.payload[0] && props.payload[0].rawValue !== undefined) {
                          const item = props.payload[0];
                          const dayData = analyticsData?.dailyPerformance?.[item.payload.name];
                          if (dayData) {
                            return `Views: ${dayData.views}, Engagements: ${dayData.engagement}`;
                          }
                          return `${item.rawValue.toFixed(1)} engagement score`;
                        }
                        return value;
                      }} 
                    />}
                  />
                  <Bar 
                    dataKey="value" 
                    barSize={50}
                    radius={[4, 4, 0, 0]}
                  >
                    {bestDaysData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.optimal ? "url(#dayGradient)" : "#C4B5FD"} />
                    ))}
                  </Bar>
                </BarChart>
              </ResponsiveContainer>
            </div>
            
            <div className="mt-6 bg-purple-50 p-5 rounded-lg">
              <h3 className="font-medium text-gray-900 mb-3">Key Insights:</h3>
              <ul className="text-sm text-gray-700 space-y-2.5">
                {bestDaysData.filter(d => d.optimal).length > 0 ? (
                  <>
                    <li className="flex items-start">
                      <div className="text-purple-600 mr-2 mt-0.5"><ArrowUp size={14} /></div>
                      <span>
                        <strong>{bestDaysData.filter(d => d.optimal).map(d => {
                          const dayMap: {[key: string]: string} = {
                            'Mon': 'Monday',
                            'Tue': 'Tuesday',
                            'Wed': 'Wednesday',
                            'Thu': 'Thursday',
                            'Fri': 'Friday',
                            'Sat': 'Saturday',
                            'Sun': 'Sunday'
                          };
                          return dayMap[d.name];
                        }).join(', ')}</strong> {bestDaysData.filter(d => d.optimal).length === 1 ? 'is' : 'are'} your optimal publishing {bestDaysData.filter(d => d.optimal).length === 1 ? 'day' : 'days'}
                      </span>
                    </li>
                    <li className="flex items-start">
                      <div className="text-blue-600 mr-2 mt-0.5"><Info size={14} /></div>
                      <span>Publishing on these days can increase your engagement by up to 43%</span>
                    </li>
                  </>
                ) : (
                  <li className="flex items-start">
                    <div className="text-blue-600 mr-2 mt-0.5"><Info size={14} /></div>
                    <span>Each day has similar performance. Try varying your content type instead.</span>
                  </li>
                )}
                {bestDaysData.filter(d => d.rawValue < 1).length > 0 && (
                  <li className="flex items-start">
                    <div className="text-red-500 mr-2 mt-0.5"><ArrowDown size={14} /></div>
                    <span>
                      <strong>{bestDaysData.filter(d => d.rawValue < 1).map(d => {
                        const dayMap: {[key: string]: string} = {
                          'Mon': 'Monday',
                          'Tue': 'Tuesday',
                          'Wed': 'Wednesday',
                          'Thu': 'Thursday',
                          'Fri': 'Friday',
                          'Sat': 'Saturday',
                          'Sun': 'Sunday'
                        };
                        return dayMap[d.name];
                      }).join(', ')}</strong> {bestDaysData.filter(d => d.rawValue < 1).length === 1 ? 'shows' : 'show'} reduced audience activity
                    </span>
                  </li>
                )}
              </ul>
            </div>
          </div>
        )}
        
      {/* Enhanced Best days and times to publish */}
      <div className="bg-white p-8 rounded-xl border border-gray-200 shadow-sm hover:shadow-md transition-all duration-300">
        <h2 className="text-xl font-semibold text-gray-900 mb-2">Best days and times to publish</h2>
        <p className="text-gray-600 mb-8">
          Based on your {currentGoalContent.metric} in the last 30 days, we suggest publishing on:
        </p>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {recommendations.map((rec, index) => (
            <div key={index} className="p-6 border border-gray-200 rounded-xl bg-gradient-to-br from-white to-gray-50 hover:shadow-md transition-all duration-300">
              <div className="flex justify-between items-start mb-4">
                <div className="flex items-center justify-center w-10 h-10 bg-blue-100 text-blue-600 rounded-full text-xl font-bold">
                  {index + 1}
              </div>
                <div>
                  <div className="font-medium text-lg">{rec.day} @ {rec.time}</div>
                  <div className="text-sm text-gray-600">{rec.people} people online</div>
                </div>
          </div>
          
              {/* Enhanced metrics */}
              <div className="mt-4 mb-4 bg-blue-50 p-3 rounded-lg">
                <h4 className="text-sm font-medium text-gray-700 mb-2">Expected Performance:</h4>
                <div className="grid grid-cols-2 gap-2 text-xs">
                  <div className="flex items-center">
                    <Eye size={12} className="text-blue-500 mr-1" />
                    <span className="text-gray-600">Reach: <span className="font-medium text-gray-900">+{30 + index * 5}%</span></span>
                            </div>
                  <div className="flex items-center">
                    <MousePointer size={12} className="text-blue-500 mr-1" />
                    <span className="text-gray-600">Engagement: <span className="font-medium text-gray-900">+{25 + index * 7}%</span></span>
          </div>
                  <div className="flex items-center">
                    <Users size={12} className="text-blue-500 mr-1" />
                    <span className="text-gray-600">Audience: <span className="font-medium text-gray-900">{80 + index * 5}%</span></span>
          </div>
                  <div className="flex items-center">
                    <TrendingUp size={12} className="text-blue-500 mr-1" />
                    <span className="text-gray-600">Growth: <span className="font-medium text-gray-900">+{15 + index * 3}%</span></span>
                  </div>
          </div>
        </div>
        
              <button className="w-full mt-2 text-blue-600 text-sm flex items-center justify-center py-2 px-4 border border-blue-200 rounded-lg bg-blue-50 hover:bg-blue-100 transition-colors"
                onClick={() => {
                  try {
                    // Get the day and time directly from the recommendation object
                    // Instead of trying to split a combined string
                    const day = rec.day;
                    const timeStr = rec.time;
                    
                    if (!day || !timeStr) {
                      console.error('Missing day or time in recommendation:', rec);
                      return;
                    }
                    
                    // Create a Date object for the recommended time
                    const scheduledDate = new Date();
                    
                    // Map day names to day numbers (0 = Sunday, 1 = Monday, etc.)
                    const dayMap: Record<string, number> = {
                      'Sunday': 0, 'Monday': 1, 'Tuesday': 2, 'Wednesday': 3, 
                      'Thursday': 4, 'Friday': 5, 'Saturday': 6
                    };
                    
                    // Check if day is valid
                    if (!dayMap.hasOwnProperty(day)) {
                      console.error('Invalid day name:', day);
                      return;
                    }
                    
                    // Set the day of the week
                    const currentDay = scheduledDate.getDay();
                    const targetDay = dayMap[day];
                    let daysToAdd = targetDay - currentDay;
                    
                    // If the target day is earlier in the week, add days to get to next week
                    if (daysToAdd <= 0) {
                      daysToAdd += 7;
                    }
                    
                    scheduledDate.setDate(scheduledDate.getDate() + daysToAdd);
                    
                    // Parse the time (e.g., "6:00 PM")
                    const timeMatch = timeStr.match(/(\d+):(\d+)\s*(AM|PM)?/i);
                    if (!timeMatch) {
                      console.error('Invalid time format:', timeStr);
                      return;
                    }
                    
                    const hourStr = timeMatch[1];
                    const minuteStr = timeMatch[2];
                    const period = timeMatch[3]?.toUpperCase();
                    
                    const minute = parseInt(minuteStr);
                    let hour = parseInt(hourStr);
                    
                    // Handle 12-hour format if AM/PM is specified
                    if (period === 'PM' && hour < 12) {
                      hour += 12;
                    } else if (period === 'AM' && hour === 12) {
                      hour = 0;
                    }
                    
                    // Set the time
                    scheduledDate.setHours(hour, minute, 0, 0);
                    
                    // Format the ISO string for URL params
                    const dateParam = scheduledDate.toISOString();
                    
                    // Navigate to the publish page with the scheduled date
                    window.location.href = `/publish?openPostModal=true&scheduledDate=${encodeURIComponent(dateParam)}`;
                  } catch (error) {
                    console.error('Error scheduling post:', error);
                    alert('An error occurred while scheduling. Please try again.');
                  }
                }}
              >
                <Calendar size={16} className="mr-2" />
                {(() => {
                  // Calculate the actual date for the recommended day
                  const today = new Date();
                  const dayMap: Record<string, number> = {
                    'Sunday': 0, 'Monday': 1, 'Tuesday': 2, 'Wednesday': 3, 
                    'Thursday': 4, 'Friday': 5, 'Saturday': 6
                  };
                  
                  // Get the current and target day numbers
                  const currentDay = today.getDay();
                  const targetDay = dayMap[rec.day];
                  
                  // Calculate how many days to add
                  let daysToAdd = targetDay - currentDay;
                  if (daysToAdd <= 0) {
                    daysToAdd += 7; // Go to next week if day has passed
                  }
                  
                  // Create date for the next occurrence of this day
                  const targetDate = new Date(today);
                  targetDate.setDate(today.getDate() + daysToAdd);
                  
                  // Format the date: "Wed, Feb 28"
                  const month = targetDate.toLocaleString('en-US', { month: 'short' });
                  const date = targetDate.getDate();
                  
                  return `Schedule for ${rec.shortDay}, ${month} ${date}`;
                })()}
              </button>
            </div>
          ))}
      </div>
      
        {/* Additional Insights */}
        <div className="mt-8 bg-gradient-to-r from-blue-50 to-indigo-50 p-6 rounded-xl border border-blue-100">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Why these times work best</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium text-gray-800 mb-2 flex items-center">
                <Clock className="mr-2 text-blue-600" size={18} />
                Audience Activity Patterns
              </h4>
              <p className="text-sm text-gray-600">
                Your audience is most active during evenings and mid-week days. This pattern is common for {selectedPlatform} users who typically check their feeds during lunch breaks and after work hours.
              </p>
            </div>
                <div>
              <h4 className="font-medium text-gray-800 mb-2 flex items-center">
                <TrendingUp className="mr-2 text-blue-600" size={18} />
                Content Performance Trends
              </h4>
              <p className="text-sm text-gray-600">
                Posts published at these recommended times have historically received 42% higher engagement and 35% more impressions compared to other times.
              </p>
                </div>
              </div>
        </div>
      </div>
    </div>
  );
}

// Main Page Component
export default function BestTimePage() {
  const { userState, cycleUserState } = useDemoState();
  const [selectedPlatform, setSelectedPlatform] = useState<string>('YouTube');
  const [showPlatformDropdown, setShowPlatformDropdown] = useState<boolean>(false);
  const [activeGoal, setActiveGoal] = useState<string>('reach');
  const [heatmapData, setHeatmapData] = useState<HeatmapData>(generateHeatmapData());
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [analyticsData, setAnalyticsData] = useState<BestTimeAnalyticsData | null>(null);
  const [selectedDateRange, setSelectedDateRange] = useState<string>('Last 30 days');
  
  const { platforms } = usePlatform();
  const { channelInfo: youtubeChannel, isLoading: isYouTubeLoading } = useYouTubeChannel();
  
  const channelSelectorRef = useRef<HTMLDivElement>(null);
  const initialPlatformSetRef = useRef<boolean>(false);
  
  // Filter connected platforms
  const connectedPlatforms = platforms.filter(p => p.status === 'connected');
  const hasConnectedPlatforms = connectedPlatforms.length > 0;

  // Memoize real connected platforms for the dropdown
  const realConnectedPlatforms = useMemo(() => {
    const realPlatforms = [...connectedPlatforms];
    
    // Add YouTube as the first platform if connected
    if (youtubeChannel && youtubeChannel.title) {
      // Check if YouTube is already in the list and remove it to avoid duplicates
      const filteredPlatforms = realPlatforms.filter(p => p.name.toLowerCase() !== 'youtube');
      
      // Add the real YouTube channel
      return [
        { 
          id: 'youtube', 
          name: 'YouTube', 
          status: 'connected',
          // Use real YouTube channel data
          channelId: youtubeChannel.channelId || youtubeChannel.id,
          channelTitle: youtubeChannel.title
        },
        ...filteredPlatforms
      ];
    }
    
    return realPlatforms;
  }, [connectedPlatforms, youtubeChannel]);

  // Set initial platform based on connected platforms
  useEffect(() => {
    const hasYouTube = realConnectedPlatforms.some(
      platform => platform.name.toLowerCase() === 'youtube'
    );
    
    if (hasYouTube && youtubeChannel) {
      setSelectedPlatform('youtube');
      
      // YouTube works best with at least 30 days of data
      // Only set to 30 days if currently on a shorter timeframe
      if (selectedDateRange === 'Last 7 days' || selectedDateRange === 'Last 14 days') {
        setSelectedDateRange('Last 30 days');
      }
    } else if (realConnectedPlatforms.length > 0) {
      setSelectedPlatform(realConnectedPlatforms[0].name);
    }
    
    // Initial data load will happen via the platform change effect
  }, [realConnectedPlatforms, youtubeChannel]);

  // Track the pending timeframes to prevent duplicate requests
  const [pendingTimeframes, setPendingTimeframes] = useState<Record<string, boolean>>({});
  
  // Implement a more robust loadData function with debouncing
  const loadData = useCallback(async (platform: string) => {
    // Skip loading if we're already loading
    if (isLoading) return;
    
    // Create a cache key from platform and date range
    const cacheKey = `${platform.toLowerCase()}_${selectedDateRange}`;
    
    // Skip if this timeframe is already being loaded
    if (pendingTimeframes[cacheKey]) {
      console.log(`Already loading data for ${platform} with timeframe ${selectedDateRange}`);
      return;
    }
    
    // Check session storage for cached data (expires after 30 minutes)
    // Only check in browser environment
    if (typeof window !== 'undefined') {
      const cachedData = sessionStorage.getItem(cacheKey);
      if (cachedData) {
        try {
          const { data, timestamp } = JSON.parse(cachedData);
          const cacheAge = Date.now() - timestamp;
          
          // Use cache if it's less than 30 minutes old
          if (cacheAge < 30 * 60 * 1000) {
            console.log(`Using cached best time data for ${platform} ${selectedDateRange}`);
            setAnalyticsData(data);
            setHeatmapData(data.heatmapData);
            return;
          }
        } catch (e) {
          console.error('Error parsing cached data:', e);
          // Continue with API call if cache parsing fails
        }
      }
    }
    
    setPendingTimeframes(prev => ({ ...prev, [cacheKey]: true }));
    setIsLoading(true);
    setError(null);
    
    try {
      // Convert platform name to lowercase for API call
      const platformKey = platform.toLowerCase();
      
      // Convert date range to timeframe format expected by the API
      let timeframe = selectedDateRange.toLowerCase();
      
      console.log(`Fetching best time data for ${platformKey} with timeframe: ${timeframe}`);
      
      const data = await fetchBestTimeData(platformKey, timeframe);
      
      // Save to state
      setAnalyticsData(data);
      setHeatmapData(data.heatmapData);
      
      // Cache the result in session storage
      try {
        if (typeof window !== 'undefined') {
          sessionStorage.setItem(cacheKey, JSON.stringify({
            data,
            timestamp: Date.now()
          }));
        }
      } catch (e) {
        console.error('Error caching data:', e);
        // Non-critical error, continue without caching
      }
    } catch (err) {
      console.error('Error loading best time data:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to load best time data. Please try again.';
      setError(errorMessage);
      
      // If this is the first load and we have no data at all, use fallback data
      // to prevent UI from breaking completely
      if (!analyticsData) {
        console.log('No previous data available, using platform-specific fallback data');
        // Use valid dataset keys only to avoid TypeScript errors
        const platformKey = selectedPlatform.toLowerCase();
        const fallbackData = 
          platformKey === 'youtube' ? dataSets.youtube : 
          platformKey === 'facebook' ? dataSets.facebook : 
          platformKey === 'twitter' ? dataSets.twitter : 
          dataSets.instagram;
        
        setAnalyticsData(fallbackData);
        setHeatmapData(fallbackData.heatmapData);
      } else {
        console.log('Keeping previous data visible while showing error message');
        // Keep the existing data visible but show error message
      }
    } finally {
      setIsLoading(false);
      setPendingTimeframes(prev => ({ ...prev, [cacheKey]: false }));
    }
  }, [selectedDateRange, selectedPlatform]);

  // Load data when platform changes but use appropriate delay for initial load
  useEffect(() => {
    if (userState === 'connected' && selectedPlatform) {
      // Prevent excessive loading on initial mount
      // Delay the first load slightly to allow the component to render
      const timer = setTimeout(() => {
        loadData(selectedPlatform);
      }, 300); // Increased from 100ms to 300ms for more stability
      return () => clearTimeout(timer);
    }
  }, [userState, selectedPlatform]);

  // Separate effect for date range changes with longer debouncing
  useEffect(() => {
    if (userState === 'connected' && selectedPlatform && !isLoading) {
      // Use debounce for date range changes to prevent rapid API calls
      const timer = setTimeout(() => {
        loadData(selectedPlatform);
      }, 800); // Increased from 300ms to 800ms for date range changes
      return () => clearTimeout(timer);
    }
  }, [selectedDateRange]);

  // Close popups when clicking outside
  const handleClickOutside = (e: MouseEvent) => {
    if (channelSelectorRef.current && !channelSelectorRef.current.contains(e.target as Node)) {
      setShowPlatformDropdown(false);
    }
  };

  // Add event listener for clicks outside popups
  useEffect(() => {
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Get current goal content
  const currentGoalContent = analyticsData?.goalContent?.[activeGoal] || goalContent[activeGoal];

  // Render different content based on user state
  const renderContent = () => {
    switch (userState) {
      case 'new':
        return <NewUserState />;
      case 'no-accounts':
        return <NoAccountsState />;
      case 'connected':
      default:
        return <ConnectedState
          selectedPlatform={selectedPlatform}
          setSelectedPlatform={setSelectedPlatform}
          showPlatformDropdown={showPlatformDropdown}
          setShowPlatformDropdown={setShowPlatformDropdown}
          heatmapData={heatmapData}
          activeGoal={activeGoal}
          setActiveGoal={setActiveGoal}
          isLoading={isLoading}
          error={error}
          onRetry={() => loadData(selectedPlatform)}
          analyticsData={analyticsData}
          youtubeChannel={youtubeChannel}
          realConnectedPlatforms={realConnectedPlatforms}
          selectedDateRange={selectedDateRange}
          setSelectedDateRange={setSelectedDateRange}
        />;
    }
  };

  return (
    <ErrorBoundary>
      <AnalyzeLayout>
        <div className="space-y-6 p-4">
          <h1 className="text-2xl font-bold">Best time to publish</h1>
          
          {renderContent()}
        </div>
      </AnalyzeLayout>
    </ErrorBoundary>
  );
}

// New User State Component
function NewUserState() {
  return (
    <div className="mt-8 text-center">
      <div className="inline-flex items-center justify-center w-24 h-24 bg-blue-100 text-blue-600 rounded-full mb-4">
        <LucideBarChart size={40} />
      </div>
      <h2 className="text-xl font-semibold mb-2">Connect your first account</h2>
      <p className="text-gray-600 mb-6 max-w-md mx-auto">
        Connect your social media accounts to see the optimal times to post for maximum engagement.
      </p>
      <Link 
        href="/settings/accounts" 
        className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
      >
        Connect accounts
        <ArrowRight size={16} className="ml-2" />
      </Link>
    </div>
  )
}

// No Accounts State Component
function NoAccountsState() {
  return (
    <div className="mt-8 text-center">
      <div className="inline-flex items-center justify-center w-24 h-24 bg-amber-100 text-amber-600 rounded-full mb-4">
        <LucideBarChart size={40} />
      </div>
      <h2 className="text-xl font-semibold mb-2">No accounts connected</h2>
      <p className="text-gray-600 mb-6 max-w-md mx-auto">
        Connect your social media accounts to see the optimal times to post for maximum engagement.
      </p>
      <Link 
        href="/settings/accounts" 
        className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
      >
        Connect accounts
        <ArrowRight size={16} className="ml-2" />
      </Link>
    </div>
  )
}

// Generate best days data from heatmap
const generateBestDaysData = (heatmapData: HeatmapData, dailyPerformance?: BestTimeAnalyticsData['dailyPerformance']) => {
  const days = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
  
  // If dailyPerformance is available, use it directly for more accurate day data
  if (dailyPerformance) {
    return days.map(day => {
      const dayData = dailyPerformance[day];
      // Calculate value based on views and engagement
      const rawValue = dayData ? (dayData.views * 0.3 + dayData.engagement * 0.7) / 100 : 0;
      
      // Find the max value for normalization
      const maxValue = Math.max(...days.map(d => {
        const data = dailyPerformance[d];
        return data ? (data.views * 0.3 + data.engagement * 0.7) / 100 : 0;
      }));
      
      // Normalize to 0-5 scale similar to heatmap values
      const normalizedValue = maxValue > 0 ? (rawValue / maxValue) * 5 : 0;
      
      return {
        name: day,
        value: Math.min(100, Math.max(0, normalizedValue * 20)), // Scale for visualization (0-100)
        rawValue: normalizedValue,
        optimal: dayData && dayData.engagement > 0 && 
                (day === 'Wed' || day === 'Thu' || day === 'Sun' || // Common high-engagement days
                 normalizedValue > 3.5) // Or any day with high value
      };
    });
  }
  
  // Fallback to using the heatmap data if dailyPerformance is not available
  // Calculate average value for each day
  const dayAverages = days.map(day => {
    const hours = Object.keys(heatmapData[day] || {});
    const values = hours.map(hour => heatmapData[day][hour] || 0);
    const sum = values.reduce((acc, val) => acc + val, 0);
    const avg = sum / (hours.length || 1);
    
    return {
      name: day,
      value: Math.min(100, Math.max(0, avg * 20)), // Scale for visualization
      rawValue: avg,
      optimal: day === 'Wed' || day === 'Mon' // Mark optimal days
    };
  });
  
  return dayAverages;
};

// Generate best times data from heatmap
const generateBestTimesData = (heatmapData: HeatmapData) => {
  const hours = Array.from({ length: 24 }, (_, i) => i.toString().padStart(2, '0'));
  
  // Calculate average value for each hour across all days
  const hourAverages = hours.map(hour => {
    const days = Object.keys(heatmapData);
    const values = days.map(day => heatmapData[day][hour] || 0);
    const sum = values.reduce((acc, val) => acc + val, 0);
    const avg = sum / (days.length || 1);
    
    // Format hour for display (e.g., "08" -> "8 AM", "13" -> "1 PM")
    const hourNum = parseInt(hour);
    const displayHour = hourNum === 0 ? '12 AM' : 
                        hourNum < 12 ? `${hourNum} AM` : 
                        hourNum === 12 ? '12 PM' : 
                        `${hourNum - 12} PM`;
    
    return {
      name: displayHour,
      hour: hour,
      value: Math.min(100, Math.max(0, avg * 20)), // Scale for visualization
      rawValue: avg,
      optimal: hour === '23' || hour === '17' || hour === '12' // Mark optimal hours
    };
  });
  
  return hourAverages;
};

// Helper function to get the recommended hour for a specific day
const getRecommendedHourForDay = (day: string): string => {
  // These values would ideally come from analytics data
  // For now using common engagement patterns
  const recommendedHours: Record<string, string> = {
    'Mon': '18', // 6 PM
    'Tue': '19', // 7 PM
    'Wed': '20', // 8 PM
    'Thu': '18', // 6 PM
    'Fri': '17', // 5 PM
    'Sat': '11', // 11 AM
    'Sun': '15'  // 3 PM
  };
  
  return recommendedHours[day] || '19'; // Default to 7 PM if no data for day
};

// Generate recommendations from heatmap
const generateRecommendations = (heatmapData: HeatmapData, dailyPerformance?: BestTimeAnalyticsData['dailyPerformance']) => {
  const days = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
  const daysFull = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
  
  // If we have daily performance data, use that for more accurate recommendations
  if (dailyPerformance) {
    // Create recommendations based on daily performance data
    const dayPerformanceList = days.map((day, index) => {
      const dayData = dailyPerformance[day];
      if (!dayData) return null;
      
      // Calculate a combined value from engagement and views
      const combinedValue = (dayData.engagement * 0.7) + (dayData.views * 0.3);
      
      return {
        day: daysFull[index],
        shortDay: day,
        hour: getRecommendedHourForDay(day), // Get the best hour for this day
        value: combinedValue,
        people: Math.floor((dayData.engagement + dayData.views) / 2)
      };
    }).filter(Boolean);
    
    // Sort by combined value (highest first) and take top 3
    dayPerformanceList.sort((a, b) => b!.value - a!.value);
    const topPerformers = dayPerformanceList.slice(0, 3);
    
    // Format recommendations
    return topPerformers.map(spot => {
      const hourNum = parseInt(spot!.hour);
      const time = hourNum === 0 ? '12:00 AM' : 
                   hourNum < 12 ? `${hourNum}:00 AM` : 
                   hourNum === 12 ? '12:00 PM' : 
                   `${hourNum - 12}:00 PM`;
      
      return {
        day: spot!.day,
        shortDay: spot!.shortDay,
        hour: spot!.hour,
        time: time,
        people: spot!.people
      };
    });
  }
  
  // Fallback to heatmap data if no daily performance data is available
  // Find top hotspots
  const hotspots: Array<{day: string; shortDay: string; hour: string; value: number; people: number}> = [];
  
  days.forEach((day, dayIndex) => {
    Object.keys(heatmapData[day] || {}).forEach(hour => {
      const value = heatmapData[day][hour] || 0;
      if (value > 0) {
        hotspots.push({
          day: daysFull[dayIndex],
          shortDay: day,
          hour: hour,
          value: value,
          people: Math.floor(value * 10) // Simulate number of people online
        });
      }
    });
  });
  
  // Sort by value (highest first) and take top 3
  hotspots.sort((a, b) => b.value - a.value);
  const topHotspots = hotspots.slice(0, 3);
  
  // Format recommendations
  return topHotspots.map(spot => {
    const hourNum = parseInt(spot.hour);
    const time = hourNum === 0 ? '12:00 AM' : 
                 hourNum < 12 ? `${hourNum}:00 AM` : 
                 hourNum === 12 ? '12:00 PM' : 
                 `${hourNum - 12}:00 PM`;
    
    return {
      day: spot.day,
      shortDay: spot.shortDay,
      hour: spot.hour,
      time: time,
      people: spot.people
    };
  });
};

// Add this custom tooltip component for the bar charts
const CustomTooltip = ({ active, payload, label, formatter }: any) => {
  if (active && payload && payload.length > 0) {
    return (
      <div className="bg-white p-2 border border-gray-200 shadow-md rounded-md">
        <p className="font-medium text-sm">{`${label}`}</p>
        <p className="text-sm text-blue-600">
          {formatter 
            ? formatter(payload[0].value, payload[0].dataKey, payload)
            : payload[0].value}
        </p>
      </div>
    );
  }
  return null;
};

// MetricCard Component
interface MetricCardProps {
  title: string;
  value: string;
  change: string;
  isPositive: boolean;
}

function MetricCard({ title, value, change, isPositive }: MetricCardProps) {
  return (
    <div className="bg-white p-6 rounded-xl border border-gray-200 shadow-sm hover:shadow-md transition-all duration-300">
      <h3 className="text-base font-semibold text-gray-700 mb-3">{title}</h3>
      <div className="flex items-end justify-between">
        <p className="text-3xl font-bold text-gray-900">{value}</p>
        <div className={`flex items-center ${isPositive ? 'text-green-600' : 'text-red-600'} px-2 py-1 rounded-full ${isPositive ? 'bg-green-50' : 'bg-red-50'}`}>
          {isPositive ? (
            <ArrowUp size={16} className="mr-1" />
          ) : (
            <ArrowDown size={16} className="mr-1" />
          )}
          <span className="font-medium">{change}</span>
        </div>
      </div>
    </div>
  );
}

// Loading Component
function LoadingState() {
  return (
    <div className="flex flex-col items-center justify-center py-12">
      <div className="w-16 h-16 border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin mb-4"></div>
      <p className="text-gray-600 font-medium">Loading best time data...</p>
    </div>
  );
}

// Error Component
function ErrorState({ message, onRetry }: { message: string, onRetry: () => void }) {
  const [countdownSeconds, setCountdownSeconds] = useState<number | null>(null);
  
  // Extract countdown seconds from error message if present
  useEffect(() => {
    if (message.includes('try again in ')) {
      const match = message.match(/try again in (\d+) seconds/i);
      if (match && match[1]) {
        setCountdownSeconds(parseInt(match[1], 10));
      }
    }
  }, [message]);
  
  // Countdown timer
  useEffect(() => {
    if (!countdownSeconds) return;
    
    const timer = setInterval(() => {
      setCountdownSeconds(prev => {
        if (prev === null || prev <= 1) {
          clearInterval(timer);
          return null;
        }
        return prev - 1;
      });
    }, 1000);
    
    return () => clearInterval(timer);
  }, [countdownSeconds]);
  
  return (
    <div className="flex flex-col items-center justify-center py-16 px-4 text-center">
      <AlertTriangle size={48} className="text-red-500 mb-4" />
      <h3 className="text-xl font-semibold text-gray-800 mb-3">Unable to load data</h3>
      <p className="text-gray-600 mb-6 max-w-md">{message}</p>
      
      {countdownSeconds ? (
        <div className="mb-6">
          <p className="text-sm text-gray-500 mb-2">Next attempt available in:</p>
          <div className="text-xl font-bold text-blue-600">{countdownSeconds} seconds</div>
        </div>
      ) : (
        <button
          onClick={onRetry}
          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200"
        >
          Try Again
        </button>
      )}
    </div>
  );
}

// Platform Icon Component
interface PlatformIconProps {
  platform: string;
  size?: number;
  className?: string;
}

function PlatformIcon({ platform, size = 16, className = '' }: PlatformIconProps) {
  const iconProps = {
    size,
    className
  };

  switch (platform.toLowerCase()) {
    case 'youtube':
      return <Youtube {...iconProps} />;
    case 'instagram':
      return <Users {...iconProps} />; // Using Users as a placeholder for Instagram
    case 'facebook':
      return <Users {...iconProps} />; // Using Users as a placeholder for Facebook
    case 'twitter':
    case 'x':
      return <Users {...iconProps} />; // Using Users as a placeholder for Twitter/X
    case 'linkedin':
      return <Users {...iconProps} />; // Using Users as a placeholder for LinkedIn
    case 'all':
      return <Users {...iconProps} />; // Using Users as a placeholder for "All platforms"
    default:
      return <Users {...iconProps} />;
  }
}

// Function to get platform color
function getPlatformColor(platform: string): string {
  switch (platform.toLowerCase()) {
    case 'youtube':
      return 'bg-red-600';
    case 'instagram':
      return 'bg-pink-600';
    case 'facebook':
      return 'bg-blue-600';
    case 'twitter':
    case 'x':
      return 'bg-blue-400';
    case 'linkedin':
      return 'bg-blue-800';
    case 'all':
      return 'bg-gray-600';
    default:
      return 'bg-gray-500';
  }
}