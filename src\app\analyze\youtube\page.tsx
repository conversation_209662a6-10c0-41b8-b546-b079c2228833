'use client'

import { useState, useEffect } from 'react'
import { Youtube, BarChart2, <PERSON><PERSON>ding<PERSON>p, Eye, ThumbsUp, Users, Clock } from 'lucide-react'
import { useGoogleAuth } from '@/lib/hooks/useGoogleAuth'
import ConnectYouTube from '@/components/ConnectYouTube'

type YouTubeChannel = {
  id: string;
  channelId: string;
  title: string;
  thumbnail: string;
  description?: string;
  customUrl?: string;
}

export default function YouTubeAnalyticsPage() {
  const [isLoading, setIsLoading] = useState(true)
  const [channelInfo, setChannelInfo] = useState<YouTubeChannel | null>(null)
  const { isAuthenticated } = useGoogleAuth()

  useEffect(() => {
    if (isAuthenticated) {
      fetchYouTubeChannelInfo()
    } else {
      setIsLoading(false)
    }
  }, [isAuthenticated])

  const fetchYouTubeChannelInfo = async () => {
    try {
      setIsLoading(true)
      const response = await fetch('/api/youtube/channel-info', {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        },
        credentials: 'include'
      })

      if (response.ok) {
        const data = await response.json()
        setChannelInfo(data.channel)
      }
    } catch (error) {
      console.error('Error fetching YouTube channel:', error)
    } finally {
      setIsLoading(false)
    }
  }

  if (isLoading) {
    return (
      <div className="container max-w-7xl mx-auto py-8">
        <div className="animate-pulse">Loading YouTube analytics...</div>
      </div>
    )
  }

  if (!channelInfo) {
    return (
      <div className="container max-w-7xl mx-auto py-8">
        <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200">
          <div className="flex flex-col items-center justify-center py-12">
            <div className="w-16 h-16 bg-red-600 rounded-full flex items-center justify-center mb-4">
              <Youtube size={32} className="text-white" />
            </div>
            <h2 className="text-2xl font-bold mb-2">Connect Your YouTube Channel</h2>
            <p className="text-gray-600 mb-8 text-center max-w-md">
              Connect your YouTube channel to view analytics, manage content, and track performance.
            </p>
            <ConnectYouTube
              onSuccess={fetchYouTubeChannelInfo}
              onFail={(error) => console.error('YouTube connection failed:', error)}
              variant="default"
              size="lg"
            />
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="container max-w-7xl mx-auto py-8">
      <div className="flex items-center mb-8">
        <div className="w-12 h-12 bg-red-600 rounded-full flex items-center justify-center mr-4">
          <Youtube size={24} className="text-white" />
        </div>
        <div>
          <h1 className="text-2xl font-bold">YouTube Analytics</h1>
          <p className="text-gray-600">{channelInfo.title}</p>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
        <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center mb-2">
            <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-2">
              <TrendingUp size={16} className="text-blue-600" />
            </div>
            <h3 className="font-medium">Growth</h3>
          </div>
          <p className="text-2xl font-bold">+5.3%</p>
          <p className="text-sm text-gray-600">from last month</p>
        </div>
        
        <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center mb-2">
            <div className="w-8 h-8 bg-indigo-100 rounded-full flex items-center justify-center mr-2">
              <Eye size={16} className="text-indigo-600" />
            </div>
            <h3 className="font-medium">Views</h3>
          </div>
          <p className="text-2xl font-bold">124,582</p>
          <p className="text-sm text-gray-600">last 28 days</p>
        </div>
        
        <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center mb-2">
            <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center mr-2">
              <Users size={16} className="text-green-600" />
            </div>
            <h3 className="font-medium">Subscribers</h3>
          </div>
          <p className="text-2xl font-bold">45,210</p>
          <p className="text-sm text-gray-600">+124 this week</p>
        </div>
        
        <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center mb-2">
            <div className="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center mr-2">
              <Clock size={16} className="text-red-600" />
            </div>
            <h3 className="font-medium">Watch Time</h3>
          </div>
          <p className="text-2xl font-bold">8,542 hrs</p>
          <p className="text-sm text-gray-600">last 28 days</p>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <h2 className="text-lg font-semibold mb-4">Performance Overview</h2>
          <div className="h-64 w-full bg-gray-50 flex items-center justify-center rounded border border-gray-200">
            <p className="text-gray-400">Chart Visualization</p>
          </div>
        </div>
        
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <h2 className="text-lg font-semibold mb-4">Top Videos</h2>
          <div className="space-y-3">
            <div className="flex items-center p-2 hover:bg-gray-50 rounded">
              <div className="w-16 h-9 bg-gray-200 rounded mr-3"></div>
              <div className="flex-1 min-w-0">
                <p className="font-medium text-sm truncate">How to Build a Next.js Website</p>
                <p className="text-xs text-gray-500">12.4K views • 3 days ago</p>
              </div>
              <div className="text-sm font-medium text-gray-900">18% CTR</div>
            </div>
            
            <div className="flex items-center p-2 hover:bg-gray-50 rounded">
              <div className="w-16 h-9 bg-gray-200 rounded mr-3"></div>
              <div className="flex-1 min-w-0">
                <p className="font-medium text-sm truncate">React Performance Tips</p>
                <p className="text-xs text-gray-500">8.7K views • 1 week ago</p>
              </div>
              <div className="text-sm font-medium text-gray-900">15% CTR</div>
            </div>
            
            <div className="flex items-center p-2 hover:bg-gray-50 rounded">
              <div className="w-16 h-9 bg-gray-200 rounded mr-3"></div>
              <div className="flex-1 min-w-0">
                <p className="font-medium text-sm truncate">CSS Grid Explained</p>
                <p className="text-xs text-gray-500">6.3K views • 2 weeks ago</p>
              </div>
              <div className="text-sm font-medium text-gray-900">12% CTR</div>
            </div>
          </div>
        </div>
      </div>

      <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
        <h2 className="text-lg font-semibold mb-4">Audience Demographics</h2>
        <div className="h-80 w-full bg-gray-50 flex items-center justify-center rounded border border-gray-200">
          <p className="text-gray-400">Demographics Visualization</p>
        </div>
      </div>
    </div>
  )
} 