import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { google } from 'googleapis';

// Define all YouTube scopes we support
const YOUTUBE_SCOPES = {
  publishing: [
    'https://www.googleapis.com/auth/youtube.readonly',
    'https://www.googleapis.com/auth/youtube',
    'https://www.googleapis.com/auth/youtube.upload',
    'https://www.googleapis.com/auth/youtubepartner',
  ],
  analytics: [
    'https://www.googleapis.com/auth/yt-analytics.readonly',
    'https://www.googleapis.com/auth/yt-analytics-monetary.readonly'
  ]
};

/**
 * Google OAuth callback handler for direct YouTube integration
 * This handler receives the OAuth code, processes it entirely server-side,
 * and redirects directly to the publish page with success/error parameters.
 */
export async function GET(request: NextRequest) {
  console.log('[YouTube Direct Callback] Processing callback');
  
  // Get auth code from query params
  const searchParams = request.nextUrl.searchParams;
  const code = searchParams.get('code');
  const state = searchParams.get('state');
  const error = searchParams.get('error');
  
  // Create Supabase client
  const supabase = createClient();
  
  // Handle OAuth errors
  if (error) {
    console.error(`[YouTube Direct Callback] OAuth error: ${error}`);
    return NextResponse.redirect(new URL('/settings?error=youtube-auth-failed', request.url));
  }
  
  // Validate code and state
  if (!code || !state) {
    console.error('[YouTube Direct Callback] Missing code or state parameter');
    return NextResponse.redirect(new URL('/settings?error=missing-params', request.url));
  }
  
  try {
    // Parse state for redirect URI and user ID
    let redirectUri = '/publish';  // Default to /publish instead of /settings
    let userId = null;
    let includeAnalytics = true; // Default to including analytics scopes
    
    try {
      const stateObj = JSON.parse(decodeURIComponent(state));
      
      // Handle both redirectUri and return_to for backward compatibility
      redirectUri = stateObj.redirectUri || stateObj.return_to || redirectUri;
      
      // Handle both userId and user_id for backward compatibility
      userId = stateObj.userId || stateObj.user_id || null;
      
      // Check if analytics flag is explicitly set
      if (stateObj.hasOwnProperty('includeAnalytics')) {
        includeAnalytics = Boolean(stateObj.includeAnalytics);
      }
      
      console.log(`[YouTube Direct Callback] State parsed: redirectUri=${redirectUri}, userId=${userId}, includeAnalytics=${includeAnalytics}`);
    } catch (e) {
      console.error('[YouTube Direct Callback] Error parsing state:', e);
      // Continue with defaults if state parsing fails
    }
    
    // If userId not in state, get from session
    if (!userId) {
      const { data: { session } } = await supabase.auth.getSession();
      if (!session) {
        console.error('[YouTube Direct Callback] No session found and no userId in state');
        return NextResponse.redirect(new URL('/login?error=no-session', request.url));
      }
      userId = session.user.id;
    }
    
    console.log(`[YouTube Direct Callback] Processing for user: ${userId}`);
    
    // IMPORTANT: This must match EXACTLY what's configured in Google Cloud Console
    // This is the critical part for fixing the redirect_uri_mismatch error
    const callbackUri = process.env.NEXT_PUBLIC_URL 
      ? `${process.env.NEXT_PUBLIC_URL}/api/auth/youtube-direct-callback`
      : 'http://localhost:3002/api/auth/youtube-direct-callback';
    
    console.log('[YouTube Direct Callback] Using callback URI:', callbackUri);
    
    // Initialize OAuth2 client with the same callback URI as the auth endpoint
    const oauth2Client = new google.auth.OAuth2(
      process.env.GOOGLE_CLIENT_ID,
      process.env.GOOGLE_CLIENT_SECRET,
      callbackUri
    );
    
    // Exchange code for tokens
    const { tokens } = await oauth2Client.getToken(code);
    console.log('[YouTube Direct Callback] Received tokens:', {
      access_token: tokens.access_token ? 'PRESENT' : 'MISSING',
      refresh_token: tokens.refresh_token ? 'PRESENT' : 'MISSING',
      expiry_date: tokens.expiry_date || 'MISSING'
    });
    
    // Set the credentials on the OAuth2 client
    oauth2Client.setCredentials(tokens);
    
    // Get scopes from token info
    const tokenInfo = await oauth2Client.getTokenInfo(tokens.access_token || '');
    const receivedScopes = tokenInfo.scopes || [];
    
    console.log('[YouTube Direct Callback] Received scopes:', receivedScopes);
    
    // Check if we have the required scopes
    const requiredScopes = [...YOUTUBE_SCOPES.publishing];
    if (includeAnalytics) {
      requiredScopes.push(...YOUTUBE_SCOPES.analytics);
    }
    
    const missingScopes = requiredScopes.filter(scope => !receivedScopes.includes(scope));
    if (missingScopes.length > 0) {
      console.warn('[YouTube Direct Callback] Missing scopes:', missingScopes);
      // We'll continue anyway, but log the missing scopes for debugging
    }
    
    // Get user info from Google
    const oauth2 = google.oauth2({
      auth: oauth2Client,
      version: 'v2'
    });
    
    const userInfo = await oauth2.userinfo.get();
    const googleUserId = userInfo.data.id;
    const googleEmail = userInfo.data.email;
    
    console.log(`[YouTube Direct Callback] Google user: ${googleEmail} (${googleUserId})`);
    
    // Initialize YouTube API for channel validation
    const youtube = google.youtube({
      version: 'v3',
      auth: oauth2Client
    });
    
    // Get YouTube channel info
    const { data: channelsData } = await youtube.channels.list({
      part: ['snippet,contentDetails,statistics'],
      mine: true
    });
    
    if (!channelsData.items || channelsData.items.length === 0) {
      console.error('[YouTube Direct Callback] No YouTube channel found for this Google account');
      return NextResponse.redirect(new URL(`${redirectUri}?error=no-youtube-channel`, request.url));
    }
    
    const channelInfo = channelsData.items[0];
    const channelId = channelInfo.id;
    const channelTitle = channelInfo.snippet?.title;
    
    console.log(`[YouTube Direct Callback] YouTube channel: ${channelTitle} (${channelId})`);
    
    // Check if this channel is already connected
    const { data: existingConnection } = await supabase
      .from('connected_accounts')
      .select('id, platform_account_name')
      .eq('user_id', userId)
      .eq('provider', 'youtube')
      .eq('platform_account_id', channelId)
      .single();

    // Save token to database using Supabase functions
    const saveTokenResult = await supabase.rpc('create_youtube_token_connection', {
      p_user_id: userId,
      p_access_token: tokens.access_token || '',
      p_refresh_token: tokens.refresh_token || '',
      p_expires_at: tokens.expiry_date ? new Date(tokens.expiry_date).toISOString() : null,
      p_google_user_id: googleUserId,
      p_google_email: googleEmail,
      p_youtube_channel_id: channelId,
      p_youtube_channel_title: channelTitle || '',
      p_scopes: receivedScopes.join(' '),
      p_service_type: 'youtube'
    });

    if (saveTokenResult.error) {
      console.error('[YouTube Direct Callback] Error saving token:', saveTokenResult.error);
      return NextResponse.redirect(new URL(`${redirectUri}?error=token-save-failed&message=${encodeURIComponent('Failed to save connection to database')}`, request.url));
    }

    console.log('[YouTube Direct Callback] Token saved successfully');

    // Determine the appropriate success message
    const isReconnection = existingConnection !== null;
    const successParam = isReconnection ? 'youtube-reconnected' : 'youtube-connected';
    const messageParam = isReconnection
      ? `Channel "${channelTitle}" has been reconnected successfully`
      : `Channel "${channelTitle}" has been connected successfully`;

    // Redirect to the provided redirect URI with success status
    return NextResponse.redirect(new URL(`${redirectUri}?success=${successParam}&message=${encodeURIComponent(messageParam)}`, request.url));
    
  } catch (error) {
    console.error('[YouTube Direct Callback] Error processing callback:', error);
    return NextResponse.redirect(new URL('/publish?error=youtube-auth-failed', request.url));
  }
} 