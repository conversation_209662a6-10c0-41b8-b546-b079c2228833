/**
 * API v1 - Facebook Pages Management Route
 * 
 * Handles Facebook page selection and integration management
 */

import { NextRequest } from 'next/server';
import { FacebookService } from '@/lib/services/facebookService';
import { 
  with<PERSON><PERSON>r<PERSON><PERSON><PERSON>, 
  requireAuth,
  ErrorFactory
} from '@/lib/error-handler';
import {
  validateRequestBody,
  validateQueryParams
} from '@/lib/validation';
import { withVersioning } from '@/lib/versioning/middleware';
import { z } from 'zod';

// ============================================================================
// Validation Schemas
// ============================================================================

const SelectPageSchema = z.object({
  page_id: z.string().min(1, 'Page ID is required'),
  access_token: z.string().min(1, 'Access token is required')
}).strict();

const GetPagesQuerySchema = z.object({
  access_token: z.string().min(1, 'Access token is required')
}).strict();

// ============================================================================
// Route Handlers
// ============================================================================

/**
 * Internal handler for selecting a Facebook page
 */
async function selectFacebookPageHandler(req: NextRequest) {
  // 1. Authenticate user
  const { user } = await requireAuth(req);

  // 2. Validate request body
  const validatedData = await validateRequestBody(req, SelectPageSchema);

  // 3. Initialize Facebook service
  const { facebookService } = await import('@/lib/services');

  // 4. Get all pages to find the selected one
  const pagesResult = await facebookService.getPages(validatedData.access_token);

  if (!pagesResult.success) {
    throw pagesResult.error;
  }

  const pages = pagesResult.data!;
  const selectedPage = pages.find(page => page.id === validatedData.page_id);

  if (!selectedPage) {
    throw ErrorFactory.notFound('Facebook page', validatedData.page_id);
  }

  // 5. Store the page integration
  const storeResult = await facebookService.storePageIntegration(
    user.id, 
    selectedPage, 
    validatedData.access_token
  );

  if (!storeResult.success) {
    throw storeResult.error;
  }

  // 6. Return success response
  return {
    success: true,
    data: {
      integration_id: storeResult.data,
      page: {
        id: selectedPage.id,
        name: selectedPage.name,
        category: selectedPage.category,
        picture: selectedPage.picture,
        fan_count: selectedPage.fan_count
      }
    },
    metadata: {
      provider: 'facebook',
      connected_at: new Date().toISOString()
    }
  };
}

/**
 * Internal handler for getting Facebook pages
 */
async function getFacebookPagesHandler(req: NextRequest) {
  // 1. Authenticate user
  const { user } = await requireAuth(req);

  // 2. Validate query parameters
  const queryParams = validateQueryParams(req, GetPagesQuerySchema);

  // 3. Initialize Facebook service
  const { facebookService } = await import('@/lib/services');

  // 4. Get user's Facebook pages
  const pagesResult = await facebookService.getPages(queryParams.access_token);

  if (!pagesResult.success) {
    throw pagesResult.error;
  }

  // 5. Return pages data
  return {
    success: true,
    data: pagesResult.data!.map(page => ({
      id: page.id,
      name: page.name,
      category: page.category,
      picture: page.picture,
      fan_count: page.fan_count,
      about: page.about,
      website: page.website
    })),
    metadata: {
      provider: 'facebook',
      total_pages: pagesResult.data!.length
    }
  };
}

/**
 * POST /api/v1/integrations/facebook/pages
 * Select and connect a Facebook page
 */
export const POST = withVersioning(withErrorHandler(selectFacebookPageHandler));

/**
 * GET /api/v1/integrations/facebook/pages
 * Get available Facebook pages
 */
export const GET = withVersioning(withErrorHandler(getFacebookPagesHandler));
