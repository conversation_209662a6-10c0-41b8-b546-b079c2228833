import { NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { google } from 'googleapis';

// Simple in-memory rate limiting
// Maps user IDs to timestamps of their last request for each timeframe
const lastRequestTimes: Record<string, Record<string, number>> = {};

// Different rate limits based on timeframe
const RATE_LIMIT_INTERVALS: Record<string, number> = {
  'Last 7 days': 60000,      // 1 minute
  'Last 14 days': 120000,    // 2 minutes
  'Last 30 days': 300000,    // 5 minutes (default)
  'Last 90 days': 600000,    // 10 minutes
  'Last 6 months': 900000,   // 15 minutes
  'default': 300000          // Default is 5 minutes
};

// Helper function to create an empty heatmap data structure
function createEmptyHeatmapData(): { [key: string]: { [key: string]: number } } {
  const emptyHeatmapData: { [key: string]: { [key: string]: number } } = {
    'Mon': {},
    'Tue': {},
    'Wed': {},
    'Thu': {},
    'Fri': {},
    'Sat': {},
    'Sun': {}
  };
  
  // Initialize all days with all hours set to zero
  Object.keys(emptyHeatmapData).forEach(day => {
    for (let i = 0; i < 24; i++) {
      const hour = i.toString().padStart(2, '0');
      emptyHeatmapData[day][hour] = 0;
    }
  });
  
  return emptyHeatmapData;
}

// Define types for day performance data
type DayPerformance = {
  views: number;
  engagement: number;
  watchTime: number;
  count: number;
};

type DailyPerformance = {
  [key: string]: DayPerformance;
  Mon: DayPerformance;
  Tue: DayPerformance;
  Wed: DayPerformance;
  Thu: DayPerformance;
  Fri: DayPerformance;
  Sat: DayPerformance;
  Sun: DayPerformance;
};

export async function GET(request: Request) {
  try {
    const url = new URL(request.url);
    let timeframe = url.searchParams.get('timeframe') || 'Last 30 days';
    
    // Normalize timeframe parameter
    timeframe = timeframe.toLowerCase().trim();
    
    // Convert to standard format
    if (timeframe.includes('7') || timeframe.includes('week')) {
      timeframe = 'Last 7 days';
    } else if (timeframe.includes('14') || timeframe.includes('2 week')) {
      timeframe = 'Last 14 days';
    } else if (timeframe.includes('30') || timeframe.includes('month')) {
      timeframe = 'Last 30 days';
    } else if (timeframe.includes('90') || timeframe.includes('3 month')) {
      timeframe = 'Last 90 days';
    } else if (timeframe.includes('6') && timeframe.includes('month')) {
      timeframe = 'Last 6 months';
    } else {
      // Default to 30 days if unrecognized
      timeframe = 'Last 30 days';
    }
    
    console.log(`Using normalized timeframe: ${timeframe}`);
    
    // Initialize Supabase client
    const supabase = createClient();

    // Get the user session to ensure they're logged in
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    
    if (sessionError || !session) {
      return NextResponse.json(
        { error: 'Not authenticated', details: sessionError?.message },
        { status: 401 }
      );
    }
    
    // Apply rate limiting
    const userId = session.user.id;
    const currentTime = Date.now();
    const lastRequestTime = lastRequestTimes[userId] && lastRequestTimes[userId][timeframe] || 0;
    const timeSinceLastRequest = currentTime - lastRequestTime;
    
    // If the user has made a request recently, return cached data or error
    if (lastRequestTime && timeSinceLastRequest < RATE_LIMIT_INTERVALS[timeframe]) {
      const remainingTime = Math.ceil((RATE_LIMIT_INTERVALS[timeframe] - timeSinceLastRequest) / 1000);
      console.log(`Rate limit applied for user ${userId} and timeframe ${timeframe}. Try again in ${remainingTime} seconds.`);
      
      return NextResponse.json(
        { 
          error: 'Rate limit exceeded', 
          message: `API quota limit reached. Please try again in ${remainingTime} seconds.`,
          timeRemaining: remainingTime,
          timeframe: timeframe,
          statusCode: 429
        },
        { status: 429 }
      );
    }
    
    // Update the last request time
    if (!lastRequestTimes[userId]) {
      lastRequestTimes[userId] = {};
    }
    lastRequestTimes[userId][timeframe] = currentTime;
    
    // Get the YouTube token from google_oauth_tokens
    let tokenData;
    
    try {
      const { data: oauthToken, error: tokenError } = await supabase
        .from('google_oauth_tokens')
        .select('*')
        .eq('user_id', session.user.id)
        .eq('service_type', 'youtube')
        .single();
      
      if (oauthToken && oauthToken.access_token) {
        console.log('Found token in google_oauth_tokens');
        tokenData = oauthToken;
      } else {
        console.log('Token not found in google_oauth_tokens, error:', tokenError);
        
        // Try getting from connected_accounts as fallback
        console.log('Trying connected_accounts table...');
        const { data: connectedAccount, error: connectedError } = await supabase
          .from('connected_accounts')
          .select('*')
          .eq('user_id', session.user.id)
          .eq('provider', 'google_youtube')
          .single();
          
        if (connectedAccount && connectedAccount.access_token) {
          console.log('Found token in connected_accounts');
          tokenData = connectedAccount;
        } else {
          console.error('No tokens found in any table:', connectedError);
          return NextResponse.json(
            { error: 'YouTube token not found. Please connect your YouTube account.' }, 
            { status: 404 }
          );
        }
      }
    } catch (error) {
      console.error('Error retrieving tokens:', error);
      return NextResponse.json(
        { error: 'Error retrieving YouTube tokens. Please try again.' }, 
        { status: 500 }
      );
    }
    
    // Check if we have analytics scopes
    const scopes = tokenData.scope || '';
    const hasAnalyticsAccess = scopes.includes('yt-analytics.readonly');
    
    if (!hasAnalyticsAccess) {
      console.log('YouTube token does not have analytics scope');
      return NextResponse.json(
        { error: 'YouTube analytics access not granted. Please reconnect with analytics permissions.' },
        { status: 403 }
      );
    }
    
    // Create a YouTube API client
    const oauth2Client = new google.auth.OAuth2(
      process.env.GOOGLE_CLIENT_ID,
      process.env.GOOGLE_CLIENT_SECRET,
      process.env.GOOGLE_OAUTH_REDIRECT_URI
    );
    
    oauth2Client.setCredentials({
      access_token: tokenData.access_token,
      refresh_token: tokenData.refresh_token,
    });
    
    // Create YouTube Data API client to get channel info
    const youtube = google.youtube({
      version: 'v3',
      auth: oauth2Client,
    });
    
    // Get channel info first
    const channelResponse = await youtube.channels.list({
      part: ['snippet,statistics'],
      mine: true
    });
    
    if (!channelResponse?.data?.items || channelResponse.data.items.length === 0) {
      return NextResponse.json(
        { error: 'Channel not found' },
        { status: 404 }
      );
    }
    
    const channelId = channelResponse.data.items[0]?.id;
    const channelTitle = channelResponse.data.items[0]?.snippet?.title || 'Unknown Channel';
    
    // Create YouTube Analytics API client
    const youtubeAnalytics = google.youtubeAnalytics({
      version: 'v2',
      auth: oauth2Client
    });
    
    // Calculate date range based on timeframe parameter
    const now = new Date();
    const endDate = now.toISOString().split('T')[0]; // Today's date as YYYY-MM-DD
    
    let startDate = new Date(now);
    let prevStartDate = new Date(now);
    
    switch (timeframe) {
      case 'Last 7 days':
        startDate.setDate(now.getDate() - 7);
        prevStartDate.setDate(now.getDate() - 14);
        break;
      case 'Last 14 days':
        startDate.setDate(now.getDate() - 14);
        prevStartDate.setDate(now.getDate() - 28);
        break;
      case 'Last 30 days':
        startDate.setDate(now.getDate() - 30);
        prevStartDate.setDate(now.getDate() - 60);
        break;
      case 'Last 90 days':
        startDate.setDate(now.getDate() - 90);
        prevStartDate.setDate(now.getDate() - 180);
        break;
      case 'Last 6 months':
        startDate.setMonth(now.getMonth() - 6);
        prevStartDate.setMonth(now.getMonth() - 12);
        break;
      default:
        startDate.setDate(now.getDate() - 30);
        prevStartDate.setDate(now.getDate() - 60);
    }
    
    const startDateString = startDate.toISOString().split('T')[0];
    const prevStartDateString = prevStartDate.toISOString().split('T')[0];
    const prevEndDateString = startDateString; // Previous period ends where current period starts
    
    console.log(`Fetching YouTube best time data for period: ${startDateString} to ${endDate}`);
    
    try {
      // Fetch performance by hour only instead of day and hour
      const analyticsResponse = await youtubeAnalytics.reports.query({
        ids: `channel==${channelId}`,
        startDate: startDateString,
        endDate: endDate,
        metrics: 'views,estimatedMinutesWatched,likes,subscribersGained,comments,shares',
        dimensions: 'day', // Changed from 'hour' to 'day' since hour isn't supported
        sort: '-views'
      });
      
      // Log the structure of the data to debug
      console.log('[API Fetch] YouTube API Raw Response Structure:', 
        analyticsResponse.data.columnHeaders?.map(h => h.name),
        'First few rows:', 
        analyticsResponse.data.rows?.slice(0, 5)
      );
      
      // Check if rows exist before processing
      if (!analyticsResponse.data.rows || analyticsResponse.data.rows.length === 0) {
        console.log('[API Processing] No rows returned from YouTube API.');
        
        // Create empty heatmap data structure
        const emptyHeatmapData = createEmptyHeatmapData();
        
        // Create empty response data with message
        const responseData = {
          heatmapData: emptyHeatmapData,
          dailyPerformance: {
            Mon: { views: 0, engagement: 0 },
            Tue: { views: 0, engagement: 0 },
            Wed: { views: 0, engagement: 0 },
            Thu: { views: 0, engagement: 0 },
            Fri: { views: 0, engagement: 0 },
            Sat: { views: 0, engagement: 0 },
            Sun: { views: 0, engagement: 0 }
          },
          metrics: {
            totalReach: { 
              value: '0', 
              change: '0%', 
              isPositive: false
            },
            engagementRate: { 
              value: '0%', 
              change: '0%', 
              isPositive: false
            },
            avgEngagement: { 
              value: '0', 
              change: '0%', 
              isPositive: false
            },
            lastUpdated: new Date().toLocaleDateString('en-US', { 
              weekday: 'long', 
              month: 'short', 
              day: 'numeric', 
              hour: 'numeric', 
              minute: 'numeric' 
            })
          },
          goalContent: {
            reach: {
              title: "Maximize audience reach",
              description: "Post when your audience is most active to reach the largest number of people",
              metric: "viewers",
              bestTimes: []
            },
            awareness: {
              title: "Build brand awareness",
              description: "These times have the highest viewership, ideal for visibility and impressions",
              metric: "audience size",
              bestTimes: []
            },
            engagement: {
              title: "Boost engagement rates",
              description: "These posting times receive the most likes, comments and shares from your audience",
              metric: "engagement rate",
              bestTimes: []
            },
            traffic: {
              title: "Drive website traffic",
              description: "These times generate the most clicks on your content, perfect for link sharing",
              metric: "click-through rate",
              bestTimes: []
            }
          },
          message: "There was no activity during this period. Try selecting a longer time range."
        };
        
        console.log('[API Response] Returning empty data with message due to no activity');
        return NextResponse.json(responseData);
      }
      
      // Process data
      // Initialize heatmap data structure
      const heatmapData = createEmptyHeatmapData();
      
      // Initialize daily performance data structure
      const dailyPerformance: DailyPerformance = {
        Mon: { views: 0, engagement: 0, watchTime: 0, count: 0 },
        Tue: { views: 0, engagement: 0, watchTime: 0, count: 0 },
        Wed: { views: 0, engagement: 0, watchTime: 0, count: 0 },
        Thu: { views: 0, engagement: 0, watchTime: 0, count: 0 },
        Fri: { views: 0, engagement: 0, watchTime: 0, count: 0 },
        Sat: { views: 0, engagement: 0, watchTime: 0, count: 0 },
        Sun: { views: 0, engagement: 0, watchTime: 0, count: 0 }
      };
      
      // Track total views in the period for activity level detection
      let totalViewsInPeriod = 0;
      
      // Process analytics rows to aggregate daily data
      if (analyticsResponse.data.rows && analyticsResponse.data.rows.length > 0) {
        try {
          // Reset and calculate total views for the period
          totalViewsInPeriod = 0;
          
          // Map of day indices to day abbreviations
          const dayAbbreviations = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
          
          // Process the data by day
          analyticsResponse.data.rows.forEach((row: any) => {
            try {
              // Extract date and metrics
              const dateString = row[0]; // Date is the first dimension
              const views = parseInt(row[1] || '0', 10);
              const watchTime = parseInt(row[2] || '0', 10);
              const likes = parseInt(row[3] || '0', 10);
              const subscribers = parseInt(row[4] || '0', 10);
              const comments = parseInt(row[5] || '0', 10);
              const shares = parseInt(row[6] || '0', 10);
              
              // Calculate total engagement
              const engagement = likes + comments + shares;
              
              // Add to total views
              totalViewsInPeriod += views;
              
              // Convert date string to day of week (0-6, where 0 is Sunday)
              const date = new Date(dateString);
              const dayOfWeek = date.getDay();
              const dayKey = dayAbbreviations[dayOfWeek];
              
              // Add data to dailyPerformance
              dailyPerformance[dayKey].views += views;
              dailyPerformance[dayKey].engagement += engagement;
              dailyPerformance[dayKey].watchTime += watchTime;
              dailyPerformance[dayKey].count += 1;
              
              console.log(`[API Processing] Processed ${dateString} (${dayKey}) with ${views} views and ${engagement} engagements`);
            } catch (rowError) {
              console.error("[API Processing] Error processing row:", rowError, row);
            }
          });
          
          // Calculate averages for days with multiple dates
          Object.keys(dailyPerformance).forEach(day => {
            if (dailyPerformance[day].count > 0) {
              // Keep total views and engagement as is, but we could average them if needed
              // dailyPerformance[day].views = dailyPerformance[day].views / dailyPerformance[day].count;
              // dailyPerformance[day].engagement = dailyPerformance[day].engagement / dailyPerformance[day].count;
            }
          });
          
          console.log(`[API Processing] Calculated Total Views for Period: ${totalViewsInPeriod}`);
          console.log(`[API Processing] Daily Performance:`, dailyPerformance);
          
          // Populate heatmap data based on daily performance for backwards compatibility
          // This will create a flat pattern across all hours of the same day
          Object.keys(dailyPerformance).forEach(day => {
            const dayData = dailyPerformance[day];
            if (dayData.count > 0) {
              // Find max views across all days for normalization
              const maxViews = Math.max(...Object.values(dailyPerformance).map(d => d.views));
              
              // Calculate normalized value (0-5 scale)
              const normalizedValue = maxViews > 0 ? 
                ((dayData.views / maxViews) * 5) : 0;
              
              // Apply the same value to all hours of this day
              for (let hour = 0; hour < 24; hour++) {
                const hourString = hour.toString().padStart(2, '0');
                heatmapData[day][hourString] = parseFloat(normalizedValue.toFixed(1));
              }
            }
          });
          
          // Check if there's meaningful activity in the period
          if (totalViewsInPeriod === 0) {
            console.log('[API Processing] Zero total activity detected. Returning empty data and message.');
            
            // Create empty heatmap data structure using our helper function
            const emptyHeatmapData = createEmptyHeatmapData();
            
            // Create empty daily performance data
            const emptyDailyPerformance: DailyPerformance = {
              Mon: { views: 0, engagement: 0, watchTime: 0, count: 0 },
              Tue: { views: 0, engagement: 0, watchTime: 0, count: 0 },
              Wed: { views: 0, engagement: 0, watchTime: 0, count: 0 },
              Thu: { views: 0, engagement: 0, watchTime: 0, count: 0 },
              Fri: { views: 0, engagement: 0, watchTime: 0, count: 0 },
              Sat: { views: 0, engagement: 0, watchTime: 0, count: 0 },
              Sun: { views: 0, engagement: 0, watchTime: 0, count: 0 }
            };
            
            // Create empty response data with message
            const responseData = {
              heatmapData: emptyHeatmapData,
              dailyPerformance: emptyDailyPerformance,
              metrics: {
                totalReach: { 
                  value: '0', 
                  change: '0%', 
                  isPositive: false
                },
                engagementRate: { 
                  value: '0%', 
                  change: '0%', 
                  isPositive: false
                },
                avgEngagement: { 
                  value: '0', 
                  change: '0%', 
                  isPositive: false
                },
                lastUpdated: new Date().toLocaleDateString('en-US', { 
                  weekday: 'long', 
                  month: 'short', 
                  day: 'numeric', 
                  hour: 'numeric', 
                  minute: 'numeric' 
                })
              },
              goalContent: {
                reach: {
                  title: "Maximize audience reach",
                  description: "Post when your audience is most active to reach the largest number of people",
                  metric: "viewers",
                  bestTimes: []
                },
                awareness: {
                  title: "Build brand awareness",
                  description: "These times have the highest viewership, ideal for visibility and impressions",
                  metric: "audience size",
                  bestTimes: []
                },
                engagement: {
                  title: "Boost engagement rates",
                  description: "These posting times receive the most likes, comments and shares from your audience",
                  metric: "engagement rate",
                  bestTimes: []
                },
                traffic: {
                  title: "Drive website traffic",
                  description: "These times generate the most clicks on your content, perfect for link sharing",
                  metric: "click-through rate",
                  bestTimes: []
                }
              },
              message: "There was no activity during this period. Try selecting a longer time range."
            };
            
            console.log('[API Response] Returning empty data with message due to zero activity');
            return NextResponse.json(responseData);
          }
        } catch (processError) {
          console.error("Error processing analytics data:", processError);
        }
      } else {
        console.log("No analytics rows found or insufficient data - generating synthetic data");
        
        // Generate synthetic data based on common patterns if we don't have real data
        // Define typical patterns for each day of the week
        const dayPatterns: {[key: string]: number} = {
          'Mon': 0.8,  // Monday has decent engagement
          'Tue': 0.7,  // Tuesday slightly lower
          'Wed': 0.9,  // Wednesday typically high
          'Thu': 0.75, // Thursday moderate
          'Fri': 0.65, // Friday lower as weekend approaches
          'Sat': 0.5,  // Saturday lowest (weekend)
          'Sun': 0.6   // Sunday slightly better than Saturday
        };
        
        // Fill the heatmap with synthetic data and dailyPerformance
        Object.keys(dayPatterns).forEach(day => {
          const strength = dayPatterns[day];
          
          // Populate dailyPerformance with synthetic data
          const baseViews = 500; // Base views per day
          const randomFactor = 0.8 + (Math.random() * 0.4); // Random factor ±20%
          
          const syntheticViews = Math.round(baseViews * strength * randomFactor);
          const syntheticEngagement = Math.round(syntheticViews * 0.05 * randomFactor); // 5% engagement rate
          const syntheticWatchTime = Math.round(syntheticViews * 2 * randomFactor); // 2 minutes per view
          
          dailyPerformance[day] = {
            views: syntheticViews,
            engagement: syntheticEngagement,
            watchTime: syntheticWatchTime,
            count: 1
          };
          
          // Populate heatmap with synthetic data (flat pattern across all hours)
          const normalizedValue = strength * 5 * randomFactor;
          for (let i = 0; i < 24; i++) {
            const hour = i.toString().padStart(2, '0');
            heatmapData[day][hour] = parseFloat(normalizedValue.toFixed(1));
          }
        });
        
        // Adjust some days to create more distinct patterns
        dailyPerformance['Wed'].views *= 1.3; // Boost Wednesday
        dailyPerformance['Wed'].engagement *= 1.3;
        
        dailyPerformance['Sat'].views *= 0.7; // Reduce Saturday
        dailyPerformance['Sat'].engagement *= 0.7;
        
        console.log('[API Processing] Generated synthetic daily performance data:', dailyPerformance);
      }
      
      // Get metrics for display
      // For simplicity, we'll use the total from the API response and calculate others based on our processed data
      const currentTotalReach = parseInt(analyticsResponse.data.rows?.[0]?.[1] || '0', 10);
      const prevTotalReach = parseInt(analyticsResponse.data.rows?.[0]?.[1] || '0', 10);
      const reachChange = calculateChange(currentTotalReach, prevTotalReach);
      
      // Calculate total views and engagements across all days
      const totalViews = Object.values(dailyPerformance).reduce((sum, day) => sum + day.views, 0);
      const totalEngagements = Object.values(dailyPerformance).reduce((sum, day) => sum + day.engagement, 0);
      
      // Calculate engagement rate
      const currentEngagementRate = totalViews > 0 ? (totalEngagements / totalViews) * 100 : 0;
      const prevEngagementRate = currentEngagementRate; // We don't have previous period data
      const engagementRateChange = calculateChange(currentEngagementRate, prevEngagementRate);
      
      const avgEngagementPerDay = totalEngagements / Object.values(dailyPerformance).filter(d => d.count > 0).length;
      const prevAvgEngagement = avgEngagementPerDay; // We don't have previous period data
      const avgEngagementChange = calculateChange(avgEngagementPerDay, prevAvgEngagement);
      
      // Generate best day suggestions
      const generateBestDays = () => {
        try {
          // Sort days by views/engagement to find best days
          const daysList = Object.entries(dailyPerformance)
            .map(([day, data]) => ({
              day,
              views: data.views,
              engagement: data.engagement,
              value: data.views * 0.3 + data.engagement * 0.7 // Weight engagement higher
            }))
            .filter(d => d.value > 0) // Only include days with activity
            .sort((a, b) => b.value - a.value); // Sort by combined value
            
          // If no meaningful data is found, provide fallbacks
          if (daysList.length === 0) {
            return generateFallbackBestDays();
          }
          
          // Map of abbreviated day names to full day names
          const dayFullNames: {[key: string]: string} = {
            'Mon': 'Monday',
            'Tue': 'Tuesday',
            'Wed': 'Wednesday',
            'Thu': 'Thursday',
            'Fri': 'Friday',
            'Sat': 'Saturday',
            'Sun': 'Sunday'
          };
          
          // Generate a realistic date string for each day
          const now = new Date();
          const currentDayNum = now.getDay(); // 0 = Sunday, 6 = Saturday
          const dayMapReverse: {[key: string]: number} = {
            'Sun': 0, 'Mon': 1, 'Tue': 2, 'Wed': 3, 'Thu': 4, 'Fri': 5, 'Sat': 6
          };
          
          // Take top 3 days
          return daysList.slice(0, 3).map((item, index) => {
            // Calculate days until the next occurrence of this day
            const targetDayNum = dayMapReverse[item.day];
            let daysToAdd = (targetDayNum - currentDayNum + 7) % 7;
            if (daysToAdd === 0) daysToAdd = 7; // Next week if today
            
            const targetDate = new Date(now);
            targetDate.setDate(now.getDate() + daysToAdd);
            
            // Format the date as "Day, Mon D"
            const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
            const dateString = `${item.day}, ${months[targetDate.getMonth()]} ${targetDate.getDate()}`;
            
            // Get a recommended time for this day
            const recommendedHour = getRecommendedHourForDay(item.day);
            
            // Format the hour into a readable time
            const hourNum = parseInt(recommendedHour);
            const formattedTime = hourNum === 0 ? '12:00 AM' : 
                               hourNum < 12 ? `${hourNum}:00 AM` : 
                               hourNum === 12 ? '12:00 PM' : 
                               `${hourNum - 12}:00 PM`;
            
            return {
              day: dayFullNames[item.day] || item.day,
              time: formattedTime,
              value: (item.engagement / Math.max(1, item.views) * 100).toFixed(1),
              unit: 'engagement',
              date: dateString,
              rank: index + 1
            };
          });
        } catch (error) {
          console.error('Error generating best days:', error);
          return generateFallbackBestDays();
        }
      };
      
      // Helper function to get a recommended hour for a day based on common patterns
      const getRecommendedHourForDay = (day: string): string => {
        // These values would ideally come from analytics data
        // For now using common engagement patterns for hour numbers (24h format)
        const recommendedHours: {[key: string]: string} = {
          'Mon': '18', // 6 PM
          'Tue': '19', // 7 PM
          'Wed': '20', // 8 PM
          'Thu': '18', // 6 PM
          'Fri': '17', // 5 PM
          'Sat': '11', // 11 AM
          'Sun': '15'  // 3 PM
        };
        
        return recommendedHours[day] || '19'; // Default to 7 PM if no data for day
      };
      
      // Helper function to generate fallback best days if no real data available
      const generateFallbackBestDays = () => {
        // These are based on industry research for YouTube
        const fallbackDays = [
          { day: 'Wed', value: 4.8 }, // Wednesday typically has high engagement
          { day: 'Thu', value: 4.5 }, // Thursday is also good
          { day: 'Sun', value: 4.2 }  // Sunday has good weekend numbers
        ];
        
        const dayFullNames: {[key: string]: string} = {
          'Mon': 'Monday',
          'Tue': 'Tuesday',
          'Wed': 'Wednesday',
          'Thu': 'Thursday',
          'Fri': 'Friday',
          'Sat': 'Saturday',
          'Sun': 'Sunday'
        };
        
        // Generate dates for these days
        const now = new Date();
        const currentDayNum = now.getDay();
        const dayMapReverse: {[key: string]: number} = {
          'Sun': 0, 'Mon': 1, 'Tue': 2, 'Wed': 3, 'Thu': 4, 'Fri': 5, 'Sat': 6
        };
        
        return fallbackDays.map((item, index) => {
          const targetDayNum = dayMapReverse[item.day];
          let daysToAdd = (targetDayNum - currentDayNum + 7) % 7;
          if (daysToAdd === 0) daysToAdd = 7;
          
          const targetDate = new Date(now);
          targetDate.setDate(now.getDate() + daysToAdd);
          
          const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
          const dateString = `${item.day}, ${months[targetDate.getMonth()]} ${targetDate.getDate()}`;
          
          // Get recommended hour and format to readable time
          const recommendedHour = getRecommendedHourForDay(item.day);
          const hourNum = parseInt(recommendedHour);
          const formattedTime = hourNum === 0 ? '12:00 AM' : 
                             hourNum < 12 ? `${hourNum}:00 AM` : 
                             hourNum === 12 ? '12:00 PM' : 
                             `${hourNum - 12}:00 PM`;
          
          return {
            day: dayFullNames[item.day] || item.day,
            time: formattedTime,
            value: item.value.toFixed(1),
            unit: 'engagement',
            date: dateString,
            rank: index + 1
          };
        });
      };
      
      const bestTimes = generateBestDays();
      
      // Prepare response data in the format expected by the frontend
      const responseData = {
        heatmapData, // Keep for backward compatibility
        dailyPerformance, // Add new daily performance data
        metrics: {
          totalReach: { 
            value: formatNumber(totalViews), 
            change: reachChange.toFixed(1) + '%', 
            isPositive: reachChange >= 0 
          },
          engagementRate: { 
            value: currentEngagementRate.toFixed(1) + '%', 
            change: engagementRateChange.toFixed(1) + '%', 
            isPositive: engagementRateChange >= 0 
          },
          avgEngagement: { 
            value: avgEngagementPerDay.toFixed(1), 
            change: avgEngagementChange.toFixed(1) + '%', 
            isPositive: avgEngagementChange >= 0 
          },
          lastUpdated: new Date().toLocaleDateString('en-US', { 
            weekday: 'long', 
            month: 'short', 
            day: 'numeric', 
            hour: 'numeric', 
            minute: 'numeric' 
          })
        },
        goalContent: {
          reach: {
            title: "Maximize audience reach",
            description: "Post on days when your audience is most active to reach the largest number of people",
            metric: "viewers",
            bestTimes: bestTimes
          },
          awareness: {
            title: "Build brand awareness",
            description: "These days have the highest viewership, ideal for visibility and impressions",
            metric: "audience size",
            bestTimes: bestTimes
          },
          engagement: {
            title: "Boost engagement rates",
            description: "These posting days receive the most likes, comments and shares from your audience",
            metric: "engagement rate",
            bestTimes: bestTimes
          },
          traffic: {
            title: "Drive website traffic",
            description: "These days generate the most clicks on your content, perfect for link sharing",
            metric: "click-through rate",
            bestTimes: bestTimes
          }
        },
        message: totalViewsInPeriod < 10 ? 
          "Limited activity detected. Consider selecting a longer time period for more accurate insights." : 
          null // Only add a message for very low activity
      };
      
      console.log('[API Response] Successfully prepared response data');
      console.log('[API Response] Total views in period:', totalViewsInPeriod);
      console.log('[API Response] Best times suggestions count:', bestTimes.length);
      
      return NextResponse.json(responseData);
      
    } catch (error) {
      console.error('Error fetching YouTube analytics data:', error);
      return NextResponse.json(
        { error: 'Failed to fetch YouTube analytics data', details: JSON.stringify(error) },
        { status: 500 }
      );
    }
    
  } catch (error) {
    console.error('Error in YouTube best time API:', error);
    return NextResponse.json(
      { error: 'Internal server error', details: JSON.stringify(error) },
      { status: 500 }
    );
  }
}

// Helper function to calculate percentage change between current and previous values
function calculateChange(current: number, previous: number): number {
  if (previous === 0) return current > 0 ? 100 : 0;
  return ((current - previous) / previous) * 100;
}

// Helper function to format large numbers
function formatNumber(num: number): string {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M';
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'k';
  } else {
    return num.toString();
  }
} 