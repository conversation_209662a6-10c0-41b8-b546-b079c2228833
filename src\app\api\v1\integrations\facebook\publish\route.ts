/**
 * API v1 - Facebook Publish Route
 * 
 * Publishes content to Facebook pages
 */

import { NextRequest } from 'next/server';
import { FacebookService } from '@/lib/services/facebookService';
import { 
  with<PERSON><PERSON>r<PERSON><PERSON><PERSON>, 
  requireAuth,
  ErrorFactory
} from '@/lib/error-handler';
import {
  validateRequestBody
} from '@/lib/validation';
import { withVersioning } from '@/lib/versioning/middleware';
import { z } from 'zod';

// ============================================================================
// Validation Schemas
// ============================================================================

const FacebookPublishSchema = z.object({
  integration_id: z.string().uuid('Invalid integration ID'),
  message: z.string().max(63206, 'Message too long for Facebook').optional(),
  link: z.string().url('Invalid URL').optional(),
  media: z.array(z.object({
    url: z.string().url('Invalid media URL'),
    type: z.enum(['photo', 'video'], { message: 'Media type must be photo or video' })
  })).max(10, 'Maximum 10 media items allowed').optional(),
  scheduled_publish_time: z.number().int().min(Date.now() / 1000 + 600, 'Scheduled time must be at least 10 minutes in the future').optional(),
  published: z.boolean().default(true)
}).strict().refine(
  (data) => {
    // At least one of message, link, or media must be provided
    return data.message || data.link || (data.media && data.media.length > 0);
  },
  {
    message: 'At least one of message, link, or media must be provided',
    path: ['message']
  }
);

// ============================================================================
// Route Handler
// ============================================================================

/**
 * Internal handler for Facebook publishing
 */
async function facebookPublishHandler(req: NextRequest) {
  // 1. Authenticate user
  const { user } = await requireAuth(req);

  // 2. Validate request body
  const validatedData = await validateRequestBody(req, FacebookPublishSchema);

  // 3. Initialize Facebook service
  const { facebookService } = await import('@/lib/services');

  // 4. Prepare post data
  const postData = {
    message: validatedData.message,
    link: validatedData.link,
    media: validatedData.media,
    scheduled_publish_time: validatedData.scheduled_publish_time,
    published: validatedData.published
  };

  // 5. Publish the post
  const publishResult = await facebookService.publishPost(
    validatedData.integration_id,
    postData
  );

  if (!publishResult.success) {
    throw publishResult.error;
  }

  const postResult = publishResult.data!;

  // 6. Return success response
  return {
    success: true,
    data: {
      platform_post_id: postResult.id,
      post_id: postResult.post_id,
      permalink_url: postResult.permalink_url,
      created_time: postResult.created_time,
      scheduled: !!validatedData.scheduled_publish_time,
      published: validatedData.published
    },
    metadata: {
      provider: 'facebook',
      integration_id: validatedData.integration_id,
      published_at: new Date().toISOString(),
      media_count: validatedData.media?.length || 0,
      has_link: !!validatedData.link,
      message_length: validatedData.message?.length || 0
    }
  };
}

/**
 * POST /api/v1/integrations/facebook/publish
 * Publish content to Facebook page
 */
export const POST = withVersioning(withErrorHandler(facebookPublishHandler));
