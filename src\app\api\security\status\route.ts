/**
 * Security Status Endpoint
 * Provides security configuration and monitoring information
 */

import { NextRequest, NextResponse } from 'next/server';
import { getValidatedEnv } from '@/lib/security/env-validation';
import { getSecurityConfig, validateSecurityConfig } from '@/lib/security/security-headers';
import { getCSPConfig } from '@/lib/security/csp-config';
import { requireAuth } from '@/lib/error-handler';

/**
 * Security status interface
 */
interface SecurityStatus {
  environment: string;
  timestamp: string;
  headers: {
    csp: {
      enabled: boolean;
      strict: boolean;
      reportingEnabled: boolean;
    };
    hsts: {
      enabled: boolean;
      maxAge: number;
      includeSubDomains: boolean;
      preload: boolean;
    };
    crossOrigin: {
      openerPolicy: string;
      resourcePolicy: string;
      embedderPolicy: string;
    };
    permissions: {
      enabled: boolean;
      restrictedFeatures: string[];
    };
  };
  environment_variables: {
    validated: boolean;
    criticalSecretsPresent: boolean;
    httpsEnforced: boolean;
    debugMode: boolean;
  };
  recommendations: string[];
  warnings: string[];
}

/**
 * Check if HTTPS is properly configured
 */
function checkHTTPSConfiguration(): boolean {
  const env = getValidatedEnv();
  const isProduction = env.NODE_ENV === 'production';
  
  if (!isProduction) return true; // Skip HTTPS check in development
  
  return (
    env.NEXT_PUBLIC_APP_URL.startsWith('https://') &&
    env.NEXTAUTH_URL.startsWith('https://')
  );
}

/**
 * Generate security recommendations
 */
function generateRecommendations(): string[] {
  const recommendations: string[] = [];
  const env = getValidatedEnv();
  const securityConfig = getSecurityConfig();
  const isProduction = env.NODE_ENV === 'production';
  
  if (!isProduction) {
    recommendations.push('Enable production mode for full security features');
  }
  
  if (securityConfig.hsts.maxAge < 31536000 && isProduction) {
    recommendations.push('Consider increasing HSTS max-age to 1 year (31536000 seconds)');
  }
  
  if (!securityConfig.hsts.preload && isProduction) {
    recommendations.push('Consider enabling HSTS preload for enhanced security');
  }
  
  if (env.DEBUG_MODE === 'true' && isProduction) {
    recommendations.push('Disable debug mode in production');
  }
  
  return recommendations;
}

/**
 * Generate security warnings
 */
function generateWarnings(): string[] {
  const warnings: string[] = [];
  const env = getValidatedEnv();
  const securityConfig = getSecurityConfig();
  const configErrors = validateSecurityConfig(securityConfig);
  
  // Add configuration errors as warnings
  warnings.push(...configErrors);
  
  // Check for weak secrets in production
  if (env.NODE_ENV === 'production') {
    if (!checkHTTPSConfiguration()) {
      warnings.push('HTTPS not properly configured for production');
    }
    
    if (env.DEBUG_MODE === 'true') {
      warnings.push('Debug mode is enabled in production');
    }
  }
  
  return warnings;
}

/**
 * Get security status
 */
function getSecurityStatus(): SecurityStatus {
  const env = getValidatedEnv();
  const securityConfig = getSecurityConfig();
  const cspConfig = getCSPConfig();
  
  return {
    environment: env.NODE_ENV,
    timestamp: new Date().toISOString(),
    
    headers: {
      csp: {
        enabled: true,
        strict: !(Array.isArray(cspConfig['script-src']) ? cspConfig['script-src'].includes("'unsafe-inline'") :
                  typeof cspConfig['script-src'] === 'string' ? cspConfig['script-src'].includes("'unsafe-inline'") : false),
        reportingEnabled: !!securityConfig.cspReporting.reportUri,
      },
      
      hsts: {
        enabled: securityConfig.hsts.maxAge > 0,
        maxAge: securityConfig.hsts.maxAge,
        includeSubDomains: securityConfig.hsts.includeSubDomains,
        preload: securityConfig.hsts.preload,
      },
      
      crossOrigin: {
        openerPolicy: securityConfig.crossOrigin.openerPolicy,
        resourcePolicy: securityConfig.crossOrigin.resourcePolicy,
        embedderPolicy: securityConfig.crossOrigin.embedderPolicy,
      },
      
      permissions: {
        enabled: true,
        restrictedFeatures: Object.keys(securityConfig.permissionsPolicy).filter(
          key => securityConfig.permissionsPolicy[key as keyof typeof securityConfig.permissionsPolicy].length === 0
        ),
      },
    },
    
    environment_variables: {
      validated: true,
      criticalSecretsPresent: !!(
        env.SUPABASE_SERVICE_ROLE_KEY &&
        env.NEXTAUTH_SECRET &&
        env.TOKEN_ENCRYPTION_KEY
      ),
      httpsEnforced: checkHTTPSConfiguration(),
      debugMode: env.DEBUG_MODE === 'true',
    },
    
    recommendations: generateRecommendations(),
    warnings: generateWarnings(),
  };
}

/**
 * GET /api/security/status
 * Returns security configuration status
 */
export async function GET(request: NextRequest): Promise<NextResponse> {
  try {
    // Require authentication for security status
    await requireAuth(request);
    
    const status = getSecurityStatus();
    
    return NextResponse.json(status, {
      status: 200,
      headers: {
        'Cache-Control': 'no-store, no-cache, must-revalidate',
        'Content-Type': 'application/json',
      },
    });
    
  } catch (error) {
    console.error('Error getting security status:', error);
    
    if (error instanceof Error && error.message.includes('Authentication required')) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/security/status/validate
 * Validates current security configuration
 */
export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    // Require authentication
    await requireAuth(request);
    
    const status = getSecurityStatus();
    const isSecure = status.warnings.length === 0;
    
    return NextResponse.json({
      secure: isSecure,
      score: Math.max(0, 100 - (status.warnings.length * 10)),
      status,
    }, {
      status: 200,
      headers: {
        'Cache-Control': 'no-store, no-cache, must-revalidate',
        'Content-Type': 'application/json',
      },
    });
    
  } catch (error) {
    console.error('Error validating security configuration:', error);
    
    if (error instanceof Error && error.message.includes('Authentication required')) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
