/**
 * QStash Worker: Job Status Monitor
 * 
 * Monitors job statuses and provides health metrics
 * POST /api/workers/job-status-monitor
 */

import { NextRequest } from 'next/server';
import { createSecureWorkerHandler, extractQStashMetadata, logWorkerRequest, createWorkerResponse as createErrorResponse } from '../utils/signature-verification';
import { processWorkerJob, getSupabaseClient, WorkerJobPayload, createWorkerResponse } from '../utils/worker-helpers';
import { getMessageStatus, qstashClient } from '../utils/qstash-client';

// ============================================================================
// Types and Interfaces
// ============================================================================

interface JobStatusMonitorPayload {
  jobId: string;
  action: 'monitor' | 'health_check' | 'cleanup' | 'metrics';
  timeRange?: {
    startDate: string;
    endDate: string;
  };
  jobTypes?: string[];
  platforms?: string[];
  type: 'job_status_monitoring';
}

interface JobStatusResult {
  action: string;
  totalJobs: number;
  jobsByStatus: {
    pending: number;
    processing: number;
    completed: number;
    failed: number;
    retrying: number;
  };
  jobsByType: Record<string, number>;
  jobsByPlatform: Record<string, number>;
  healthMetrics: {
    successRate: number;
    averageProcessingTime: number;
    failureRate: number;
    retryRate: number;
  };
  alerts: Array<{
    type: 'warning' | 'error' | 'info';
    message: string;
    count?: number;
  }>;
  recommendations: string[];
}

interface JobMetrics {
  id: string;
  type: string;
  platform?: string;
  status: string;
  created_at: string;
  updated_at: string;
  processing_time_ms?: number;
  retry_count: number;
  last_error?: string;
}

// ============================================================================
// Job Monitoring Logic
// ============================================================================

async function monitorJobStatus(payload: JobStatusMonitorPayload): Promise<JobStatusResult> {
  switch (payload.action) {
    case 'monitor':
      return await performJobMonitoring(payload);
      
    case 'health_check':
      return await performHealthCheck(payload);
      
    case 'cleanup':
      return await performJobCleanup(payload);
      
    case 'metrics':
      return await generateJobMetrics(payload);
      
    default:
      throw new Error(`Unknown action: ${payload.action}`);
  }
}

async function performJobMonitoring(payload: JobStatusMonitorPayload): Promise<JobStatusResult> {
  const supabase = getSupabaseClient();
  
  try {
    // Build query based on filters
    let query = supabase
      .from('jobs')
      .select('*');
    
    if (payload.timeRange) {
      query = query
        .gte('created_at', payload.timeRange.startDate)
        .lte('created_at', payload.timeRange.endDate);
    }
    
    if (payload.jobTypes && payload.jobTypes.length > 0) {
      query = query.in('type', payload.jobTypes);
    }
    
    const { data: jobs, error } = await query.order('created_at', { ascending: false });
    
    if (error) {
      throw new Error(`Failed to fetch jobs: ${error.message}`);
    }
    
    // Analyze job data
    const analysis = analyzeJobs(jobs || []);
    
    // Generate alerts and recommendations
    const alerts = generateAlerts(analysis);
    const recommendations = generateRecommendations(analysis);
    
    console.log(`Job monitoring completed: ${jobs?.length || 0} jobs analyzed`);
    
    return {
      action: 'monitor',
      totalJobs: jobs?.length || 0,
      jobsByStatus: analysis.jobsByStatus,
      jobsByType: analysis.jobsByType,
      jobsByPlatform: analysis.jobsByPlatform,
      healthMetrics: analysis.healthMetrics,
      alerts,
      recommendations
    };
    
  } catch (error) {
    console.error('Error monitoring job status:', error);
    throw error;
  }
}

async function performHealthCheck(payload: JobStatusMonitorPayload): Promise<JobStatusResult> {
  const supabase = getSupabaseClient();
  
  try {
    // Check recent job performance (last 24 hours)
    const twentyFourHoursAgo = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString();
    
    const { data: recentJobs, error } = await supabase
      .from('jobs')
      .select('*')
      .gte('created_at', twentyFourHoursAgo)
      .order('created_at', { ascending: false });
    
    if (error) {
      throw new Error(`Failed to fetch recent jobs: ${error.message}`);
    }
    
    const analysis = analyzeJobs(recentJobs || []);
    const healthAlerts = generateHealthAlerts(analysis);
    const healthRecommendations = generateHealthRecommendations(analysis);
    
    console.log(`Health check completed: ${recentJobs?.length || 0} recent jobs analyzed`);
    
    return {
      action: 'health_check',
      totalJobs: recentJobs?.length || 0,
      jobsByStatus: analysis.jobsByStatus,
      jobsByType: analysis.jobsByType,
      jobsByPlatform: analysis.jobsByPlatform,
      healthMetrics: analysis.healthMetrics,
      alerts: healthAlerts,
      recommendations: healthRecommendations
    };
    
  } catch (error) {
    console.error('Error performing health check:', error);
    throw error;
  }
}

async function performJobCleanup(payload: JobStatusMonitorPayload): Promise<JobStatusResult> {
  const supabase = getSupabaseClient();
  
  try {
    // Clean up old completed jobs (older than 30 days)
    const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString();
    
    const { data: oldJobs, error: fetchError } = await supabase
      .from('jobs')
      .select('id, status')
      .eq('status', 'completed')
      .lt('created_at', thirtyDaysAgo);
    
    if (fetchError) {
      throw new Error(`Failed to fetch old jobs: ${fetchError.message}`);
    }
    
    let cleanedCount = 0;
    if (oldJobs && oldJobs.length > 0) {
      const { error: deleteError } = await supabase
        .from('jobs')
        .delete()
        .in('id', oldJobs.map(job => job.id));
      
      if (deleteError) {
        throw new Error(`Failed to delete old jobs: ${deleteError.message}`);
      }
      
      cleanedCount = oldJobs.length;
    }
    
    // Clean up old failed jobs (older than 7 days)
    const sevenDaysAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString();
    
    const { data: oldFailedJobs, error: fetchFailedError } = await supabase
      .from('failed_jobs')
      .select('id')
      .eq('status', 'archived')
      .lt('archived_at', sevenDaysAgo);
    
    if (fetchFailedError) {
      console.warn('Failed to fetch old failed jobs:', fetchFailedError);
    } else if (oldFailedJobs && oldFailedJobs.length > 0) {
      const { error: deleteFailedError } = await supabase
        .from('failed_jobs')
        .delete()
        .in('id', oldFailedJobs.map(job => job.id));
      
      if (deleteFailedError) {
        console.warn('Failed to delete old failed jobs:', deleteFailedError);
      } else {
        cleanedCount += oldFailedJobs.length;
      }
    }
    
    console.log(`Job cleanup completed: ${cleanedCount} old jobs removed`);
    
    return {
      action: 'cleanup',
      totalJobs: cleanedCount,
      jobsByStatus: { pending: 0, processing: 0, completed: 0, failed: 0, retrying: 0 },
      jobsByType: {},
      jobsByPlatform: {},
      healthMetrics: { successRate: 0, averageProcessingTime: 0, failureRate: 0, retryRate: 0 },
      alerts: [{
        type: 'info',
        message: `Cleaned up ${cleanedCount} old job records`
      }],
      recommendations: []
    };
    
  } catch (error) {
    console.error('Error performing job cleanup:', error);
    throw error;
  }
}

async function generateJobMetrics(payload: JobStatusMonitorPayload): Promise<JobStatusResult> {
  const supabase = getSupabaseClient();
  
  try {
    // Get comprehensive job metrics
    const timeRange = payload.timeRange || {
      startDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days ago
      endDate: new Date().toISOString()
    };
    
    const { data: jobs, error } = await supabase
      .from('jobs')
      .select('*')
      .gte('created_at', timeRange.startDate)
      .lte('created_at', timeRange.endDate)
      .order('created_at', { ascending: false });
    
    if (error) {
      throw new Error(`Failed to fetch jobs for metrics: ${error.message}`);
    }
    
    const analysis = analyzeJobs(jobs || []);
    const detailedMetrics = generateDetailedMetrics(jobs || []);
    
    console.log(`Job metrics generated for ${jobs?.length || 0} jobs`);
    
    return {
      action: 'metrics',
      totalJobs: jobs?.length || 0,
      jobsByStatus: analysis.jobsByStatus,
      jobsByType: analysis.jobsByType,
      jobsByPlatform: analysis.jobsByPlatform,
      healthMetrics: {
        ...analysis.healthMetrics,
        ...detailedMetrics
      },
      alerts: [],
      recommendations: []
    };
    
  } catch (error) {
    console.error('Error generating job metrics:', error);
    throw error;
  }
}

// ============================================================================
// Analysis Functions
// ============================================================================

function analyzeJobs(jobs: any[]) {
  const jobsByStatus = {
    pending: 0,
    processing: 0,
    completed: 0,
    failed: 0,
    retrying: 0
  };
  
  const jobsByType: Record<string, number> = {};
  const jobsByPlatform: Record<string, number> = {};
  
  let totalProcessingTime = 0;
  let processedJobsCount = 0;
  let totalRetries = 0;
  
  jobs.forEach(job => {
    // Count by status
    if (job.status in jobsByStatus) {
      jobsByStatus[job.status as keyof typeof jobsByStatus]++;
    }
    
    // Count by type
    if (job.type) {
      jobsByType[job.type] = (jobsByType[job.type] || 0) + 1;
    }
    
    // Count by platform (if available in job data)
    const platform = job.data?.platform || job.metadata?.platform;
    if (platform) {
      jobsByPlatform[platform] = (jobsByPlatform[platform] || 0) + 1;
    }
    
    // Processing time metrics
    if (job.processing_time_ms) {
      totalProcessingTime += job.processing_time_ms;
      processedJobsCount++;
    }
    
    // Retry metrics
    if (job.retry_count) {
      totalRetries += job.retry_count;
    }
  });
  
  const totalJobs = jobs.length;
  const successRate = totalJobs > 0 ? (jobsByStatus.completed / totalJobs) * 100 : 0;
  const failureRate = totalJobs > 0 ? (jobsByStatus.failed / totalJobs) * 100 : 0;
  const retryRate = totalJobs > 0 ? (totalRetries / totalJobs) * 100 : 0;
  const averageProcessingTime = processedJobsCount > 0 ? totalProcessingTime / processedJobsCount : 0;
  
  return {
    jobsByStatus,
    jobsByType,
    jobsByPlatform,
    healthMetrics: {
      successRate,
      averageProcessingTime,
      failureRate,
      retryRate
    }
  };
}

function generateAlerts(analysis: any): Array<{ type: 'warning' | 'error' | 'info'; message: string; count?: number }> {
  const alerts = [];
  
  if (analysis.healthMetrics.failureRate > 20) {
    alerts.push({
      type: 'error' as const,
      message: `High failure rate: ${analysis.healthMetrics.failureRate.toFixed(1)}%`,
      count: analysis.jobsByStatus.failed
    });
  } else if (analysis.healthMetrics.failureRate > 10) {
    alerts.push({
      type: 'warning' as const,
      message: `Elevated failure rate: ${analysis.healthMetrics.failureRate.toFixed(1)}%`,
      count: analysis.jobsByStatus.failed
    });
  }
  
  if (analysis.healthMetrics.retryRate > 50) {
    alerts.push({
      type: 'warning' as const,
      message: `High retry rate: ${analysis.healthMetrics.retryRate.toFixed(1)}%`
    });
  }
  
  if (analysis.healthMetrics.averageProcessingTime > 30000) {
    alerts.push({
      type: 'warning' as const,
      message: `Slow processing: ${(analysis.healthMetrics.averageProcessingTime / 1000).toFixed(1)}s average`
    });
  }
  
  return alerts;
}

function generateRecommendations(analysis: any): string[] {
  const recommendations = [];
  
  if (analysis.healthMetrics.failureRate > 15) {
    recommendations.push('Consider reviewing error patterns and improving error handling');
  }
  
  if (analysis.healthMetrics.retryRate > 30) {
    recommendations.push('High retry rate detected - check for transient errors and rate limiting');
  }
  
  if (analysis.healthMetrics.averageProcessingTime > 20000) {
    recommendations.push('Consider optimizing job processing logic or increasing worker capacity');
  }
  
  if (analysis.jobsByStatus.pending > 100) {
    recommendations.push('Large number of pending jobs - consider scaling worker capacity');
  }
  
  return recommendations;
}

function generateHealthAlerts(analysis: any) {
  return generateAlerts(analysis);
}

function generateHealthRecommendations(analysis: any) {
  return generateRecommendations(analysis);
}

function generateDetailedMetrics(jobs: any[]) {
  // Additional detailed metrics can be added here
  return {};
}

// ============================================================================
// Worker Handler
// ============================================================================

async function jobStatusMonitorHandler(req: NextRequest): Promise<Response> {
  logWorkerRequest(req, 'JobStatusMonitor');

  const result = await processWorkerJob(req, async (payload: WorkerJobPayload) => {
    const monitorPayload = payload as unknown as JobStatusMonitorPayload;
    console.log(`Job status monitoring: action=${monitorPayload.action}`);

    const monitorResult = await monitorJobStatus(monitorPayload);

    console.log(`Job status monitoring completed:`, {
      action: monitorResult.action,
      totalJobs: monitorResult.totalJobs,
      successRate: monitorResult.healthMetrics.successRate,
      failureRate: monitorResult.healthMetrics.failureRate
    });

    return monitorResult;
  });

  return createWorkerResponse(result);
}

// ============================================================================
// Route Handlers
// ============================================================================

export const POST = createSecureWorkerHandler(jobStatusMonitorHandler);

export async function GET() {
  return createErrorResponse({
    success: false,
    error: {
      message: 'Method not allowed. This endpoint only accepts POST requests from QStash.',
      code: 'METHOD_NOT_ALLOWED'
    }
  }, 405);
}

export async function PUT() {
  return createErrorResponse({
    success: false,
    error: {
      message: 'Method not allowed. This endpoint only accepts POST requests from QStash.',
      code: 'METHOD_NOT_ALLOWED'
    }
  }, 405);
}

export async function DELETE() {
  return createErrorResponse({
    success: false,
    error: {
      message: 'Method not allowed. This endpoint only accepts POST requests from QStash.',
      code: 'METHOD_NOT_ALLOWED'
    }
  }, 405);
}
