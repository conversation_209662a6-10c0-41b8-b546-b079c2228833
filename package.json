{"name": "social-media-manager", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3002", "dev-pages": "next dev -p 3002", "build": "next build", "start": "next start -p 3002", "lint": "next lint", "setup-upstash": "node src/scripts/setup-upstash-env.js", "test-upstash": "node src/scripts/run-upstash-test.js", "check-google-env": "node src/scripts/check-google-env.js"}, "dependencies": {"@auth/core": "^0.39.1", "@auth/supabase-adapter": "^1.8.0", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.1.0", "@radix-ui/react-label": "^2.1.3", "@radix-ui/react-select": "^2.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.1.4", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.6", "@supabase/auth-helpers-nextjs": "^0.8.7", "@supabase/ssr": "^0.5.2", "@supabase/supabase-js": "^2.49.4", "@tailwindcss/forms": "^0.5.10", "@tanstack/react-query": "^5.80.6", "@tanstack/react-query-devtools": "^5.80.6", "@tremor/react": "^3.18.7", "@types/react-big-calendar": "^1.16.2", "@types/react-window": "^1.8.8", "@types/react-window-infinite-loader": "^1.0.9", "@upstash/qstash": "^2.7.23", "@upstash/redis": "^1.25.1", "autoprefixer": "^10.4.16", "bullmq": "^4.12.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dotenv": "^16.4.7", "emoji-picker-react": "^4.12.0", "google-auth-library": "^9.15.1", "googleapis": "^146.0.0", "jspdf": "^3.0.0", "lucide-react": "^0.294.0", "moment": "^2.30.1", "next": "^14.2.29", "next-auth": "^4.24.5", "node-fetch": "^3.3.2", "react": "^18.2.0", "react-big-calendar": "^1.19.2", "react-dom": "^18.2.0", "react-hook-form": "^7.57.0", "react-window": "^1.8.11", "react-window-infinite-loader": "^1.0.10", "recharts": "^2.15.3", "sonner": "^2.0.3", "tailwind-merge": "^2.6.0", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "twitter-api-v2": "^1.23.2", "zod": "^3.25.56"}, "devDependencies": {"@eslint/eslintrc": "^3", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@types/jest": "^29.5.14", "@types/node": "^20.10.0", "@types/react": "^18.2.38", "@types/react-dom": "^18.2.15", "@types/uuid": "^10.0.0", "critters": "^0.0.23", "eslint": "^8.54.0", "eslint-config-next": "14.0.3", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "typescript": "^5.3.2"}}