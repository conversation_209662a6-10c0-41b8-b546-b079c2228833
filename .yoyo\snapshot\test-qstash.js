require('dotenv').config();
const https = require('https');

console.log('QStash publishing test started');

// Manually extract the token if process.env doesn't have it
let qstashToken = process.env.QSTASH_TOKEN;
if (!qstashToken) {
  const fs = require('fs');
  const path = require('path');
  const envPath = path.resolve('.env');
  if (fs.existsSync(envPath)) {
    const envContent = fs.readFileSync(envPath, 'utf8');
    const tokenLine = envContent.split('\n').find(line => line.startsWith('QSTASH_TOKEN'));
    if (tokenLine) {
      const tokenMatch = tokenLine.match(/QSTASH_TOKEN="(.+)"/);
      if (tokenMatch && tokenMatch[1]) {
        qstashToken = tokenMatch[1];
        console.log('Loaded QSTASH_TOKEN from .env file');
      }
    }
  }
}

if (!qstashToken) {
  console.error('QSTASH_TOKEN not found. Please check your .env file.');
  process.exit(1);
}

console.log('QSTASH_TOKEN loaded successfully');

// Get ngrok URL from env or use default for testing
let ngrokUrl = process.env.NGROK_URL;
if (!ngrokUrl) {
  const fs = require('fs');
  const path = require('path');
  const envPath = path.resolve('.env');
  if (fs.existsSync(envPath)) {
    const envContent = fs.readFileSync(envPath, 'utf8');
    const ngrokLine = envContent.split('\n').find(line => line.startsWith('NGROK_URL'));
    if (ngrokLine) {
      const ngrokMatch = ngrokLine.match(/NGROK_URL="(.+)"/);
      if (ngrokMatch && ngrokMatch[1]) {
        ngrokUrl = ngrokMatch[1];
        console.log('Loaded NGROK_URL from .env file');
      }
    }
  }
}

if (!ngrokUrl) {
  console.error('NGROK_URL not found. Please check your .env file.');
  process.exit(1);
}

// Remove trailing slash if present
if (ngrokUrl.endsWith('/')) {
  ngrokUrl = ngrokUrl.slice(0, -1);
}

// Ensure ngrokUrl starts with https:// or http://
if (!ngrokUrl.startsWith('http://') && !ngrokUrl.startsWith('https://')) {
  ngrokUrl = 'https://' + ngrokUrl;
}

console.log(`Using ngrok URL: ${ngrokUrl}`);

// Test post ID
const postId = 'cf43776a-54bd-4bfd-bec4-b6f230463edd';
console.log(`Using test post ID: ${postId}`);

// Setup the API request options
const destinationUrl = `${ngrokUrl}/api/posts/publish-callback`;
console.log(`Destination URL: ${destinationUrl}`);

const requestOptions = {
  hostname: 'qstash.upstash.io',
  port: 443,
  path: '/v2/publish',
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${qstashToken}`,
    'Upstash-Forward-Url': destinationUrl
  }
};

console.log(`QStash API URL: https://${requestOptions.hostname}${requestOptions.path}`);

// Prepare the message payload
const postData = JSON.stringify({
  postId: postId,
  timestamp: new Date().toISOString()
});

// Make the request
const req = https.request(requestOptions, (res) => {
  console.log(`QStash response status: ${res.statusCode}`);
  
  let responseData = '';
  res.on('data', (chunk) => {
    responseData += chunk;
  });
  
  res.on('end', () => {
    console.log(`QStash response: ${responseData}`);
    if (res.statusCode === 200 || res.statusCode === 201) {
      console.log('Message published successfully to QStash!');
      console.log('QStash will deliver to your endpoint shortly.');
      console.log('Check your Next.js logs and ngrok dashboard for incoming requests.');
    }
  });
});

req.on('error', (error) => {
  console.error(`QStash request error: ${error.message}`);
});

// Write data to request body
req.write(postData);
req.end(); 