# Postbee - Social Media Management Platform

🚀 A comprehensive social media management platform that enables users to create, schedule, and publish content across multiple social media platforms from a single, unified interface.

## ✨ Features

### 🔐 Authentication & Account Management
- **Multi-Provider Authentication**: Google OAuth, email/password via Supabase Auth
- **Connected Accounts**: Link YouTube, Facebook, Instagram, Twitter/X accounts
- **Secure Token Management**: Encrypted storage of OAuth tokens with automatic refresh

### 📝 Content Creation & Management
- **Rich Post Editor**: Create posts with text, images, and videos
- **Media Management**: Upload and organize media assets via Google Drive integration
- **Auto-Comment System**: Automated comment replies with customizable rules
- **Content Scheduling**: Schedule posts for optimal engagement times

### 📊 Analytics & Insights
- **Performance Tracking**: Monitor post engagement across all platforms
- **Best Time Analysis**: AI-powered recommendations for optimal posting times
- **Social Score**: Comprehensive engagement metrics and insights
- **Comment Management**: View and respond to comments from all platforms

### 🎯 Advanced Features
- **Multi-Channel Publishing**: Publish to multiple platforms simultaneously
- **Tag Organization**: Categorize and organize content with custom tags
- **Draft Management**: Save and manage post drafts
- **Approval Workflows**: Team collaboration with approval processes

## 🛠 Tech Stack

- **Frontend**: Next.js 14 (App Router), React 18, TypeScript, Tailwind CSS
- **Backend**: Supabase (PostgreSQL, Auth, Storage, Real-time)
- **Authentication**: Supabase Auth + Custom OAuth integrations
- **APIs**: YouTube Data API, Facebook Graph API, Instagram Basic Display, Twitter API v2
- **Queue System**: Upstash QStash for reliable job processing
- **UI Components**: Radix UI, Shadcn/ui, Lucide Icons
- **Styling**: Tailwind CSS with custom design system

## 🚀 Quick Start

### Prerequisites

- Node.js 18+ and npm/yarn
- Supabase account ([supabase.com](https://supabase.com))
- Google Cloud Platform account for API access
- Social media developer accounts (Facebook, Twitter, etc.)

### 1. Clone & Install

```bash
git clone https://github.com/abirich01/Postbee.git
cd Postbee
npm install
```

### 2. Environment Configuration

Create `.env.local` and configure the following:

```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your-supabase-project-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# Google OAuth & APIs
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret

# Social Media APIs
FACEBOOK_APP_ID=your-facebook-app-id
FACEBOOK_APP_SECRET=your-facebook-app-secret
TWITTER_CLIENT_ID=your-twitter-client-id
TWITTER_CLIENT_SECRET=your-twitter-client-secret

# Queue System
QSTASH_URL=https://qstash.upstash.io
QSTASH_TOKEN=your-qstash-token

# App Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3002
NEXTAUTH_SECRET=your-nextauth-secret
```

### 3. Database Setup

```bash
# Run Supabase migrations
npx supabase migration up

# Or manually run the SQL files in supabase/migrations/
```

### 4. Start Development

```bash
npm run dev
```

Visit [http://localhost:3002](http://localhost:3002) to see the application.

## 📁 Project Structure

```
src/
├── app/                 # Next.js App Router pages
│   ├── (landing)/       # Landing page routes
│   ├── api/             # API routes
│   ├── auth/            # Authentication pages
│   ├── dashboard/       # Dashboard pages
│   ├── publish/         # Content creation
│   ├── engage/          # Comment management
│   └── analyze/         # Analytics pages
├── components/          # Reusable React components
│   ├── ui/              # Base UI components
│   ├── layout/          # Layout components
│   ├── modals/          # Modal components
│   └── auto-comment/    # Auto-comment features
├── lib/                 # Utility libraries
│   ├── services/        # API service classes
│   ├── hooks/           # Custom React hooks
│   ├── supabase/        # Supabase client setup
│   └── validation/      # Zod schemas
├── types/               # TypeScript definitions
└── middleware.ts        # Next.js middleware
```

## 🔧 Key Features Explained

### Multi-Platform Publishing
- **Unified Interface**: Create once, publish everywhere
- **Platform-Specific Optimization**: Automatic content formatting per platform
- **Bulk Operations**: Schedule multiple posts across platforms

### Auto-Comment System
- **Smart Replies**: AI-powered comment responses
- **Rule-Based Automation**: Custom rules for different comment types
- **Sentiment Analysis**: Automatic detection of comment sentiment

### Analytics Dashboard
- **Real-Time Metrics**: Live engagement tracking
- **Performance Insights**: Detailed analytics per platform
- **Growth Tracking**: Follower and engagement growth over time

## 🚀 Deployment

### Deploy to Vercel (Recommended)

[![Deploy with Vercel](https://vercel.com/button)](https://vercel.com/new/clone?repository-url=https://github.com/abirich01/Postbee)

1. **Connect Repository**: Import your GitHub repository to Vercel
2. **Environment Variables**: Add all required environment variables
3. **Deploy**: Vercel will automatically build and deploy your application

### Manual Deployment

```bash
# Build the application
npm run build

# Start production server
npm start
```

### Database Migration

```bash
# Apply all migrations
npx supabase migration up

# Reset database (development only)
npx supabase db reset
```

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guidelines](CONTRIBUTING.md) for details.

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Commit your changes: `git commit -m 'Add amazing feature'`
4. Push to the branch: `git push origin feature/amazing-feature`
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- [Supabase](https://supabase.com) for the backend infrastructure
- [Vercel](https://vercel.com) for hosting and deployment
- [Tailwind CSS](https://tailwindcss.com) for the styling system
- [Radix UI](https://radix-ui.com) for accessible UI components

---

**Built with ❤️ by [abirich01](https://github.com/abirich01)**


