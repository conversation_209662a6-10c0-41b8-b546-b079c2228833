/**
 * API v1 - Facebook Analytics Route
 * 
 * Fetches analytics data for Facebook pages
 */

import { NextRequest } from 'next/server';
import { FacebookService } from '@/lib/services/facebookService';
import { 
  with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 
  requireAuth,
  ErrorFactory
} from '@/lib/error-handler';
import {
  validateQueryParams
} from '@/lib/validation';
import { withVersioning } from '@/lib/versioning/middleware';
import { z } from 'zod';

// ============================================================================
// Validation Schemas
// ============================================================================

const FacebookAnalyticsQuerySchema = z.object({
  integration_id: z.string().uuid('Invalid integration ID'),
  start_date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'Start date must be in YYYY-MM-DD format'),
  end_date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'End date must be in YYYY-MM-DD format'),
  cache: z.string().default('true')
}).strict().refine(
  (data) => {
    const startDate = new Date(data.start_date);
    const endDate = new Date(data.end_date);
    const now = new Date();
    
    // Start date must be before end date
    if (startDate >= endDate) {
      return false;
    }
    
    // End date cannot be in the future
    if (endDate > now) {
      return false;
    }
    
    // Date range cannot be more than 90 days
    const diffTime = Math.abs(endDate.getTime() - startDate.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    if (diffDays > 90) {
      return false;
    }
    
    return true;
  },
  {
    message: 'Invalid date range. Start date must be before end date, end date cannot be in future, and range cannot exceed 90 days.'
  }
);

// ============================================================================
// Route Handler
// ============================================================================

/**
 * Internal handler for Facebook analytics
 */
async function facebookAnalyticsHandler(req: NextRequest) {
  // 1. Authenticate user
  const { user } = await requireAuth(req);

  // 2. Validate query parameters
  const queryParams = validateQueryParams(req, FacebookAnalyticsQuerySchema);

  // 3. Convert cache string to boolean
  const cacheEnabled = queryParams.cache === 'true';

  // 4. Initialize Facebook service
  const { facebookService } = await import('@/lib/services');

  // 5. Check cache first if enabled
  if (cacheEnabled) {
    const cacheResult = await checkAnalyticsCache(
      queryParams.integration_id,
      queryParams.start_date,
      queryParams.end_date
    );
    
    if (cacheResult.success && cacheResult.data) {
      return {
        success: true,
        data: cacheResult.data,
        metadata: {
          provider: 'facebook',
          integration_id: queryParams.integration_id,
          date_range: {
            start: queryParams.start_date,
            end: queryParams.end_date
          },
          cached: true,
          cache_timestamp: cacheResult.data.created_at
        }
      };
    }
  }

  // 6. Fetch fresh analytics data
  const analyticsResult = await facebookService.fetchAnalytics(
    queryParams.integration_id,
    {
      start: queryParams.start_date,
      end: queryParams.end_date
    }
  );

  if (!analyticsResult.success) {
    throw analyticsResult.error;
  }

  const analyticsData = analyticsResult.data!;

  // 7. Cache the results if caching is enabled
  if (cacheEnabled) {
    await cacheAnalyticsData(
      queryParams.integration_id,
      queryParams.start_date,
      queryParams.end_date,
      analyticsData
    );
  }

  // 8. Return analytics data
  return {
    success: true,
    data: analyticsData,
    metadata: {
      provider: 'facebook',
      integration_id: queryParams.integration_id,
      date_range: {
        start: queryParams.start_date,
        end: queryParams.end_date
      },
      cached: false,
      fetched_at: new Date().toISOString(),
      metrics_count: Object.keys(analyticsData.metrics).length,
      top_posts_count: analyticsData.top_posts.length
    }
  };
}

// ============================================================================
// Helper Functions
// ============================================================================

/**
 * Check if analytics data is cached
 */
async function checkAnalyticsCache(
  integrationId: string,
  startDate: string,
  endDate: string
) {
  try {
    const { createClient } = await import('@supabase/supabase-js');
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );

    const result = await supabase
      .from('platform_analytics')
      .select('*')
      .eq('account_id', integrationId)
      .eq('platform', 'facebook')
      .eq('date_range_start', startDate)
      .eq('date_range_end', endDate)
      .gte('created_at', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()) // Cache for 24 hours
      .single();

    if (result.error || !result.data) {
      return { success: false, data: null };
    }

    return {
      success: true,
      data: {
        page_id: result.data.metrics.page_id || integrationId,
        date_range: {
          start: startDate,
          end: endDate
        },
        metrics: result.data.metrics,
        top_posts: result.data.top_posts || [],
        created_at: result.data.created_at
      }
    };
  } catch (error) {
    return { success: false, data: null };
  }
}

/**
 * Cache analytics data
 */
async function cacheAnalyticsData(
  integrationId: string,
  startDate: string,
  endDate: string,
  analyticsData: any
) {
  try {
    const { createClient } = await import('@supabase/supabase-js');
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );

    await supabase
      .from('platform_analytics')
      .upsert({
        account_id: integrationId,
        platform: 'facebook',
        date_range_start: startDate,
        date_range_end: endDate,
        metrics: analyticsData.metrics,
        top_posts: analyticsData.top_posts,
        insights: {},
        audience_data: {}
      }, {
        onConflict: 'account_id,date_range_start,date_range_end'
      });
  } catch (error) {
    // Ignore cache errors
    console.error('Failed to cache Facebook analytics:', error);
  }
}

/**
 * GET /api/v1/integrations/facebook/analytics
 * Fetch Facebook page analytics
 */
export const GET = withVersioning(withErrorHandler(facebookAnalyticsHandler));
