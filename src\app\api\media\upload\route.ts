/**
 * Media Upload API Route
 * Demonstrates comprehensive Zod validation for multipart form data
 */

import { NextRequest } from 'next/server';
import { mediaService } from '@/lib/services';
import { 
  withError<PERSON><PERSON><PERSON>, 
  requireA<PERSON>,
  ErrorFactory
} from '@/lib/error-handler';
import { z } from 'zod';
import { 
  validateFormData,
  AllowedFileTypesSchema,
  UUIDSchema
} from '@/lib/validation';

/**
 * Schema for file upload form data
 */
const UploadFormSchema = z.object({
  file: z.object({
    name: z.string().min(1, 'File name is required').max(255, 'File name too long'),
    size: z.number().int().min(1, 'File must have content').max(100 * 1024 * 1024, 'File too large (max 100MB)'),
    type: AllowedFileTypesSchema,
    file: z.instanceof(File, { message: 'Valid file is required' })
  }),
  path: z.string().max(500, 'Path too long').optional(),
  description: z.string().max(1000, 'Description too long').optional(),
  tags: z.string().optional(),
  isPublic: z.string().optional()
}).strict();

/**
 * Internal handler for media upload
 */
async function uploadMediaHandler(req: NextRequest) {
  // 1. Authenticate user
  const { user } = await requireAuth(req);

  // 2. Validate form data
  const validatedData = await validateFormData(req, UploadFormSchema);

  // 3. Parse tags from string to array
  const tags = validatedData.tags
    ? validatedData.tags.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0)
    : [];

  // Validate tags array
  if (tags.length > 20) {
    throw ErrorFactory.validationError('Too many tags (max 20)', { tags });
  }
  if (tags.some(tag => tag.length > 50)) {
    throw ErrorFactory.validationError('Tag too long (max 50 characters)', { tags });
  }

  // Parse isPublic from string to boolean
  const isPublic = validatedData.isPublic === 'true';

  // 4. Additional business logic validation
  const file = validatedData.file.file;
  
  // Check file extension matches MIME type
  const fileExtension = file.name.split('.').pop()?.toLowerCase();
  const expectedExtensions: Record<string, string[]> = {
    'image/jpeg': ['jpg', 'jpeg'],
    'image/png': ['png'],
    'image/gif': ['gif'],
    'image/webp': ['webp'],
    'video/mp4': ['mp4'],
    'video/webm': ['webm'],
    'video/quicktime': ['mov'],
    'audio/mpeg': ['mp3'],
    'audio/wav': ['wav'],
    'audio/ogg': ['ogg']
  };

  const allowedExtensions = expectedExtensions[file.type];
  if (!allowedExtensions || !fileExtension || !allowedExtensions.includes(fileExtension)) {
    throw ErrorFactory.validationError(
      `File extension .${fileExtension} does not match MIME type ${file.type}`,
      { 
        fileExtension, 
        mimeType: file.type, 
        allowedExtensions 
      }
    );
  }

  // 4. Check user's storage quota (example business rule)
  const userMediaResult = await mediaService.getMediaAssets(user.id, 1, 0);
  if (userMediaResult.success) {
    // Example: Check if user has exceeded storage limit
    const totalSize = userMediaResult.data?.reduce((sum, asset) => sum + asset.file_size, 0) || 0;
    const maxStorage = 1024 * 1024 * 1024; // 1GB limit
    
    if (totalSize + file.size > maxStorage) {
      throw ErrorFactory.businessRuleViolation(
        'Storage quota exceeded. Please delete some files or upgrade your plan.',
        'storage_quota_exceeded'
      );
    }
  }

  // 5. Upload the file
  const uploadResult = await mediaService.uploadMedia({
    file,
    userId: user.id,
    path: validatedData.path
  });

  if (!uploadResult.success) {
    throw uploadResult.error;
  }

  // 6. Return success response
  return {
    media: uploadResult.data,
    metadata: {
      originalName: file.name,
      size: file.size,
      type: file.type,
      description: validatedData.description,
      tags: tags,
      isPublic: isPublic
    }
  };
}

/**
 * POST /api/media/upload
 * Upload a media file with comprehensive validation
 */
export const POST = withErrorHandler(uploadMediaHandler);

/**
 * Example of using the withValidation HOF for cleaner code
 */
import { withValidation } from '@/lib/validation';

// Alternative implementation using the validation HOF
const uploadMediaHandlerAlt = withValidation({
  // No body validation since we're using form data
})(async (req: NextRequest, { validatedQuery, validatedParams }) => {
  const { user } = await requireAuth(req);
  
  // Manual form validation since withValidation doesn't support FormData yet
  const validatedData = await validateFormData(req, UploadFormSchema);
  
  // Rest of the logic...
  const uploadResult = await mediaService.uploadMedia({
    file: validatedData.file.file,
    userId: user.id,
    path: validatedData.path
  });

  if (!uploadResult.success) {
    throw uploadResult.error;
  }

  return { media: uploadResult.data };
});

// You could export this alternative if preferred:
// export const POST = withErrorHandler(uploadMediaHandlerAlt);
