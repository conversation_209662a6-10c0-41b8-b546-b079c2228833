/** @type {import('next').NextConfig} */
const withBundleAnalyzer = require('@next/bundle-analyzer')({
  enabled: process.env.ANALYZE === 'true',
})

const nextConfig = {
  reactStrictMode: true,
  // Remove experimental optimizations that are causing errors
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'picsum.photos',
        port: '',
        pathname: '**',
      },
      {
        protocol: 'https',
        hostname: 'images.unsplash.com',
        port: '',
        pathname: '**',
      },
      {
        protocol: 'https',
        hostname: 'via.placeholder.com',
        port: '',
        pathname: '**',
      },
      {
        protocol: 'https',
        hostname: 'yt3.ggpht.com',
        port: '',
        pathname: '**',
      },
      {
        protocol: 'https',
        hostname: 'i.ytimg.com',
        port: '',
        pathname: '**',
      },
      {
        protocol: 'https',
        hostname: 'img.youtube.com',
        port: '',
        pathname: '**',
      },
    ],
    domains: [
      'yt3.ggpht.com',       // YouTube thumbnail domain
      'i.ytimg.com',         // YouTube image domain
      'i9.ytimg.com',        // YouTube image domain
      'img.youtube.com',     // YouTube video thumbnails
      'www.googleapis.com',  // Google APIs domain
      'via.placeholder.com', // Placeholder images
      'lh3.googleusercontent.com', // Google user content (profile pictures)
      'localhost'            // Local development
    ],
    unoptimized: true,       // Disable Next.js image optimization to avoid proxy issues
  },
  // Explicitly enable API routes
  rewrites: async () => {
    return [
      // General API routes
      {
        source: '/api/:path*',
        destination: '/api/:path*',
      },
      // Test routes
      {
        source: '/api/test',
        destination: '/api/test',
      },
      {
        source: '/api/youtube-test',
        destination: '/api/youtube-test',
      },
      // YouTube-specific routes
      {
        source: '/api/youtube/:path*',
        destination: '/api/youtube/:path*',
      },
      {
        source: '/api/youtube/get-token',
        destination: '/api/youtube/get-token',
      },
      {
        source: '/api/youtube/auth',
        destination: '/api/youtube/auth',
      },
      {
        source: '/api/youtube/callback',
        destination: '/api/youtube/callback',
      },
      // Google Drive routes
      {
        source: '/api/google-drive/callback',
        destination: '/api/google-drive/callback',
      },
    ];
  },
  // Add comprehensive security headers
  async headers() {
    const isProduction = process.env.NODE_ENV === 'production';

    return [
      {
        source: '/:path*',
        headers: [
          // HSTS - HTTP Strict Transport Security
          {
            key: 'Strict-Transport-Security',
            value: isProduction
              ? 'max-age=31536000; includeSubDomains; preload'
              : 'max-age=0' // Disable in development
          },
          // Prevent MIME type sniffing
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff'
          },
          // Prevent clickjacking
          {
            key: 'X-Frame-Options',
            value: 'DENY'
          },
          // Disable legacy XSS protection (CSP is better)
          {
            key: 'X-XSS-Protection',
            value: '0'
          },
          // Control referrer information
          {
            key: 'Referrer-Policy',
            value: 'strict-origin-when-cross-origin'
          },
          // Cross-Origin policies
          {
            key: 'Cross-Origin-Opener-Policy',
            value: 'same-origin'
          },
          {
            key: 'Cross-Origin-Resource-Policy',
            value: 'same-origin'
          },
          {
            key: 'Cross-Origin-Embedder-Policy',
            value: isProduction ? 'require-corp' : 'unsafe-none'
          },
          // Permissions Policy (restrict dangerous features)
          {
            key: 'Permissions-Policy',
            value: 'camera=(), microphone=(), geolocation=(), interest-cohort=()'
          },
          // Origin Agent Cluster
          {
            key: 'Origin-Agent-Cluster',
            value: '?1'
          },
        ],
      },
    ];
  },
};

module.exports = withBundleAnalyzer(nextConfig);