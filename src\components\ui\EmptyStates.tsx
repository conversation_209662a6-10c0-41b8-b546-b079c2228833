/**
 * Empty State Components
 * 
 * Meaningful empty states with clear messaging, helpful illustrations,
 * and actionable CTAs to guide users when no data is available.
 */

'use client'

import React, { memo } from 'react'
import { 
  FileText, 
  Calendar, 
  Users, 
  BarChart3, 
  Plus, 
  Search, 
  Filter,
  Wifi,
  AlertCircle,
  RefreshCw,
  Settings,
  Zap,
  MessageSquare,
  Image,
  Video
} from 'lucide-react'
import { Button } from '@/components/ui/Button'

interface EmptyStateProps {
  title: string
  description: string
  icon?: React.ComponentType<{ className?: string }>
  action?: {
    label: string
    onClick: () => void
    variant?: 'primary' | 'secondary'
  }
  secondaryAction?: {
    label: string
    onClick: () => void
  }
  className?: string
}

// Base empty state component
const EmptyState = memo<EmptyStateProps>(({
  title,
  description,
  icon: Icon = FileText,
  action,
  secondaryAction,
  className = ''
}) => {
  return (
    <div className={`flex flex-col items-center justify-center p-12 text-center ${className}`}>
      <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-6">
        <Icon className="w-8 h-8 text-gray-400" />
      </div>
      
      <h3 className="text-xl font-semibold text-gray-900 mb-3">{title}</h3>
      <p className="text-gray-500 mb-8 max-w-md leading-relaxed">{description}</p>
      
      <div className="flex flex-col sm:flex-row gap-3">
        {action && (
          <Button
            onClick={action.onClick}
            variant={action.variant === 'secondary' ? 'outline' : 'default'}
            className="inline-flex items-center"
          >
            <Plus className="w-4 h-4 mr-2" />
            {action.label}
          </Button>
        )}
        
        {secondaryAction && (
          <Button
            onClick={secondaryAction.onClick}
            variant="outline"
            className="inline-flex items-center"
          >
            {secondaryAction.label}
          </Button>
        )}
      </div>
    </div>
  )
})

EmptyState.displayName = 'EmptyState'

// Specific empty state components for different scenarios

export const EmptyPosts = memo<{
  onCreatePost?: () => void
  onConnectAccount?: () => void
  filter?: string
}>(({ onCreatePost, onConnectAccount, filter }) => {
  if (filter) {
    return (
      <EmptyState
        icon={Search}
        title="No posts match your filter"
        description={`We couldn't find any posts matching "${filter}". Try adjusting your search criteria or clearing the filter.`}
        action={onCreatePost ? {
          label: 'Create New Post',
          onClick: onCreatePost
        } : undefined}
        secondaryAction={{
          label: 'Clear Filter',
          onClick: () => window.location.reload()
        }}
      />
    )
  }

  return (
    <EmptyState
      icon={FileText}
      title="No posts yet"
      description="Start creating engaging content for your social media channels. Your first post is just a click away!"
      action={onCreatePost ? {
        label: 'Create Your First Post',
        onClick: onCreatePost
      } : undefined}
      secondaryAction={onConnectAccount ? {
        label: 'Connect Social Account',
        onClick: onConnectAccount
      } : undefined}
    />
  )
})

EmptyPosts.displayName = 'EmptyPosts'

export const EmptyScheduledPosts = memo<{
  onSchedulePost?: () => void
  onViewDrafts?: () => void
}>(({ onSchedulePost, onViewDrafts }) => (
  <EmptyState
    icon={Calendar}
    title="No scheduled posts"
    description="Keep your social media active by scheduling posts in advance. Plan your content calendar and never miss a posting opportunity."
    action={onSchedulePost ? {
      label: 'Schedule a Post',
      onClick: onSchedulePost
    } : undefined}
    secondaryAction={onViewDrafts ? {
      label: 'View Drafts',
      onClick: onViewDrafts
    } : undefined}
  />
))

EmptyScheduledPosts.displayName = 'EmptyScheduledPosts'

export const EmptyDrafts = memo<{
  onCreateDraft?: () => void
  onViewTemplates?: () => void
}>(({ onCreateDraft, onViewTemplates }) => (
  <EmptyState
    icon={FileText}
    title="No drafts saved"
    description="Save your work-in-progress posts as drafts. Perfect for when you need time to refine your content before publishing."
    action={onCreateDraft ? {
      label: 'Create Draft',
      onClick: onCreateDraft
    } : undefined}
    secondaryAction={onViewTemplates ? {
      label: 'Browse Templates',
      onClick: onViewTemplates
    } : undefined}
  />
))

EmptyDrafts.displayName = 'EmptyDrafts'

export const EmptyAnalytics = memo<{
  onPublishPost?: () => void
  onViewInsights?: () => void
}>(({ onPublishPost, onViewInsights }) => (
  <EmptyState
    icon={BarChart3}
    title="No analytics data yet"
    description="Start publishing posts to see detailed analytics about your social media performance, engagement rates, and audience insights."
    action={onPublishPost ? {
      label: 'Publish Your First Post',
      onClick: onPublishPost
    } : undefined}
    secondaryAction={onViewInsights ? {
      label: 'Learn About Analytics',
      onClick: onViewInsights
    } : undefined}
  />
))

EmptyAnalytics.displayName = 'EmptyAnalytics'

export const EmptyConnectedAccounts = memo<{
  onConnectAccount?: () => void
  onLearnMore?: () => void
}>(({ onConnectAccount, onLearnMore }) => (
  <EmptyState
    icon={Users}
    title="No connected accounts"
    description="Connect your social media accounts to start managing all your content from one place. We support Facebook, Instagram, Twitter, LinkedIn, and more."
    action={onConnectAccount ? {
      label: 'Connect Account',
      onClick: onConnectAccount
    } : undefined}
    secondaryAction={onLearnMore ? {
      label: 'Learn More',
      onClick: onLearnMore
    } : undefined}
  />
))

EmptyConnectedAccounts.displayName = 'EmptyConnectedAccounts'

export const EmptyComments = memo<{
  onEngageAudience?: () => void
  onViewPosts?: () => void
}>(({ onEngageAudience, onViewPosts }) => (
  <EmptyState
    icon={MessageSquare}
    title="No comments yet"
    description="Engage with your audience by creating content that sparks conversations. Comments and interactions will appear here."
    action={onEngageAudience ? {
      label: 'Create Engaging Content',
      onClick: onEngageAudience
    } : undefined}
    secondaryAction={onViewPosts ? {
      label: 'View Published Posts',
      onClick: onViewPosts
    } : undefined}
  />
))

EmptyComments.displayName = 'EmptyComments'

export const EmptyMedia = memo<{
  onUploadMedia?: () => void
  onBrowseStock?: () => void
}>(({ onUploadMedia, onBrowseStock }) => (
  <EmptyState
    icon={Image}
    title="No media files"
    description="Upload images, videos, and other media files to enhance your social media posts. Visual content drives higher engagement."
    action={onUploadMedia ? {
      label: 'Upload Media',
      onClick: onUploadMedia
    } : undefined}
    secondaryAction={onBrowseStock ? {
      label: 'Browse Stock Photos',
      onClick: onBrowseStock
    } : undefined}
  />
))

EmptyMedia.displayName = 'EmptyMedia'

// Error states
export const ErrorState = memo<{
  onRetry?: () => void
  onSupport?: () => void
  title?: string
  description?: string
}>(({ 
  onRetry, 
  onSupport,
  title = "Something went wrong",
  description = "We encountered an error while loading your data. Please try again or contact support if the problem persists."
}) => (
  <EmptyState
    icon={AlertCircle}
    title={title}
    description={description}
    action={onRetry ? {
      label: 'Try Again',
      onClick: onRetry,
      variant: 'primary'
    } : undefined}
    secondaryAction={onSupport ? {
      label: 'Contact Support',
      onClick: onSupport
    } : undefined}
    className="text-red-600"
  />
))

ErrorState.displayName = 'ErrorState'

export const OfflineState = memo<{
  onRetry?: () => void
}>(({ onRetry }) => (
  <EmptyState
    icon={Wifi}
    title="You're offline"
    description="Check your internet connection and try again. Some features may not be available while offline."
    action={onRetry ? {
      label: 'Retry',
      onClick: onRetry
    } : undefined}
    className="text-orange-600"
  />
))

OfflineState.displayName = 'OfflineState'

export const LoadingState = memo<{
  title?: string
  description?: string
}>(({ 
  title = "Loading...",
  description = "Please wait while we fetch your data."
}) => (
  <div className="flex flex-col items-center justify-center p-12 text-center">
    <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-6">
      <RefreshCw className="w-8 h-8 text-blue-600 animate-spin" />
    </div>
    
    <h3 className="text-xl font-semibold text-gray-900 mb-3">{title}</h3>
    <p className="text-gray-500 max-w-md leading-relaxed">{description}</p>
  </div>
))

LoadingState.displayName = 'LoadingState'

export default EmptyState
export { EmptyState }
