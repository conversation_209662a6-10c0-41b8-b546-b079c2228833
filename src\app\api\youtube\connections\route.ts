import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';

/**
 * GET /api/youtube/connections
 * Fetches all YouTube channel connections for the authenticated user
 */
export async function GET(request: NextRequest) {
  try {
    const supabase = createClient();
    
    // Get the authenticated user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Fetch YouTube connections from the new table
    const { data: connections, error: connectionsError } = await supabase
      .from('youtube_connections')
      .select('*')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false });

    if (connectionsError) {
      console.error('Error fetching YouTube connections:', connectionsError);
      return NextResponse.json(
        { error: 'Failed to fetch connections', details: connectionsError.message },
        { status: 500 }
      );
    }

    // Transform the data to match the expected frontend format
    const transformedConnections = connections.map(conn => ({
      id: conn.id,
      channelId: conn.youtube_channel_id,
      channelTitle: conn.youtube_channel_title,
      channelThumbnail: conn.youtube_channel_thumbnail,
      googleEmail: conn.google_account_email,
      createdAt: conn.created_at,
      updatedAt: conn.updated_at,
      // For backward compatibility with existing frontend code
      provider: 'google',
      service_type: 'youtube',
      platform_account_id: conn.youtube_channel_id,
      platform_account_name: conn.youtube_channel_title,
      profile_picture_url: conn.youtube_channel_thumbnail,
      provider_account_email: conn.google_account_email,
      metadata: {
        platform: 'youtube',
        channel_id: conn.youtube_channel_id,
        channel_title: conn.youtube_channel_title,
        channel_thumbnail: conn.youtube_channel_thumbnail
      }
    }));

    return NextResponse.json({
      success: true,
      connections: transformedConnections,
      count: transformedConnections.length
    });

  } catch (error) {
    console.error('Error in YouTube connections API:', error);
    return NextResponse.json(
      { error: 'Internal server error', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}
