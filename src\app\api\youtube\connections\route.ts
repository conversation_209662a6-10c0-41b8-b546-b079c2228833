import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';

/**
 * GET /api/youtube/connections
 * Fetches all YouTube channel connections for the authenticated user
 */
export async function GET(request: NextRequest) {
  try {
    const supabase = createClient();
    
    // Get the authenticated user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // First, fetch YouTube connections from the new table
    const { data: youtubeConnections, error: connectionsError } = await supabase
      .from('youtube_connections')
      .select('*')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false });

    if (connectionsError) {
      console.error('Error fetching YouTube connections:', connectionsError);
      return NextResponse.json(
        { error: 'Failed to fetch connections', details: connectionsError.message },
        { status: 500 }
      );
    }

    if (!youtubeConnections || youtubeConnections.length === 0) {
      return NextResponse.json({
        success: true,
        connections: [],
        count: 0
      });
    }

    // Get the channel IDs to look up in connected_accounts
    const channelIds = youtubeConnections.map(conn => conn.youtube_channel_id);

    // Fetch corresponding connected_accounts records
    const { data: connectedAccounts, error: accountsError } = await supabase
      .from('connected_accounts')
      .select('id, provider, platform_account_id, platform_account_name, auto_reply_enabled')
      .eq('user_id', user.id)
      .in('platform_account_id', channelIds);

    if (accountsError) {
      console.error('Error fetching connected accounts:', accountsError);
      // Continue without connected accounts data rather than failing completely
    }

    // Create a map for quick lookup
    const accountsMap = new Map();
    if (connectedAccounts) {
      connectedAccounts.forEach(account => {
        accountsMap.set(account.platform_account_id, account);
      });
    }

    // Transform the data to match the expected frontend format
    const transformedConnections = youtubeConnections.map(conn => {
      // Get the corresponding connected_accounts record
      const connectedAccount = accountsMap.get(conn.youtube_channel_id);

      return {
        // Use the connected_accounts ID for auto-comment compatibility, fallback to youtube_connections ID
        id: connectedAccount?.id || conn.id,
        channelId: conn.youtube_channel_id,
        channelTitle: conn.youtube_channel_title,
        channelThumbnail: conn.youtube_channel_thumbnail,
        googleEmail: conn.google_account_email,
        createdAt: conn.created_at,
        updatedAt: conn.updated_at,
        // For backward compatibility with existing frontend code
        provider: 'google',
        service_type: 'youtube',
        platform_account_id: conn.youtube_channel_id,
        platform_account_name: conn.youtube_channel_title,
        profile_picture_url: conn.youtube_channel_thumbnail,
        provider_account_email: conn.google_account_email,
        auto_reply_enabled: connectedAccount?.auto_reply_enabled || false,
        metadata: {
          platform: 'youtube',
          channel_id: conn.youtube_channel_id,
          channel_title: conn.youtube_channel_title,
          channel_thumbnail: conn.youtube_channel_thumbnail
        }
      };
    });

    return NextResponse.json({
      success: true,
      connections: transformedConnections,
      count: transformedConnections.length
    });

  } catch (error) {
    console.error('Error in YouTube connections API:', error);
    return NextResponse.json(
      { error: 'Internal server error', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}
