/**
 * PAY-004: Webhook Status and Monitoring API
 * 
 * API endpoint for monitoring webhook health and viewing recent webhook events.
 * Provides insights into webhook processing across all payment providers.
 */

import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { z } from 'zod';

// Validation schema
const webhookStatusQuerySchema = z.object({
  provider: z.enum(['stripe', 'paypal', 'square', 'razorpay', 'paddle']).optional(),
  limit: z.string().transform(Number).pipe(z.number().min(1).max(100)).optional(),
  hours: z.string().transform(Number).pipe(z.number().min(1).max(168)).optional() // Max 1 week
});

/**
 * GET /api/v1/webhooks/status
 * Get webhook processing status and recent events
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const queryParams = Object.fromEntries(searchParams.entries());
    
    // Validate query parameters
    const validatedParams = webhookStatusQuerySchema.parse(queryParams);
    
    const supabase = createClient();
    
    // Get recent billing events (webhook processing results)
    const hoursAgo = validatedParams.hours || 24;
    const limit = validatedParams.limit || 50;
    const since = new Date(Date.now() - hoursAgo * 60 * 60 * 1000).toISOString();
    
    let query = supabase
      .from('billing_events')
      .select('*')
      .gte('created_at', since)
      .order('created_at', { ascending: false })
      .limit(limit);
    
    if (validatedParams.provider) {
      query = query.eq('payment_provider', validatedParams.provider);
    }
    
    const { data: events, error: eventsError } = await query;
    
    if (eventsError) {
      throw eventsError;
    }
    
    // Get webhook statistics
    const stats = await getWebhookStatistics(supabase, since, validatedParams.provider);
    
    // Get provider health status
    const providerHealth = await getProviderHealthStatus(supabase, since);
    
    return NextResponse.json({
      success: true,
      data: {
        statistics: stats,
        providerHealth,
        recentEvents: events || [],
        timeRange: {
          since,
          hours: hoursAgo
        }
      }
    });

  } catch (error) {
    console.error('Error getting webhook status:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          success: false, 
          error: { 
            message: 'Invalid query parameters', 
            code: 'VALIDATION_ERROR',
            details: error.errors 
          } 
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { 
        success: false, 
        error: { 
          message: 'Failed to get webhook status', 
          code: 'WEBHOOK_STATUS_ERROR' 
        } 
      },
      { status: 500 }
    );
  }
}

/**
 * Get webhook processing statistics
 */
async function getWebhookStatistics(
  supabase: any, 
  since: string, 
  provider?: string
): Promise<any> {
  let query = supabase
    .from('billing_events')
    .select('payment_provider, event_type, status, created_at')
    .gte('created_at', since);
  
  if (provider) {
    query = query.eq('payment_provider', provider);
  }
  
  const { data: events, error } = await query;
  
  if (error) {
    throw error;
  }
  
  const stats = {
    totalEvents: events?.length || 0,
    eventsByProvider: {} as Record<string, number>,
    eventsByType: {} as Record<string, number>,
    successfulEvents: 0,
    failedEvents: 0,
    processingRate: 0
  };
  
  if (events) {
    events.forEach((event: any) => {
      // Count by provider
      stats.eventsByProvider[event.payment_provider] = 
        (stats.eventsByProvider[event.payment_provider] || 0) + 1;
      
      // Count by type
      stats.eventsByType[event.event_type] = 
        (stats.eventsByType[event.event_type] || 0) + 1;
      
      // Count success/failure (assuming processed_at indicates success)
      if (event.processed_at) {
        stats.successfulEvents++;
      } else {
        stats.failedEvents++;
      }
    });
    
    stats.processingRate = stats.totalEvents > 0 
      ? (stats.successfulEvents / stats.totalEvents) * 100 
      : 0;
  }
  
  return stats;
}

/**
 * Get provider health status
 */
async function getProviderHealthStatus(
  supabase: any, 
  since: string
): Promise<any> {
  const { data: events, error } = await supabase
    .from('billing_events')
    .select('payment_provider, event_type, status, created_at, processed_at')
    .gte('created_at', since);
  
  if (error) {
    throw error;
  }
  
  const providers = ['stripe', 'paypal', 'square', 'razorpay', 'paddle'];
  const health: Record<string, any> = {};
  
  providers.forEach(provider => {
    const providerEvents = events?.filter((e: any) => e.payment_provider === provider) || [];
    const totalEvents = providerEvents.length;
    const successfulEvents = providerEvents.filter((e: any) => e.processed_at).length;
    const failedEvents = totalEvents - successfulEvents;
    
    let status = 'healthy';
    if (totalEvents === 0) {
      status = 'no_activity';
    } else if (failedEvents / totalEvents > 0.1) { // More than 10% failure rate
      status = 'degraded';
    } else if (failedEvents / totalEvents > 0.25) { // More than 25% failure rate
      status = 'unhealthy';
    }
    
    health[provider] = {
      status,
      totalEvents,
      successfulEvents,
      failedEvents,
      successRate: totalEvents > 0 ? (successfulEvents / totalEvents) * 100 : 0,
      lastEventAt: providerEvents.length > 0 
        ? providerEvents[0].created_at 
        : null
    };
  });
  
  return health;
}

/**
 * POST /api/v1/webhooks/status/test
 * Test webhook endpoint (for development)
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { provider, eventType, testData } = body;
    
    if (!provider || !eventType) {
      return NextResponse.json(
        { 
          success: false, 
          error: { 
            message: 'Provider and eventType are required', 
            code: 'MISSING_PARAMETERS' 
          } 
        },
        { status: 400 }
      );
    }
    
    // Create a test billing event
    const supabase = createClient();
    
    const { data, error } = await supabase
      .from('billing_events')
      .insert({
        payment_provider: provider,
        event_type: eventType,
        provider_event_id: `test_${Date.now()}`,
        data: testData || { test: true },
        status: 'test',
        processed_at: new Date().toISOString()
      })
      .select()
      .single();
    
    if (error) {
      throw error;
    }
    
    return NextResponse.json({
      success: true,
      data: {
        message: 'Test webhook event created',
        billingEvent: data
      }
    });

  } catch (error) {
    console.error('Error creating test webhook:', error);
    
    return NextResponse.json(
      { 
        success: false, 
        error: { 
          message: 'Failed to create test webhook event', 
          code: 'TEST_WEBHOOK_ERROR' 
        } 
      },
      { status: 500 }
    );
  }
}
