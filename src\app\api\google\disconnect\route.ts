import { OAuth2Client } from 'google-auth-library';
import { NextResponse } from 'next/server';
import { supabaseAdmin } from '@/lib/supabase';

const oauth2Client = new OAuth2Client({
  clientId: process.env.GOOGLE_CLIENT_ID!,
  clientSecret: process.env.GOOGLE_CLIENT_SECRET!
});

export async function POST() {
  try {
    // Get the current authenticated user
    const { data: { user } } = await supabaseAdmin.auth.getUser();
    
    if (!user) {
      return NextResponse.json({ 
        success: false, 
        message: 'User not authenticated' 
      }, { status: 401 });
    }
    
    // Get the Google account from Supabase
    const { data: account } = await supabaseAdmin
      .from('connected_accounts')
      .select('access_token')
      .eq('user_id', user.id)
      .eq('provider', 'google')
      .single();
    
    // Revoke the token if it exists
    if (account?.access_token) {
      try {
        await oauth2Client.revokeToken(account.access_token);
      } catch (revokeError) {
        console.error('Error revoking token:', revokeError);
        // Continue with disconnection even if revocation fails
      }
    }
    
    // Delete the account from Supabase
    await supabaseAdmin
      .from('connected_accounts')
      .delete()
      .eq('user_id', user.id)
      .eq('provider', 'google');
    
    return NextResponse.json({ 
      success: true, 
      message: 'Successfully disconnected from Google' 
    });
  } catch (error) {
    console.error('Error disconnecting from Google:', error);
    
    return NextResponse.json({ 
      success: false, 
      message: 'Error disconnecting from Google' 
    }, { status: 500 });
  }
} 