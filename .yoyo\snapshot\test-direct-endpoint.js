const https = require('https');
const fs = require('fs');
const path = require('path');

// Get the ngrok URL from the .env file
const envContent = fs.readFileSync(path.resolve('.env'), 'utf8');
const ngrokMatch = envContent.match(/NGROK_URL=(.+)/);
let ngrokUrl = ngrokMatch ? ngrokMatch[1].trim().replace(/^"|"$/g, '') : '';

// If no ngrok URL found, use a hardcoded one for testing
if (!ngrokUrl) {
  ngrokUrl = 'https://7861-2404-8ec0-4-2d98-a950-a216-4887-d919.ngrok-free.app';
}

console.log(`Using ngrok URL: ${ngrokUrl}`);

// Create the endpoint URL
const endpointUrl = `${ngrokUrl}/api/qstash-test`;
console.log(`Testing endpoint: ${endpointUrl}`);

// Parse the URL
const parsedUrl = new URL(endpointUrl);

// Set up the request options
const options = {
  hostname: parsedUrl.hostname,
  port: 443,
  path: parsedUrl.pathname,
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  }
};

console.log(`Making direct HTTPS request to: ${parsedUrl.hostname}${parsedUrl.pathname}`);

// Create a payload
const payload = JSON.stringify({
  postId: 'direct-test',
  timestamp: new Date().toISOString()
});

// Make the request
const req = https.request(options, (res) => {
  console.log(`Status Code: ${res.statusCode}`);
  console.log('Headers:', JSON.stringify(res.headers, null, 2));
  
  let data = '';
  res.on('data', (chunk) => {
    data += chunk;
  });
  
  res.on('end', () => {
    console.log(`Response body: ${data}`);
    try {
      const jsonResponse = JSON.parse(data);
      console.log('Parsed JSON response:');
      console.log(JSON.stringify(jsonResponse, null, 2));
    } catch (e) {
      console.log('Response is not valid JSON');
    }
  });
});

req.on('error', (error) => {
  console.error(`Request error: ${error.message}`);
});

// Send the request
req.write(payload);
req.end();

console.log(`Request sent to ${endpointUrl} with payload:`, payload); 