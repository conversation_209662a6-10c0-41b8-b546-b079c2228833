import { NextResponse } from 'next/server';
import { OAuth2Client } from 'google-auth-library';
import { supabaseAdmin } from '@/lib/supabase';

// Google OAuth configuration
const GOOGLE_CLIENT_ID = process.env.GOOGLE_CLIENT_ID || '';
const GOOGLE_CLIENT_SECRET = process.env.GOOGLE_CLIENT_SECRET || '';

export async function GET() {
  try {
    // Get the current authenticated user
    const { data: { user } } = await supabaseAdmin.auth.getUser();
    
    if (!user) {
      return NextResponse.json(
        { connected: false, message: 'User not authenticated' },
        { status: 401 }
      );
    }

    // Check if user has a connected Google account
    const { data: account, error } = await supabaseAdmin
      .from('connected_accounts')
      .select('*')
      .eq('user_id', user.id)
      .eq('provider', 'google')
      .single();

    if (error || !account) {
      return NextResponse.json(
        { connected: false, message: 'No Google account connected' },
        { status: 200 }
      );
    }

    // Initialize OAuth client
    const oauth2Client = new OAuth2Client(
      GOOGLE_CLIENT_ID,
      GOOGLE_CLIENT_SECRET
    );

    // Set credentials
    oauth2Client.setCredentials({
      access_token: account.access_token,
      refresh_token: account.refresh_token,
      expiry_date: account.expires_at ? new Date(account.expires_at).getTime() : undefined
    });

    // Check if token is expired and needs refresh
    const isTokenExpired = account.expires_at && new Date(account.expires_at) <= new Date();
    
    if (isTokenExpired && account.refresh_token) {
      try {
        // Refresh the token
        const { credentials } = await oauth2Client.refreshAccessToken();
        
        // Update the token in the database
        const expiresAt = credentials.expiry_date 
          ? new Date(credentials.expiry_date).toISOString()
          : new Date(Date.now() + 3600 * 1000).toISOString();
        
        await supabaseAdmin
          .from('connected_accounts')
          .update({
            access_token: credentials.access_token,
            refresh_token: credentials.refresh_token || account.refresh_token,
            expires_at: expiresAt,
            updated_at: new Date().toISOString()
          })
          .eq('id', account.id);
      } catch (refreshError) {
        console.error('Error refreshing token:', refreshError);
        return NextResponse.json(
          { connected: false, message: 'Failed to refresh token' },
          { status: 401 }
        );
      }
    }

    // Verify token by making a test request
    try {
      const response = await fetch(
        'https://www.googleapis.com/oauth2/v2/userinfo',
        {
          headers: {
            Authorization: `Bearer ${account.access_token}`,
          },
        }
      );

      if (!response.ok) {
        throw new Error(`Token validation failed: ${response.statusText}`);
      }

      const userInfo = await response.json();

      return NextResponse.json({
        connected: true,
        email: userInfo.email,
        name: userInfo.name,
        picture: userInfo.picture
      });
    } catch (validationError) {
      console.error('Token validation error:', validationError);
      
      // If token validation fails, mark as disconnected
      return NextResponse.json(
        { connected: false, message: 'Invalid or expired token' },
        { status: 401 }
      );
    }
  } catch (error) {
    console.error('Error checking Google connection status:', error);
    return NextResponse.json(
      { connected: false, message: 'Error checking connection status' },
      { status: 500 }
    );
  }
} 