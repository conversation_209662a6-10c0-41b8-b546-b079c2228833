/**
 * Dashboard Stats Component
 *
 * Client component that uses React Query to fetch and display dashboard statistics
 * Optimized with React.memo, useMemo, and useCallback for performance
 */

'use client'

import React, { memo, useMemo, useCallback } from 'react'
import { BarChart3, Users, Calendar, Zap } from 'lucide-react'
import { useBillingOverview } from '@/hooks/api/useBilling'
import { useConnectedAccounts } from '@/hooks/api/useAccounts'
import { useAnalyticsOverview } from '@/hooks/api/useAnalytics'
import { SkeletonMetricCard } from '@/components/ui/Skeleton'

interface QuickStatsCardProps {
  title: string
  value: string
  icon: React.ComponentType<{ className?: string }>
  description: string
  isLoading?: boolean
}

// Memoized QuickStatsCard component to prevent unnecessary re-renders
const QuickStatsCard = memo<QuickStatsCardProps>(({ title, value, icon: Icon, description, isLoading }) => {
  // Memoize the card content to avoid re-rendering when props haven't changed
  const cardContent = useMemo(() => {
    if (isLoading) {
      return <SkeletonMetricCard />
    }

    return (
      <div className="bg-white rounded-xl border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-4">
          <div className="p-2 bg-blue-100 rounded-lg">
            <Icon className="w-6 h-6 text-blue-600" />
          </div>
          <div className="text-right">
            <div className="text-2xl font-bold text-gray-900">{value}</div>
            <div className="text-sm text-gray-500">{title}</div>
          </div>
        </div>
        <p className="text-sm text-gray-600">{description}</p>
      </div>
    )
  }, [title, value, Icon, description, isLoading])

  return cardContent
})

QuickStatsCard.displayName = 'QuickStatsCard'

// Memoized DashboardStats component
export const DashboardStats = memo(() => {
  // Fetch data using React Query hooks
  const { data: billingData, isLoading: billingLoading } = useBillingOverview()
  const { data: accounts, isLoading: accountsLoading } = useConnectedAccounts()
  const { data: analytics, isLoading: analyticsLoading } = useAnalyticsOverview()

  // Memoize calculated stats to avoid recalculation on every render
  const stats = useMemo(() => {
    const connectedAccountsCount = accounts?.length ?? 0
    const postsThisMonth = analytics?.totalPosts ?? 0
    const totalReach = analytics?.totalViews ?? 0
    const engagementRate = analytics?.totalEngagement
      ? `${((analytics.totalEngagement / analytics.totalViews) * 100).toFixed(1)}%`
      : '0%'

    return {
      connectedAccountsCount,
      postsThisMonth,
      totalReach,
      engagementRate
    }
  }, [accounts?.length, analytics?.totalPosts, analytics?.totalViews, analytics?.totalEngagement])

  // Memoize format number function to avoid recreation on every render
  const formatNumber = useCallback((num: number): string => {
    if (num >= 1000000) {
      return `${(num / 1000000).toFixed(1)}M`
    }
    if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}K`
    }
    return num.toString()
  }, [])

  // Memoize the stats cards to avoid recreation when data hasn't changed
  const statsCards = useMemo(() => [
    {
      title: "Connected Accounts",
      value: stats.connectedAccountsCount.toString(),
      icon: Users,
      description: "Social media accounts connected",
      isLoading: accountsLoading
    },
    {
      title: "Posts This Month",
      value: stats.postsThisMonth.toString(),
      icon: Calendar,
      description: "Content published across platforms",
      isLoading: analyticsLoading
    },
    {
      title: "Total Reach",
      value: formatNumber(stats.totalReach),
      icon: BarChart3,
      description: "People reached this month",
      isLoading: analyticsLoading
    },
    {
      title: "Engagement Rate",
      value: stats.engagementRate,
      icon: Zap,
      description: "Average engagement across posts",
      isLoading: analyticsLoading
    }
  ], [stats, formatNumber, accountsLoading, analyticsLoading])

  return (
    <section>
      <h2 className="text-xl font-semibold text-gray-900 mb-4">Quick Stats</h2>
      <div className="grid md:grid-cols-2 gap-6">
        {statsCards.map((card, index) => (
          <QuickStatsCard
            key={card.title}
            title={card.title}
            value={card.value}
            icon={card.icon}
            description={card.description}
            isLoading={card.isLoading}
          />
        ))}
      </div>
    </section>
  )
})

DashboardStats.displayName = 'DashboardStats'

// Memoized SubscriptionOverview component
export const SubscriptionOverview = memo(() => {
  const { data: billingData, isLoading, error } = useBillingOverview()

  // Memoize subscription data to avoid recalculation - must be called before any early returns
  const subscriptionData = useMemo(() => {
    const subscription = billingData?.subscription
    const planName = subscription?.plan || 'Free Plan'
    const status = subscription?.status || 'active'
    const amount = billingData?.nextBilling?.amount || 0
    const currency = billingData?.nextBilling?.currency || 'USD'

    return { planName, status, amount, currency }
  }, [billingData])

  if (isLoading) {
    return (
      <section>
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Your Subscription</h2>
        <div className="bg-gray-100 rounded-xl border-2 p-6">
          <div className="animate-pulse">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-3">
                <div className="w-6 h-6 bg-gray-300 rounded"></div>
                <div>
                  <div className="h-5 bg-gray-300 rounded w-20 mb-1"></div>
                  <div className="h-4 bg-gray-300 rounded w-12"></div>
                </div>
              </div>
              <div className="text-right">
                <div className="h-8 bg-gray-300 rounded w-12 mb-1"></div>
                <div className="h-4 bg-gray-300 rounded w-16"></div>
              </div>
            </div>
            <div className="flex space-x-3">
              <div className="flex-1 h-10 bg-gray-300 rounded-lg"></div>
              <div className="flex-1 h-10 bg-gray-300 rounded-lg"></div>
            </div>
          </div>
        </div>
      </section>
    )
  }

  if (error) {
    return (
      <section>
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Your Subscription</h2>
        <div className="bg-red-50 border border-red-200 rounded-xl p-6">
          <p className="text-red-600">Failed to load subscription information</p>
        </div>
      </section>
    )
  }

  return (
    <section>
      <h2 className="text-xl font-semibold text-gray-900 mb-4">Your Subscription</h2>
      <div className="bg-gray-100 text-gray-800 border-gray-200 rounded-xl border-2 p-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-3">
            <Zap className="w-6 h-6" />
            <div>
              <h3 className="text-lg font-semibold">{subscriptionData.planName}</h3>
              <p className="text-sm opacity-75 capitalize">{subscriptionData.status}</p>
            </div>
          </div>
          <div className="text-right">
            <div className="text-2xl font-bold">
              {subscriptionData.currency === 'USD' ? '$' : ''}{subscriptionData.amount}
            </div>
            <div className="text-sm opacity-75">per month</div>
          </div>
        </div>

        <div className="flex space-x-3">
          <a
            href="/dashboard/billing"
            className="flex-1 inline-flex items-center justify-center px-4 py-2 bg-white/50 hover:bg-white/75 rounded-lg font-medium transition-colors"
          >
            <BarChart3 className="w-4 h-4 mr-2" />
            Manage Billing
          </a>
          <a
            href="/dashboard/upgrade"
            className="flex-1 inline-flex items-center justify-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors"
          >
            <Zap className="w-4 h-4 mr-2" />
            Upgrade
          </a>
        </div>
      </div>
    </section>
  )
})

SubscriptionOverview.displayName = 'SubscriptionOverview'
