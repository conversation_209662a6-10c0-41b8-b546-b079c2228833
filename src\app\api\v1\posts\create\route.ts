/**
 * API v1 - Create Post Route
 * 
 * Version 1 of the create post API with legacy response format
 */

import { NextRequest } from 'next/server';
import { postService } from '@/lib/services';
import { 
  withErrorHandler, 
  requireAuth
} from '@/lib/error-handler';
import { 
  validateRe<PERSON>Body,
  CreatePostSchema
} from '@/lib/validation';
import { withVersioning } from '@/lib/versioning/middleware';

/**
 * Internal handler for post creation (V1 format)
 */
async function createPostHandlerV1(req: NextRequest) {
  // 1. Authenticate user
  const { user } = await requireAuth(req);

  // 2. Validate request body
  const validatedData = await validateRequestBody(req, CreatePostSchema);

  // 3. Prepare post data for the service
  const createPostData = {
    user_id: user.id,
    title: validatedData.title,
    content: validatedData.content,
    status: validatedData.status,
    scheduled_at: validatedData.scheduledDate,
    youtube_settings: validatedData.youtubeSettings,
    mediaIds: validatedData.mediaIds,
    channels: validatedData.channels,
    tags: validatedData.tags
  };

  // 4. Call service to create the post
  const result = await postService.createPost(createPostData);

  // 5. Handle service result
  if (!result.success) {
    throw result.error;
  }

  // 6. Return response in V1 format (legacy format)
  const responseData: any = {
    success: true,
    post: result.data,
    message: 'Post created successfully'
  };

  // Add V1-specific scheduling information
  if (validatedData.status === 'scheduled' && validatedData.scheduledDate) {
    responseData.scheduled = true;
    responseData.scheduledFor = validatedData.scheduledDate;
  }

  return responseData;
}

/**
 * POST /api/v1/posts/create
 * Create a new post (V1 format)
 */
export const POST = withVersioning(withErrorHandler(createPostHandlerV1));
