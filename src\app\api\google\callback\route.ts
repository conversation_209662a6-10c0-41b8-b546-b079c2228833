import { NextResponse } from 'next/server';
import { OAuth2Client } from 'google-auth-library';
import { supabaseAdmin } from '@/lib/supabase';

// Google OAuth configuration
const GOOGLE_CLIENT_ID = process.env.GOOGLE_CLIENT_ID || '';
const GOOGLE_CLIENT_SECRET = process.env.GOOGLE_CLIENT_SECRET || '';
const REDIRECT_URI = process.env.GOOGLE_REDIRECT_URI || 'http://localhost:3000/api/google/callback';
const FRONTEND_URL = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';

export async function GET(request: Request) {
  try {
    const url = new URL(request.url);
    const code = url.searchParams.get('code');
    const stateParam = url.searchParams.get('state'); // This should be the JSON string with userId and returnTo
    const error = url.searchParams.get('error');

    // Handle authentication errors
    if (error) {
      console.error('Google OAuth error:', error);
      return NextResponse.redirect(
        `${FRONTEND_URL}?error=${encodeURIComponent('Authentication failed: ' + error)}`
      );
    }

    // Validate required parameters
    if (!code || !stateParam) {
      console.error('Missing required parameters:', { code, stateParam });
      return NextResponse.redirect(
        `${FRONTEND_URL}?error=${encodeURIComponent('Missing required authentication parameters')}`
      );
    }

    // Parse state parameter
    let state;
    let userId;
    let returnTo = '/publish';
    
    try {
      state = JSON.parse(stateParam);
      userId = state.userId;
      returnTo = state.returnTo || '/publish';
    } catch (e) {
      // For backward compatibility, if state is not JSON, assume it's just the userId
      userId = stateParam;
    }

    // Initialize OAuth client
    const oauth2Client = new OAuth2Client(
      GOOGLE_CLIENT_ID,
      GOOGLE_CLIENT_SECRET,
      REDIRECT_URI
    );

    // Exchange code for tokens
    const { tokens } = await oauth2Client.getToken(code);
    
    if (!tokens.access_token) {
      throw new Error('No access token received');
    }

    // Set credentials to get user info
    oauth2Client.setCredentials(tokens);

    // Get user info
    const userInfoResponse = await fetch(
      'https://www.googleapis.com/oauth2/v2/userinfo',
      {
        headers: {
          Authorization: `Bearer ${tokens.access_token}`,
        },
      }
    );

    if (!userInfoResponse.ok) {
      throw new Error('Failed to fetch user info');
    }

    const userInfo = await userInfoResponse.json();

    // Calculate token expiration time
    const expiresAt = tokens.expiry_date 
      ? new Date(tokens.expiry_date).toISOString()
      : new Date(Date.now() + 3600 * 1000).toISOString(); // Default to 1 hour if no expiry provided

    // Store tokens in Supabase
    const { error: upsertError } = await supabaseAdmin
      .from('connected_accounts')
      .upsert({
        user_id: userId,
        provider: 'google',
        access_token: tokens.access_token,
        refresh_token: tokens.refresh_token || null,
        expires_at: expiresAt,
        provider_user_id: userInfo.id,
        provider_user_email: userInfo.email,
        scopes: tokens.scope?.split(' ') || [],
        provider_user_data: userInfo
      });

    if (upsertError) {
      console.error('Error storing tokens in Supabase:', upsertError);
      throw new Error('Failed to store authentication data');
    }

    // Redirect back to the frontend with success
    return NextResponse.redirect(
      `${FRONTEND_URL}${returnTo}?success=true&email=${encodeURIComponent(userInfo.email)}`
    );
  } catch (error) {
    console.error('Error in Google OAuth callback:', error);
    return NextResponse.redirect(
      `${FRONTEND_URL}?error=${encodeURIComponent('Authentication failed. Please try again.')}`
    );
  }
} 