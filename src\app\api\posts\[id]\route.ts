/**
 * API routes for individual post operations
 * Demonstrates comprehensive error handling patterns
 */

import { NextRequest } from 'next/server';
import { postService } from '@/lib/services';
import {
  withError<PERSON><PERSON><PERSON>,
  requireAuth,
  ErrorFactory
} from '@/lib/error-handler';
import {
  validateRequestBody,
  validateRouteParams,
  PostParamsSchema,
  UpdatePostSchema
} from '@/lib/validation';

/**
 * GET /api/posts/[id]
 * Get a specific post by ID
 */
async function getPostHandler(req: NextRequest, { params }: { params: { id: string } }) {
  // 1. Authenticate user
  const { user } = await requireAuth(req);

  // 2. Validate route parameters
  const { id: postId } = validateRouteParams(params, PostParamsSchema);

  // 3. Get the post
  const result = await postService.getPost(postId);

  if (!result.success) {
    throw result.error;
  }

  const post = result.data!;

  // 4. Check if user owns the post
  if (post.user_id !== user.id) {
    throw ErrorFactory.forbidden('Access denied to this post');
  }

  return { post };
}

/**
 * PUT /api/posts/[id]
 * Update a specific post
 */
async function updatePostHandler(req: NextRequest, { params }: { params: { id: string } }) {
  // 1. Authenticate user
  const { user } = await requireAuth(req);

  // 2. Validate route parameters
  const { id: postId } = validateRouteParams(params, PostParamsSchema);

  // 3. Validate request body
  const validatedData = await validateRequestBody(req, UpdatePostSchema);

  // 4. Check if post exists and user owns it
  const existingPostResult = await postService.getPost(postId);
  if (!existingPostResult.success) {
    throw existingPostResult.error;
  }

  const existingPost = existingPostResult.data!;
  if (existingPost.user_id !== user.id) {
    throw ErrorFactory.forbidden('Access denied to this post');
  }

  // 5. Check business rules
  if (existingPost.status === 'published' && validatedData.status === 'draft') {
    throw ErrorFactory.businessRuleViolation('Cannot change published post back to draft');
  }

  // 6. Update the post
  const updateResult = await postService.updatePost(postId, validatedData);
  
  if (!updateResult.success) {
    throw updateResult.error;
  }

  return { 
    post: updateResult.data,
    message: 'Post updated successfully'
  };
}

/**
 * DELETE /api/posts/[id]
 * Delete a specific post
 */
async function deletePostHandler(req: NextRequest, { params }: { params: { id: string } }) {
  // 1. Authenticate user
  const { user } = await requireAuth(req);

  // 2. Validate post ID
  const postId = params.id;
  if (!postId || !/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(postId)) {
    throw ErrorFactory.invalidFormat('postId', 'UUID');
  }

  // 3. Check if post exists and user owns it
  const existingPostResult = await postService.getPost(postId);
  if (!existingPostResult.success) {
    throw existingPostResult.error;
  }

  const existingPost = existingPostResult.data!;
  if (existingPost.user_id !== user.id) {
    throw ErrorFactory.forbidden('Access denied to this post');
  }

  // 4. Business rule: Cannot delete published posts
  if (existingPost.status === 'published') {
    throw ErrorFactory.businessRuleViolation(
      'Cannot delete published posts. Archive them instead.',
      'no_delete_published_posts'
    );
  }

  // 5. Cancel scheduled post if needed
  if (existingPost.status === 'scheduled') {
    const cancelResult = await postService.cancelScheduledPost(postId);
    if (!cancelResult.success) {
      // Log warning but continue with deletion
      console.warn(`Failed to cancel scheduled post ${postId}:`, cancelResult.error);
    }
  }

  // 6. Delete the post
  const deleteResult = await postService.deletePost(postId, user.id);
  
  if (!deleteResult.success) {
    throw deleteResult.error;
  }

  return { 
    message: 'Post deleted successfully',
    deletedPostId: postId
  };
}

/**
 * POST /api/posts/[id]/duplicate
 * Duplicate a post
 */
async function duplicatePostHandler(req: NextRequest, { params }: { params: { id: string } }) {
  // 1. Authenticate user
  const { user } = await requireAuth(req);

  // 2. Validate post ID
  const postId = params.id;
  if (!postId || !/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(postId)) {
    throw ErrorFactory.invalidFormat('postId', 'UUID');
  }

  // 3. Get the original post
  const originalPostResult = await postService.getPost(postId);
  if (!originalPostResult.success) {
    throw originalPostResult.error;
  }

  const originalPost = originalPostResult.data!;
  if (originalPost.user_id !== user.id) {
    throw ErrorFactory.forbidden('Access denied to this post');
  }

  // 4. Parse optional request body for modifications
  let modifications: any = {};
  try {
    const requestData = await req.json();
    modifications = requestData || {};
  } catch {
    // No body is fine for duplication
  }

  // 5. Create duplicate post data
  const duplicateData = {
    user_id: user.id,
    title: modifications.title || `Copy of ${originalPost.title}`,
    content: originalPost.content,
    status: 'draft' as const, // Always create duplicates as drafts
    youtube_settings: originalPost.youtube_settings,
    mediaIds: originalPost.media?.map(m => m.id) || [],
    channels: originalPost.channels?.map(c => c.id) || [],
    tags: originalPost.tags?.map(t => t.id) || [],
    ...modifications
  };

  // 6. Create the duplicate
  const duplicateResult = await postService.createPost(duplicateData);
  
  if (!duplicateResult.success) {
    throw duplicateResult.error;
  }

  return { 
    post: duplicateResult.data,
    originalPostId: postId,
    message: 'Post duplicated successfully'
  };
}

// Export the wrapped handlers
export const GET = withErrorHandler(getPostHandler);
export const PUT = withErrorHandler(updatePostHandler);
export const DELETE = withErrorHandler(deletePostHandler);

// For the duplicate endpoint, we'll need a separate route file
// This demonstrates how to handle custom endpoints with the error handler
