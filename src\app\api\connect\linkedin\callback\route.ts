/**
 * LinkedIn OAuth Callback Endpoint
 * 
 * Handles LinkedIn OAuth 2.0 callback and completes authentication
 * GET /api/connect/linkedin/callback
 */

import { NextRequest, NextResponse } from 'next/server';
import { createServerComponentClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { LinkedInAuthService } from '@/lib/services/linkedinAuthService';
import { 
  withError<PERSON><PERSON><PERSON>, 
  requireAuth,
  ErrorFactory
} from '@/lib/error-handler';

/**
 * Internal handler for LinkedIn OAuth callback
 */
async function linkedinOAuthCallbackHandler(req: NextRequest) {
  const { searchParams } = new URL(req.url);
  
  // Extract OAuth parameters from callback URL
  const authorizationCode = searchParams.get('code');
  const state = searchParams.get('state');
  const error = searchParams.get('error');
  const errorDescription = searchParams.get('error_description');

  try {
    // Handle OAuth error
    if (error) {
      const frontendUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3002';
      const errorUrl = `${frontendUrl}/integrations?error=${encodeURIComponent(error)}&description=${encodeURIComponent(errorDescription || 'LinkedIn authorization failed')}`;
      return NextResponse.redirect(errorUrl);
    }

    // Validate required parameters
    if (!authorizationCode || !state) {
      const frontendUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3002';
      const errorUrl = `${frontendUrl}/integrations?error=invalid_request&description=${encodeURIComponent('Missing required OAuth parameters')}`;
      return NextResponse.redirect(errorUrl);
    }

    // Authenticate user (this will validate the session)
    const { user } = await requireAuth(req);

    // Initialize LinkedIn authentication service
    const supabase = createServerComponentClient({ cookies });
    const linkedinAuthService = new LinkedInAuthService({
      supabase,
      supabaseAdmin: supabase
    });

    // Exchange authorization code for access tokens
    const authResult = await linkedinAuthService.handleCallback(authorizationCode, state);

    if (!authResult.success) {
      const frontendUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3002';
      const errorMessage = authResult.error?.message || 'Authentication failed';
      const errorUrl = `${frontendUrl}/integrations?error=auth_failed&description=${encodeURIComponent(errorMessage)}`;
      return NextResponse.redirect(errorUrl);
    }

    const { accessToken, refreshToken, expiresIn, userInfo, companyPages } = authResult.data!;

    // Store LinkedIn account integration in connected_accounts table
    const storeResult = await storeLinkedInIntegration(
      supabase, 
      user.id, 
      userInfo, 
      accessToken, 
      refreshToken, 
      expiresIn,
      companyPages
    );

    if (!storeResult.success) {
      const frontendUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3002';
      const errorUrl = `${frontendUrl}/integrations?error=store_failed&description=${encodeURIComponent('Failed to store LinkedIn account integration')}`;
      return NextResponse.redirect(errorUrl);
    }

    // Redirect to success page
    const frontendUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3002';
    const accountName = `${userInfo.firstName} ${userInfo.lastName}`.trim();
    const companyPagesCount = companyPages?.length || 0;
    const successUrl = `${frontendUrl}/integrations?success=true&provider=linkedin&account=${encodeURIComponent(accountName)}&companyPages=${companyPagesCount}`;
    return NextResponse.redirect(successUrl);

  } catch (error) {
    console.error('LinkedIn OAuth callback error:', error);
    
    const frontendUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3002';
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    const errorUrl = `${frontendUrl}/integrations?error=callback_failed&description=${encodeURIComponent(errorMessage)}`;
    return NextResponse.redirect(errorUrl);
  }
}

/**
 * Store LinkedIn account integration in connected_accounts table
 */
async function storeLinkedInIntegration(
  supabase: any,
  userId: string,
  userInfo: any,
  accessToken: string,
  refreshToken: string | undefined,
  expiresIn: number,
  companyPages?: any[]
) {
  try {
    // Calculate token expiry
    const expiresAt = new Date(Date.now() + expiresIn * 1000);

    // Check if personal connection already exists
    const { data: existingConnection } = await supabase
      .from('connected_accounts')
      .select('id')
      .eq('user_id', userId)
      .eq('provider', 'linkedin')
      .eq('service_type', 'personal')
      .eq('provider_account_id', userInfo.id)
      .single();

    const personalConnectionData = {
      user_id: userId,
      provider: 'linkedin',
      service_type: 'personal',
      provider_account_id: userInfo.id,
      account_username: userInfo.email,
      platform_account_name: `${userInfo.firstName} ${userInfo.lastName}`.trim(),
      profile_picture_url: userInfo.profilePicture,
      access_token: accessToken, // Will be encrypted by database trigger
      refresh_token: refreshToken,
      expires_at: expiresAt.toISOString(),
      scopes_granted: ['r_liteprofile', 'r_emailaddress', 'w_member_social'],
      status: 'active',
      last_sync_at: new Date().toISOString(),
      metadata: {
        firstName: userInfo.firstName,
        lastName: userInfo.lastName,
        email: userInfo.email,
        headline: userInfo.headline,
        industry: userInfo.industry,
        location: userInfo.location
      }
    };

    // Store or update personal connection
    let personalResult;
    if (existingConnection) {
      personalResult = await supabase
        .from('connected_accounts')
        .update(personalConnectionData)
        .eq('id', existingConnection.id);
    } else {
      personalResult = await supabase
        .from('connected_accounts')
        .insert(personalConnectionData);
    }

    if (personalResult.error) {
      throw new Error(`Failed to store personal connection: ${personalResult.error.message}`);
    }

    // Store company page connections if available
    if (companyPages && companyPages.length > 0) {
      for (const companyPage of companyPages) {
        // Check if company page connection already exists
        const { data: existingCompanyConnection } = await supabase
          .from('connected_accounts')
          .select('id')
          .eq('user_id', userId)
          .eq('provider', 'linkedin')
          .eq('service_type', 'company_page')
          .eq('provider_account_id', companyPage.id)
          .single();

        const companyConnectionData = {
          user_id: userId,
          provider: 'linkedin',
          service_type: 'company_page',
          provider_account_id: companyPage.id,
          account_username: companyPage.name,
          platform_account_name: companyPage.name,
          profile_picture_url: companyPage.logoUrl,
          access_token: accessToken, // Same token for company pages
          refresh_token: refreshToken,
          expires_at: expiresAt.toISOString(),
          scopes_granted: ['r_organization_social', 'w_organization_social', 'rw_organization_admin'],
          status: 'active',
          last_sync_at: new Date().toISOString(),
          metadata: {
            description: companyPage.description,
            industry: companyPage.industry,
            website: companyPage.website,
            followerCount: companyPage.followerCount,
            isAdmin: companyPage.isAdmin,
            permissions: companyPage.permissions
          }
        };

        let companyResult;
        if (existingCompanyConnection) {
          companyResult = await supabase
            .from('connected_accounts')
            .update(companyConnectionData)
            .eq('id', existingCompanyConnection.id);
        } else {
          companyResult = await supabase
            .from('connected_accounts')
            .insert(companyConnectionData);
        }

        if (companyResult.error) {
          console.error(`Failed to store company page connection for ${companyPage.name}:`, companyResult.error);
          // Don't fail the entire process for company page errors
        }
      }
    }

    return { success: true };
  } catch (error) {
    console.error('Error storing LinkedIn integration:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}

/**
 * GET handler for OAuth callback
 */
export const GET = withErrorHandler(linkedinOAuthCallbackHandler);

/**
 * Handle unsupported methods
 */
export async function POST() {
  return NextResponse.json({
    success: false,
    error: {
      message: 'Method not allowed. OAuth callback should use GET.',
      code: 'METHOD_NOT_ALLOWED'
    }
  }, { status: 405 });
}

export async function PUT() {
  return NextResponse.json({
    success: false,
    error: {
      message: 'Method not allowed. OAuth callback should use GET.',
      code: 'METHOD_NOT_ALLOWED'
    }
  }, { status: 405 });
}

export async function DELETE() {
  return NextResponse.json({
    success: false,
    error: {
      message: 'Method not allowed. OAuth callback should use GET.',
      code: 'METHOD_NOT_ALLOWED'
    }
  }, { status: 405 });
}
