const fs = require('fs');
const path = require('path');

// Read the .env file directly
console.log('Reading .env file directly:');
const envPath = path.resolve('.env');
console.log(`Env file path: ${envPath}`);
console.log(`Env file exists: ${fs.existsSync(envPath)}`);

if (fs.existsSync(envPath)) {
  const envContent = fs.readFileSync(envPath, 'utf8');
  console.log('\nEnv file content:');
  
  // Extract and show QStash related variables
  const qstashLines = envContent
    .split('\n')
    .filter(line => line.includes('QSTASH'))
    .map(line => line.trim());
  
  console.log(qstashLines);
  
  // Manually parse the token
  const tokenLine = qstashLines.find(line => line.startsWith('QSTASH_TOKEN'));
  if (tokenLine) {
    // Extract the token value
    const tokenMatch = tokenLine.match(/QSTASH_TOKEN="(.+)"/);
    if (tokenMatch && tokenMatch[1]) {
      console.log('\nExtracted QStash token:', tokenMatch[1]);
    } else {
      console.log('\nCould not extract token value.');
    }
  } else {
    console.log('\nQSTASH_TOKEN line not found.');
  }
}

// Try using dotenv
try {
  require('dotenv').config();
  console.log('\nLoaded with dotenv:');
  console.log('QSTASH_TOKEN:', process.env.QSTASH_TOKEN);
  console.log('NGROK_URL:', process.env.NGROK_URL);
} catch (error) {
  console.error('Error loading dotenv:', error);
} 