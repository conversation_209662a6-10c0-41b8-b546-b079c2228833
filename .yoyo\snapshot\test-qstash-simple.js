require('dotenv').config();
const https = require('https');

// Load token directly from file
const fs = require('fs');
const path = require('path');
const envContent = fs.readFileSync(path.resolve('.env'), 'utf8');

// Log .env content for debugging (obscuring sensitive data)
console.log("ENV content (sanitized):");
const sanitizedEnv = envContent.replace(/(TOKEN|KEY)="[^"]+"/g, '$1="[REDACTED]"');
console.log(sanitizedEnv);

// Get token
const tokenMatch = envContent.match(/QSTASH_TOKEN="(.+)"/);
if (!tokenMatch) {
  console.error("Failed to find QSTASH_TOKEN in .env file");
  process.exit(1);
}
const qstashToken = tokenMatch[1];
console.log(`Using token (first 10 chars): ${qstashToken.substring(0, 10)}...`);

// Manually set destination URL
const destinationUrl = "https://7861-2404-8ec0-4-2d98-a950-a216-4887-d919.ngrok-free.app/api/posts/publish-callback";
console.log(`Destination URL: ${destinationUrl}`);

// Create payload
const postData = JSON.stringify({
  postId: 'test-post-id',
  timestamp: new Date().toISOString()
});

// Create request options
const options = {
  hostname: 'qstash.upstash.io',
  port: 443,
  path: '/v2/publish',
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${qstashToken}`,
    'Upstash-Forward-Url': destinationUrl
  }
};

console.log("Request headers:");
Object.keys(options.headers).forEach(key => {
  const value = key === 'Authorization' 
    ? 'Bearer [REDACTED]' 
    : options.headers[key];
  console.log(`- ${key}: ${value}`);
});

// Make request
const req = https.request(options, (res) => {
  console.log(`Status Code: ${res.statusCode}`);
  
  let data = '';
  res.on('data', (chunk) => {
    data += chunk;
  });
  
  res.on('end', () => {
    console.log(`Response: ${data}`);
  });
});

req.on('error', (e) => {
  console.error(`Error: ${e.message}`);
});

// Send request
req.write(postData);
req.end(); 