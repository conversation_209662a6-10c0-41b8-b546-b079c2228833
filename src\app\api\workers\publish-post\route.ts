import { NextRequest, NextResponse } from 'next/server';
import { supabase, supabaseAdmin, checkSupabaseHealth } from '@/lib/supabase';
import { youtubeService } from '@/lib/services';
import { getTokenForService, TokenData } from '@/lib/authUtils';
import { Receiver } from '@upstash/qstash';
import { getPostFromFallback, savePostToFallback } from '@/lib/fallbackStorage';

// Add these interfaces at the top of the file, where other interfaces are defined
interface Post {
  id: string;
  title?: string;
  content?: string;
  media?: any[];
  user_id?: string;
  youtube_settings?: any;
}

interface Platform {
  id: string;
  name?: string;
}

interface PublishingResult {
  platformPostId: string | null;
  platformStatus: 'pending' | 'published' | 'failed' | 'skipped';
  platformPostUrl: string | null;
  platformError?: string | null;
}

/**
 * API route to publish a post that was scheduled
 * This is triggered by QStash when a scheduled post's time arrives
 */
export async function POST(req: NextRequest) {
  console.log('🚀 Publish post worker triggered');
  console.log('🔑 Worker version: 2.1.0 (with database sync support and improved error handling)');
  
  try {
    // Print out request headers for debugging
    console.log('📨 Request headers:', Object.fromEntries([...req.headers.entries()]));
    
    // Check Supabase connection health before proceeding, but continue with warning if it fails
    let isSupabaseHealthy = false;
    let useOfflineMode = false;
    try {
      isSupabaseHealthy = await checkSupabaseHealth();
      if (!isSupabaseHealthy) {
        console.warn('⚠️ Supabase connection health check failed, switching to offline mode');
        useOfflineMode = true;
      } else {
        console.log('✅ Supabase connection is healthy');
      }
    } catch (healthError) {
      console.error('❌ Error during health check:', healthError);
      console.warn('⚠️ Switching to offline mode due to health check error');
      useOfflineMode = true;
    }
    
    // Verify QStash signature if the signing keys are configured
    if (process.env.QSTASH_CURRENT_SIGNING_KEY && process.env.QSTASH_NEXT_SIGNING_KEY) {
      try {
        console.log('QStash keys found. Verifying QStash signature...');
        console.log(`Current key starts with: ${process.env.QSTASH_CURRENT_SIGNING_KEY.substring(0, 5)}...`);
        console.log(`Next key starts with: ${process.env.QSTASH_NEXT_SIGNING_KEY.substring(0, 5)}...`);
        
        const receiver = new Receiver({
          currentSigningKey: process.env.QSTASH_CURRENT_SIGNING_KEY,
          nextSigningKey: process.env.QSTASH_NEXT_SIGNING_KEY
        });
        
        // Get the request body as text to verify
        const body = await req.text();
        const signature = req.headers.get('upstash-signature');
        
        console.log(`Request body length: ${body.length} chars`);
        console.log(`Upstash signature: ${signature || 'MISSING'}`);
        
        // If no signature, proceed without verification for testing
        if (signature) {
          const isValid = await receiver.verify({
            signature,
            body
          });
          
          if (!isValid) {
            console.error('Invalid QStash signature');
            return NextResponse.json({ error: 'Invalid QStash signature' }, { status: 401 });
          }
          
          console.log('QStash signature verified successfully');
        } else {
          console.warn('No QStash signature found, proceeding without verification for testing');
        }
        
        // Parse the body as JSON for further processing
        const data = JSON.parse(body);
        console.log('Request data:', data);
        return await handlePost(data, req);
      } catch (error) {
        console.error('Error verifying QStash signature:', error);
        // For local testing, continue even if verification fails
        const bodyText = await req.text();
        console.log('Falling back to direct body processing');
        let data;
        try {
          data = JSON.parse(bodyText);
        } catch (err) {
          console.error('Failed to parse request body as JSON:', err);
          return NextResponse.json({ error: 'Invalid JSON in request body' }, { status: 400 });
        }
        return await handlePost(data, req);
      }
    } else {
      // No QStash keys configured, proceed without verification
      console.warn('QStash verification keys not configured, skipping signature verification');
      const data = await req.json();
      console.log('Request data (unverified):', data);
      return await handlePost(data, req);
    }
  } catch (error) {
    console.error('Error in publish-post worker:', error);
    return NextResponse.json({ 
      error: error instanceof Error ? error.message : String(error) 
    }, { status: 500 });
  }
}

/**
 * Handle the post publishing logic
 */
async function handlePost(data: any, req: NextRequest) {
  try {
    const { postId } = data;
    
    if (!postId) {
      console.error('Missing post ID in request body');
      return NextResponse.json({ error: 'Missing post ID' }, { status: 400 });
    }
    
    console.log(`Processing scheduled post: ${postId}`);
    
    // First try to get the post from fallback storage
    const fallbackPost = await getPostFromFallback(postId);
    if (fallbackPost) {
      console.log(`Found post ${postId} in fallback storage`);
      // Process the post from fallback storage
      return await processPostWithFallback(fallbackPost, postId);
    }
    
    // If not in fallback, try database with retry logic
    const maxRetries = 3;
    let post = null;
    let error = null;
    let attempt = 0;
    
    // Retry fetching the post with exponential backoff
    while (attempt < maxRetries) {
      try {
        attempt++;
        console.log(`Attempt ${attempt} to retrieve post ${postId}`);
        
        // Use supabaseAdmin instead of supabase to bypass RLS
        const result = await supabaseAdmin.from('posts')
      .select('*')
      .eq('id', postId)
      .single();
    
        if (result.error) {
          throw result.error;
        }
        
        post = result.data;
        if (post) {
          console.log(`Found post ${postId}`, { title: post.title, status: post.status });
          
          // Save to fallback storage for future reference
          await savePostToFallback(post);
          
          break; // Success, exit retry loop
        } else {
          throw new Error('Post not found');
        }
      } catch (err) {
        error = err;
        console.error(`Error on attempt ${attempt}/${maxRetries} retrieving post ${postId}:`, err);
        
        if (attempt < maxRetries) {
          // Exponential backoff with jitter
          const delay = Math.min(1000 * Math.pow(2, attempt) + Math.random() * 1000, 10000);
          console.log(`Retrying in ${Math.round(delay)}ms...`);
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }
    
    if (!post) {
      console.error(`Failed to retrieve post ${postId} after ${maxRetries} attempts:`, error);
      
      // Last resort - check if we can find post info in QStash message
      if (data.postData) {
        console.log(`Found post data in QStash message, using that for fallback`);
        
        // Construct a more complete post object from the available data
        const fallbackPostData = {
          id: postId,
          ...data.postData,
          status: 'processing',
          created_at: new Date().toISOString(),
          processed_at: new Date().toISOString(),
          user_id: data.postData.user_id || data.userId || 'unknown_user',
          title: data.postData.title || 'Untitled Post',
          content: data.postData.content || '',
          channels: data.channels || [],
          media: data.media || []
        };
        
        console.log('Constructed fallback post data:', fallbackPostData);
        
        // Try to save this post to the database first
        try {
          console.log(`Attempting to create missing post ${postId} in database`);
          
          // Strip non-column properties before insertion
          const { channels, media, ...dbPostData } = fallbackPostData;
          
          const { error: createError } = await supabaseAdmin
            .from('posts')
            .insert(dbPostData);
            
          if (createError) {
            console.error(`Error creating missing post ${postId} in database:`, createError);
          } else {
            console.log(`Successfully created missing post ${postId} in database`);
          }
        } catch (createError) {
          console.error(`Exception creating missing post ${postId} in database:`, createError);
        }
        
        // Save post data from message to fallback storage
        await savePostToFallback(fallbackPostData);
        
        // Try again from fallback
        const emergencyFallbackPost = await getPostFromFallback(postId);
        if (emergencyFallbackPost) {
          return await processPostWithFallback(emergencyFallbackPost, postId);
        }
      }
      
      return NextResponse.json({ 
        error: `Failed to retrieve post after multiple attempts: ${error instanceof Error ? error.message : String(error)}` 
      }, { status: 404 });
    }
    
    // Get media and channels separately to avoid RLS issues
    let postMedia = [];
    try {
    console.log(`Getting media for post ${postId}`);
      const { data: media, error: mediaError } = await supabaseAdmin
      .from('post_media')
      .select('*, media:media_id(*)')
      .eq('post_id', postId);
      
    if (mediaError) {
      console.error(`Error retrieving media for post ${postId}:`, mediaError);
    } else {
        postMedia = media || [];
      console.log(`Found ${postMedia?.length || 0} media items for post ${postId}`);
      }
    } catch (mediaFetchError) {
      console.error(`Exception retrieving media for post ${postId}:`, mediaFetchError);
      // Continue with empty media array
    }
    
    let postChannels = [];
    try {
    console.log(`Getting channels for post ${postId}`);
      const { data: channels, error: channelsError } = await supabaseAdmin
      .from('post_channels')
      .select('*, connected_accounts:account_id(*)')
      .eq('post_id', postId);
      
    if (channelsError) {
      console.error(`Error retrieving channels for post ${postId}:`, channelsError);
    } else {
        postChannels = channels || [];
      console.log(`Found ${postChannels?.length || 0} channels for post ${postId}`);
      }
    } catch (channelFetchError) {
      console.error(`Exception retrieving channels for post ${postId}:`, channelFetchError);
      // Continue with empty channels array
    }
    
    try {
    // Update post status to processing
    await supabaseAdmin
      .from('posts')
      .update({
        status: 'processing',
        processed_at: new Date().toISOString()
      })
      .eq('id', postId);
    
    console.log(`Updated post ${postId} status to 'processing'`);
    } catch (updateError) {
      console.error(`Error updating post status to processing:`, updateError);
      // Continue processing even if update fails
    }
    
    // Process each channel (social media platform)
    const results = [];
    
    console.log(`Post has ${postChannels?.length || 0} channels to process`);
    
    for (const channel of postChannels || []) {
      try {
        console.log(`Processing channel: ${channel.id}`, { accountId: channel.account_id });
        
        try {
        // Update channel status to processing
        await supabaseAdmin
          .from('post_channels')
          .update({
            status: 'processing'
          })
          .eq('id', channel.id);
        } catch (updateError) {
          console.error(`Error updating channel status to processing:`, updateError);
          // Continue processing even if update fails
        }
        
        const connectedAccount = channel.connected_accounts;
        if (!connectedAccount) {
          console.error(`No connected account found for channel ${channel.id}`);
          throw new Error('No connected account found for channel');
        }
        
        console.log(`Using connected account: ${connectedAccount.id}`, { provider: connectedAccount.provider });
        
        // Find the media to use for this post
        const mediaItem = postMedia && postMedia.length > 0 ? postMedia[0] : null;
        const mediaAsset = mediaItem ? mediaItem.media : null;
        
        console.log(`Media for this post:`, mediaAsset ? 
          { id: mediaAsset.id, file_name: mediaAsset.file_name } : 
          'No media found');
          
        const result = await processChannelPublishing(post, channel, connectedAccount, mediaAsset);
        results.push({ channelId: channel.id, ...result });
        
        try {
        // Update channel status to published
        await supabaseAdmin
          .from('post_channels')
          .update({
            status: 'published',
            published_at: new Date().toISOString(),
            platform_post_id: result.platformPostId || null,
            platform_post_url: result.platformPostUrl || null
          })
          .eq('id', channel.id);
        
        console.log(`Channel ${channel.id} published successfully`);
        } catch (updateError) {
          console.error(`Error updating channel status to published:`, updateError);
          // Continue processing even if update fails
        }
      } catch (error) {
        console.error(`Error publishing to channel ${channel.id}:`, error);
        
        try {
        // Update channel status to failed
        await supabaseAdmin
          .from('post_channels')
          .update({
            status: 'failed',
            error_message: error instanceof Error ? error.message : String(error)
          })
          .eq('id', channel.id);
        } catch (updateError) {
          console.error(`Error updating channel status to failed:`, updateError);
          // Continue processing even if update fails
        }
        
        results.push({ 
          channelId: channel.id, 
          success: false, 
          error: error instanceof Error ? error.message : String(error) 
        });
      }
    }
    
    // Update post status based on channel results
    const allSucceeded = results.every(r => r.success !== false);
    const newStatus = results.length === 0 ? 'failed' : 
      (allSucceeded ? 'published' : 'partially_published');
      
    try {
    await supabaseAdmin
      .from('posts')
      .update({
        status: newStatus,
        published_at: results.length > 0 ? new Date().toISOString() : null
      })
      .eq('id', postId);
    
    console.log(`Post ${postId} processing complete with status: ${newStatus}`);
    } catch (updateError) {
      console.error(`Error updating post final status:`, updateError);
      // Continue processing even if update fails
    }
    
    return NextResponse.json({ success: true, results });
  } catch (error) {
    console.error('Error in handlePost function:', error);
    return NextResponse.json({ 
      error: error instanceof Error ? error.message : String(error) 
    }, { status: 500 });
  }
}

/**
 * Process publishing to a specific channel based on platform type
 */
async function processChannelPublishing(post: any, channel: any, connectedAccount: any, mediaAsset: any) {
  const platform = connectedAccount.provider;
  
  console.log(`Publishing post ${post.id} to ${platform} platform`);
  
  // Get media asset if available
  console.log(`Post has media asset:`, mediaAsset ? { id: mediaAsset.id, file_id: mediaAsset.file_id } : 'None');
  
  switch (platform) {
    case 'youtube':
    case 'google_youtube':
      return await publishToYouTube(post, channel, connectedAccount, mediaAsset);
    
    // Add other platforms as needed
    default:
      console.error(`Unsupported platform: ${platform}, account:`, connectedAccount);
      throw new Error(`Unsupported platform: ${platform}`);
  }
}

/**
 * Upload video to YouTube using the YouTube API
 */
async function uploadToYouTube(
  accessToken: string,
  driveFileId: string,
  title: string,
  description: string,
  youtubeSettings: any = {},
  accountId?: string
) {
  console.log(`Using Drive file ID: ${driveFileId} for YouTube upload`);
  
  // YouTube-specific metadata
  const privacy = youtubeSettings?.privacy || 'private';
  const tags = (youtubeSettings?.tags || []).filter(Boolean);
  const categoryId = youtubeSettings?.categoryId || '22'; // 22 = People & Blogs
  
  // Upload from Drive to YouTube directly
  try {
    console.log('Starting YouTube upload from Drive...');
    
    // Build the YouTube API request
    const youtubeRequestUrl = 'https://www.googleapis.com/upload/youtube/v3/videos?part=snippet,status&uploadType=multipart';
    
    // Create the metadata for the video
    const videoMetadata = {
      snippet: {
        title,
        description,
        tags,
        categoryId
      },
      status: {
        privacyStatus: privacy,
        selfDeclaredMadeForKids: false
      }
    };
    
    // First, get the file metadata from Drive to confirm it exists
    let driveFileResponse;
    
    try {
      const driveFileUrl = `https://www.googleapis.com/drive/v3/files/${driveFileId}?fields=name,mimeType,size`;
      driveFileResponse = await fetch(driveFileUrl, {
        headers: {
          'Authorization': `Bearer ${accessToken}`
        }
      });
      
      if (!driveFileResponse.ok) {
        // Check if this is an authentication error (401)
        if (driveFileResponse.status === 401 && accountId) {
          console.log('Received 401 from Google Drive, attempting to refresh token...');
          
          // Try to refresh the token
          try {
            const refreshResponse = await fetch(`${process.env.NEXT_PUBLIC_APP_URL}/api/youtube/refresh-token-test?accountId=${accountId}`);
            
            if (refreshResponse.ok) {
              const refreshData = await refreshResponse.json();
              
              if (refreshData.success) {
                console.log('Token refreshed successfully, retrying with new token');
                
                // Get the refreshed token
                const newToken = await getTokenForService(accountId, 'youtube');
                
                if (newToken && newToken.accessToken) {
                  console.log('Got new token, retrying upload with refreshed token');
                  // Retry the upload with the new token
                  return await uploadToYouTube(
                    newToken.accessToken,
                    driveFileId,
                    title,
                    description,
                    youtubeSettings,
                    accountId
                  );
                }
              }
            }
          } catch (refreshError) {
            console.error('Error refreshing token:', refreshError);
          }
        }
        
        const driveError = await driveFileResponse.text();
        throw new Error(`Failed to retrieve Drive file metadata: ${driveError}`);
      }
    } catch (driveError) {
      console.error('Error accessing Drive file:', driveError);
      throw driveError;
    }
    
    const driveFile = await driveFileResponse.json();
    console.log(`Found Drive file: ${driveFile.name}, type: ${driveFile.mimeType}, size: ${driveFile.size}`);
    
    // Use the direct Drive to YouTube upload approach
    const exportUrl = `https://www.googleapis.com/drive/v3/files/${driveFileId}/export`;
    
    // Create a resumable upload session
    const sessionResponse = await fetch(
      'https://www.googleapis.com/upload/youtube/v3/videos?uploadType=resumable&part=snippet,status',
      {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json',
          'X-Upload-Content-Type': driveFile.mimeType
        },
        body: JSON.stringify(videoMetadata)
      }
    );
    
    if (!sessionResponse.ok) {
      const sessionError = await sessionResponse.text();
      throw new Error(`Failed to create YouTube upload session: ${sessionError}`);
    }
    
    // Get the upload URL from the location header
    const uploadUrl = sessionResponse.headers.get('location');
    if (!uploadUrl) {
      throw new Error('No upload URL received from YouTube API');
    }
    
    // Download the file from Drive and stream it to YouTube
    const driveDownloadUrl = `https://www.googleapis.com/drive/v3/files/${driveFileId}?alt=media`;
    const driveDownloadResponse = await fetch(driveDownloadUrl, {
      headers: {
        'Authorization': `Bearer ${accessToken}`
      }
    });
    
    if (!driveDownloadResponse.ok) {
      const downloadError = await driveDownloadResponse.text();
      throw new Error(`Failed to download file from Drive: ${downloadError}`);
    }
    
    // Upload the file to YouTube
    const buffer = await driveDownloadResponse.arrayBuffer();
    const uploadResponse = await fetch(uploadUrl, {
      method: 'PUT',
      headers: {
        'Content-Type': driveFile.mimeType,
        'Content-Length': buffer.byteLength.toString()
      },
      body: buffer
    });
    
    if (!uploadResponse.ok) {
      const uploadError = await uploadResponse.text();
      throw new Error(`Failed to upload to YouTube: ${uploadError}`);
    }
    
    // Get the video ID from the response
    const videoData = await uploadResponse.json();
    console.log(`Successfully uploaded to YouTube! Video ID: ${videoData.id}`);
    
    return {
      id: videoData.id,
      title: videoData.snippet?.title,
      description: videoData.snippet?.description,
      url: `https://www.youtube.com/watch?v=${videoData.id}`
    };
  } catch (error) {
    console.error('Error during YouTube upload:', error);
    throw new Error(`YouTube upload failed: ${error instanceof Error ? error.message : String(error)}`);
  }
}

/**
 * Publish a post to YouTube
 */
async function publishToYouTube(post: Post, channel: any, connectedAccount: any, mediaAsset: any, logPrefix: string = ''): Promise<PublishingResult> {
  // Add a prefix for logging to make it easier to trace through logs
  const prefix = logPrefix ? `${logPrefix} [YouTube]` : '[YouTube]';
  console.log(`${prefix} Starting YouTube publishing process for post ${post.id}`);
  
  // Check if the post has any media assets
  if (!mediaAsset) {
    console.log(`${prefix} No media found for this post, skipping YouTube`);
    return {
      platformPostId: null,
      platformStatus: 'skipped',
      platformPostUrl: null,
      platformError: 'No media found for YouTube upload'
    };
  }
  
  console.log(`${prefix} Using media item:`, JSON.stringify({
    id: mediaAsset.id,
    fileName: mediaAsset.file_name,
    fileType: mediaAsset.file_type,
    fileId: mediaAsset.file_id,
    hasMetadata: !!mediaAsset.metadata,
    hasDriveMetadata: !!mediaAsset.drive_metadata,
    externalId: mediaAsset.external_id,
    source: mediaAsset.source
  }));
  
  // Get the accountId from the connected account
  const accountId = connectedAccount?.id;
  if (!accountId) {
    console.log(`${prefix} No account ID found for YouTube, skipping`);
    return {
      platformPostId: null,
      platformStatus: 'failed',
      platformPostUrl: null,
      platformError: 'No account ID found for YouTube upload'
    };
  }
  
  console.log(`${prefix} Using account ID: ${accountId} for YouTube`);
  
  try {
    // Get a valid YouTube access token
    console.log(`${prefix} Retrieving YouTube access token for account ${accountId}`);
    const token = await getTokenForService(accountId, 'youtube');
    
    if (!token || !token.accessToken) {
      console.log(`${prefix} YouTube token retrieval failed for account ${accountId}`);
      return {
        platformPostId: null,
        platformStatus: 'failed',
        platformPostUrl: null,
        platformError: 'YouTube token retrieval failed'
      };
    }
    
    console.log(`${prefix} Got valid YouTube token`);
    
    // Get the file ID (we need this to access the file from Drive)
    let driveFileId = mediaAsset.file_id;
    
    // If there's no file_id directly on the media item, try to get it from metadata
    if (!driveFileId) {
      console.log('No direct file_id found, checking metadata sources...');
      
      // Check metadata
      if (mediaAsset.metadata) {
        const metadata = typeof mediaAsset.metadata === 'string' 
          ? JSON.parse(mediaAsset.metadata) 
          : mediaAsset.metadata;
          
        driveFileId = metadata.drive_id || metadata.id || metadata.fileId || metadata.file_id;
        
        if (driveFileId) {
          console.log(`Found file_id in metadata: ${driveFileId}`);
        }
      }
      
      // Check drive_metadata if still no fileId
      if (!driveFileId && mediaAsset.drive_metadata) {
        const driveMetadata = typeof mediaAsset.drive_metadata === 'string'
          ? JSON.parse(mediaAsset.drive_metadata)
          : mediaAsset.drive_metadata;
          
        driveFileId = driveMetadata.id || driveMetadata.fileId || driveMetadata.file_id;
        
        if (driveFileId) {
          console.log(`Found file_id in drive_metadata: ${driveFileId}`);
        }
      }
      
      // Use external_id as last resort for Google Drive files
      if (!driveFileId && mediaAsset.external_id && mediaAsset.source === 'google_drive') {
        driveFileId = mediaAsset.external_id;
        console.log(`Using external_id as file_id: ${driveFileId}`);
      }
      
      // Extract file ID from filename if it follows the drive-{fileId}.ext pattern
      if (!driveFileId && mediaAsset.file_name && mediaAsset.file_name.startsWith('drive-')) {
        const match = mediaAsset.file_name.match(/drive-([^.]+)\./);
        if (match && match[1]) {
          driveFileId = match[1];
          console.log(`Extracted file_id from filename: ${driveFileId}`);
        }
      }
    }
    
    // If we still don't have a file ID, check if this is a stored file with a URL
    if (!driveFileId && !mediaAsset.file_url) {
      throw new Error('Media asset missing both file_id and file_url');
    }
    
    if (!driveFileId) {
      console.log(`${prefix} No file ID found for media item ${mediaAsset.id}, cannot upload to YouTube`);
      return {
        platformPostId: null,
        platformStatus: 'failed',
        platformPostUrl: null,
        platformError: 'No file ID found for YouTube upload'
      };
    }
    
    console.log(`${prefix} Using file ID ${driveFileId} for YouTube upload`);
    
    // Prepare the video title and description
    const videoTitle = post.title || 'Untitled Video';
    const videoDescription = post.content || '';
    
    // Upload to YouTube
    console.log(`${prefix} Starting YouTube upload for file ${driveFileId}`);
    const uploadResult = await uploadToYouTube(
      token.accessToken,
    driveFileId,
      videoTitle,
      videoDescription,
      post.youtube_settings,
      accountId
    );
    
    console.log(`${prefix} YouTube upload successful: ${JSON.stringify(uploadResult)}`);
    
    return await completeYouTubeUpload(post, uploadResult, accountId, prefix);
  } catch (error) {
    console.error(`${prefix} Error publishing to YouTube:`, error);
    return {
      platformPostId: null,
      platformStatus: 'failed',
      platformPostUrl: null,
      platformError: `YouTube publishing failed: ${error instanceof Error ? error.message : String(error)}`
    };
  }
}

/**
 * Complete the YouTube upload process and update the post status
 */
async function completeYouTubeUpload(
  post: Post, 
  uploadResult: any, 
  accountId: string, 
  logPrefix: string = ''
): Promise<PublishingResult> {
  console.log('Retrieved YouTube token successfully, uploading video...');
  
  // Build YouTube upload request
  const videoTitle = post.title || 'Untitled Video';
  const videoDescription = post.content || '';
  
  // Use YouTube API to publish the video
  const youtubeResponse = await uploadToYouTube(
    uploadResult.accessToken,
    uploadResult.driveFileId,
    videoTitle,
    videoDescription,
    post.youtube_settings,
    accountId
  );
  
  return {
    platformPostId: youtubeResponse.id,
    platformStatus: 'published',
    platformPostUrl: `https://www.youtube.com/watch?v=${youtubeResponse.id}`
  };
}

/**
 * Process a post using fallback storage when database is unavailable
 */
async function processPostWithFallback(post: any, postId: string): Promise<NextResponse> {
  try {
    console.log(`Processing post ${postId} using fallback storage`);
    
    // Try to sync with database first if we can
    try {
      const isHealthy = await checkSupabaseHealth();
      if (isHealthy) {
        console.log(`Database connection is healthy, attempting to sync post ${postId} to database`);
        
        // Check if post exists in database
        const { data: existingPost, error: checkError } = await supabaseAdmin
          .from('posts')
          .select('id')
          .eq('id', postId)
          .maybeSingle();
          
        if (checkError) {
          console.error(`Error checking if post ${postId} exists in database:`, checkError);
        } else if (!existingPost) {
          console.log(`Post ${postId} not found in database, creating it`);
          
          // Create post in database
          const { error: createError } = await supabaseAdmin
            .from('posts')
            .insert({
              ...post,
              id: postId,
              status: 'processing',
              processed_at: new Date().toISOString()
            });
            
          if (createError) {
            console.error(`Error creating post ${postId} in database:`, createError);
          } else {
            console.log(`Successfully created post ${postId} in database`);
          }
        } else {
          console.log(`Post ${postId} already exists in database, proceeding with fallback processing`);
        }
      }
    } catch (syncError) {
      console.error(`Error syncing post ${postId} to database:`, syncError);
      // Continue with fallback processing
    }
    
    // Extract media and channel info from the post data
    const mediaItems = post.media || [];
    const channels = post.channels || [];
    
    console.log(`Post has ${mediaItems.length} media items and ${channels.length} channels`);
    
    if (channels.length === 0) {
      console.warn(`No channels found for post ${postId} in fallback mode`);
      return NextResponse.json({ 
        success: false, 
        error: 'No channels found for post in fallback mode' 
      });
    }
    
    // Process each channel individually
    const results = [];
    
    for (const channel of channels) {
      try {
        console.log(`Processing channel in fallback mode:`, channel);
        
        const connectedAccount = channel.connected_account || channel;
        const mediaAsset = mediaItems.length > 0 ? mediaItems[0] : null;
        
        if (!connectedAccount || !connectedAccount.provider) {
          throw new Error('Invalid connected account in fallback mode');
        }
        
        // Use the appropriate publishing method based on provider
        let result;
        if (connectedAccount.provider === 'youtube' || connectedAccount.provider === 'google_youtube') {
          result = await publishToYouTube(post, channel, connectedAccount, mediaAsset);
        } else {
          throw new Error(`Unsupported platform: ${connectedAccount.provider} in fallback mode`);
        }
        
        results.push({ 
          channelId: channel.id || 'fallback_channel', 
          ...result
        });
      } catch (error) {
        console.error(`Error publishing to channel in fallback mode:`, error);
        results.push({ 
          channelId: channel.id || 'fallback_channel', 
          platformStatus: 'failed',
          platformPostId: null,
          platformPostUrl: null,
          platformError: error instanceof Error ? error.message : String(error)
        });
      }
    }
    
    // Update the post status in fallback storage
    const allSucceeded = results.every(r => r.platformStatus !== 'failed');
    const newStatus = results.length === 0 ? 'failed' : 
      (allSucceeded ? 'published' : 'partially_published');
      
    // Update post with new status in fallback storage
    const updatedPost = {
      ...post,
      status: newStatus,
      published_at: results.length > 0 ? new Date().toISOString() : null,
      results
    };
    
    // Save updated post back to fallback storage
    await savePostToFallback(updatedPost);
    
    console.log(`Post ${postId} processed in fallback mode with status: ${newStatus}`);
    
    return NextResponse.json({ 
      success: true, 
      results,
      fallbackMode: true
    });
  } catch (error) {
    console.error(`Error in fallback processing for post ${postId}:`, error);
    return NextResponse.json({ 
      success: false, 
      error: error instanceof Error ? error.message : String(error),
      fallbackMode: true
    }, { status: 500 });
  }
} 