/**
 * QStash Worker: Publish LinkedIn Post
 * 
 * Handles scheduled LinkedIn post publishing with idempotency and error handling
 * POST /api/workers/publish-linkedin-post
 */

import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { LinkedInService } from '@/lib/services/linkedinService';
import { verifySignatureAppRouter } from '@upstash/qstash/nextjs';

// ============================================================================
// Types and Interfaces
// ============================================================================

interface LinkedInPostJobPayload {
  postId: string;
  userId: string;
  connectionId: string;
  content: {
    text: string;
    postType: 'text' | 'image' | 'video' | 'link';
    mediaUrls?: string[];
    linkUrl?: string;
    linkTitle?: string;
    linkDescription?: string;
    visibility?: 'PUBLIC' | 'CONNECTIONS' | 'LOGGED_IN_MEMBERS';
  };
  scheduledFor: string;
  jobId: string;
}

interface LinkedInPostJobResult {
  success: boolean;
  postId?: string;
  postUrn?: string;
  error?: string;
  publishedAt: string;
}

// ============================================================================
// Worker Handler
// ============================================================================

async function publishLinkedInPostWorker(req: NextRequest): Promise<NextResponse> {
  const startTime = Date.now();
  let jobPayload: LinkedInPostJobPayload | null = null;

  try {
    // Parse job payload
    jobPayload = await req.json() as LinkedInPostJobPayload;
    
    console.log(`[LinkedIn Worker] Starting job ${jobPayload.jobId} for post ${jobPayload.postId}`);

    // Validate required fields
    if (!jobPayload.postId || !jobPayload.userId || !jobPayload.connectionId || !jobPayload.content?.text) {
      throw new Error('Missing required fields in job payload');
    }

    // Initialize Supabase client
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );

    // Check if post has already been published (idempotency)
    const { data: existingPost, error: fetchError } = await supabase
      .from('posts')
      .select('id, status, platform_post_id, published_at')
      .eq('id', jobPayload.postId)
      .single();

    if (fetchError) {
      throw new Error(`Failed to fetch post: ${fetchError.message}`);
    }

    // If already published, return success
    if (existingPost.status === 'published' && existingPost.platform_post_id) {
      console.log(`[LinkedIn Worker] Post ${jobPayload.postId} already published: ${existingPost.platform_post_id}`);
      
      return NextResponse.json({
        success: true,
        postId: existingPost.platform_post_id,
        publishedAt: existingPost.published_at,
        message: 'Post already published (idempotent)',
        processingTimeMs: Date.now() - startTime
      });
    }

    // Update post status to publishing
    await supabase
      .from('posts')
      .update({ 
        status: 'publishing',
        updated_at: new Date().toISOString()
      })
      .eq('id', jobPayload.postId);

    // Initialize LinkedIn service
    const linkedinService = new LinkedInService({
      supabase,
      supabaseAdmin: supabase
    });

    // Validate connection
    const connectionResult = await linkedinService.validateConnection(jobPayload.userId, jobPayload.connectionId);
    if (!connectionResult.success) {
      throw new Error(`LinkedIn connection validation failed: ${connectionResult.error.message}`);
    }

    // Determine post type and publish accordingly
    let publishResult;
    const { text, postType, mediaUrls, linkUrl, linkTitle, linkDescription, visibility } = jobPayload.content;

    switch (postType) {
      case 'text':
        publishResult = await linkedinService.publishTextPost(
          jobPayload.userId,
          jobPayload.connectionId,
          text,
          visibility
        );
        break;

      case 'link':
        if (!linkUrl) {
          throw new Error('Link URL is required for link posts');
        }
        publishResult = await linkedinService.publishLinkSharePost(
          jobPayload.userId,
          jobPayload.connectionId,
          text,
          linkUrl,
          { linkTitle, linkDescription, visibility }
        );
        break;

      case 'image':
      case 'video':
        // For media posts, we need to handle media differently in the worker
        // This would require storing media files and retrieving them here
        console.warn(`[LinkedIn Worker] Media posts (${postType}) not fully implemented in worker. Publishing as text post.`);
        publishResult = await linkedinService.publishTextPost(
          jobPayload.userId,
          jobPayload.connectionId,
          text,
          visibility
        );
        break;

      default:
        throw new Error(`Unsupported post type: ${postType}`);
    }

    if (!publishResult.success) {
      throw new Error(`Failed to publish LinkedIn post: ${publishResult.error.message}`);
    }

    const postData = publishResult.data!;
    const publishedAt = new Date().toISOString();

    // Update post with success status
    const { error: updateError } = await supabase
      .from('posts')
      .update({
        status: 'published',
        platform_post_id: postData.postId,
        published_at: publishedAt,
        updated_at: publishedAt,
        publish_metadata: {
          postId: postData.postId,
          postUrn: postData.postUrn,
          authorUrn: postData.authorUrn,
          visibility: postData.visibility,
          publishedViaWorker: true,
          jobId: jobPayload.jobId,
          processingTimeMs: Date.now() - startTime
        }
      })
      .eq('id', jobPayload.postId);

    if (updateError) {
      console.error(`[LinkedIn Worker] Failed to update post status: ${updateError.message}`);
      // Don't throw here as the post was published successfully
    }

    console.log(`[LinkedIn Worker] Successfully published post ${jobPayload.postId} as LinkedIn post ${postData.postId}`);

    return NextResponse.json({
      success: true,
      postId: postData.postId,
      postUrn: postData.postUrn,
      publishedAt,
      processingTimeMs: Date.now() - startTime
    });

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    const processingTimeMs = Date.now() - startTime;

    console.error(`[LinkedIn Worker] Job failed:`, {
      jobId: jobPayload?.jobId,
      postId: jobPayload?.postId,
      error: errorMessage,
      processingTimeMs
    });

    // Update post with error status if we have the postId
    if (jobPayload?.postId) {
      try {
        const supabase = createClient(
          process.env.NEXT_PUBLIC_SUPABASE_URL!,
          process.env.SUPABASE_SERVICE_ROLE_KEY!
        );
        await supabase
          .from('posts')
          .update({
            status: 'failed',
            last_error: errorMessage,
            last_error_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          })
          .eq('id', jobPayload.postId);
      } catch (updateError) {
        console.error(`[LinkedIn Worker] Failed to update post error status:`, updateError);
      }
    }

    return NextResponse.json({
      success: false,
      error: errorMessage,
      processingTimeMs
    }, { status: 500 });
  }
}

// ============================================================================
// Route Handlers
// ============================================================================

/**
 * POST handler with QStash signature verification
 */
export const POST = verifySignatureAppRouter(publishLinkedInPostWorker);

/**
 * Handle unsupported methods
 */
export async function GET() {
  return NextResponse.json({
    success: false,
    error: {
      message: 'Method not allowed. This endpoint only accepts POST requests from QStash.',
      code: 'METHOD_NOT_ALLOWED'
    }
  }, { status: 405 });
}

export async function PUT() {
  return NextResponse.json({
    success: false,
    error: {
      message: 'Method not allowed. This endpoint only accepts POST requests from QStash.',
      code: 'METHOD_NOT_ALLOWED'
    }
  }, { status: 405 });
}

export async function DELETE() {
  return NextResponse.json({
    success: false,
    error: {
      message: 'Method not allowed. This endpoint only accepts POST requests from QStash.',
      code: 'METHOD_NOT_ALLOWED'
    }
  }, { status: 405 });
}
