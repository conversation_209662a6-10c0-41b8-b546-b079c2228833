import { NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';

// Import google APIs with error handling for token refresh
let google: any;
try {
  google = require('googleapis').google;
  console.log('Successfully imported googleapis library for token refresh');
} catch (importError) {
  console.error('Error importing googleapis library for token refresh:', importError);
}

async function refreshAccessToken(refreshToken: string, userId: string, supabase: any) {
  try {
    console.log('🔄 Attempting to refresh YouTube access token');
    
    // Get env vars with error handling
    const clientId = process.env.GOOGLE_CLIENT_ID;
    const clientSecret = process.env.GOOGLE_CLIENT_SECRET;
    
    if (!clientId || !clientSecret) {
      console.error('❌ Missing environment variables for token refresh:', { 
        hasClientId: !!clientId, 
        hasClientSecret: !!clientSecret
      });
      throw new Error('Missing environment variables for token refresh');
    }
    
    if (!google) {
      console.error('❌ googleapis library not available for token refresh');
      throw new Error('Token refresh not available (googleapis library missing)');
    }
    
    // Create OAuth client
    const oauth2Client = new google.auth.OAuth2(
      clientId,
      clientSecret,
      `${process.env.NEXT_PUBLIC_APP_URL}/auth/callback?provider=youtube`
    );
    
    // Set the refresh token
    oauth2Client.setCredentials({
      refresh_token: refreshToken
    });
    
    // Refresh the access token
    const response = await oauth2Client.getAccessToken();
    console.log('✅ Access token refreshed successfully');
    
    const newAccessToken = response.token || response.res.data.access_token;
    const newRefreshToken = response.res?.data?.refresh_token || refreshToken;
    const expiryDate = response.res?.data?.expiry_date || Date.now() + 3600 * 1000;
    
    // Update the token in the database
    const { error: updateError } = await supabase
      .from('google_oauth_tokens')
      .update({
        access_token: newAccessToken,
        refresh_token: newRefreshToken,
        expires_at: new Date(expiryDate).toISOString(),
        updated_at: new Date().toISOString()
      })
      .eq('user_id', userId)
      .eq('service_type', 'youtube');
    
    if (updateError) {
      console.error('❌ Error updating refreshed token:', updateError);
      throw new Error('Failed to update refreshed token in database');
    }
    
    console.log('✅ Token updated in database');
    return newAccessToken;
  } catch (error: any) {
    console.error('❌ Error in refreshAccessToken:', error);
    throw error;
  }
}

export async function GET() {
  try {
    console.log('📺 YouTube get-token APP route called');
    
    // Log available cookies for debugging
    const cookieStore = cookies();
    const availableCookies = cookieStore.getAll().map(c => c.name);
    console.log('🍪 Available cookies:', availableCookies);
    
    // Use cookies to create a route handler client
    const supabase = createClient();
    
    // Get session using cookies-based auth
    const { data, error: sessionError } = await supabase.auth.getSession();
    
    if (sessionError) {
      console.error('❌ Session error details:', sessionError.message);
      return NextResponse.json({ 
        error: 'Authentication error', 
        details: sessionError.message 
      }, { status: 401 });
    }
    
    if (!data.session?.user) {
      console.log('❌ No authenticated user found in session');
      return NextResponse.json({ 
        error: 'Authentication required', 
        missingAccount: true 
      }, { status: 401 });
    }
    
    const userId = data.session.user.id;
    console.log('✅ User authenticated, ID:', userId);
    
    // Get YouTube tokens from google_oauth_tokens table
    const { data: tokens, error: tokensError } = await supabase
      .from('google_oauth_tokens')
      .select('access_token, refresh_token, scope, expires_at, metadata')
      .eq('user_id', userId)
      .eq('service_type', 'youtube')
      .order('updated_at', { ascending: false })
      .limit(1);

    // If no tokens found or scope is missing, try to get from connected_accounts as fallback
    let fallbackScope = null;
    if (!tokens || tokens.length === 0 || !tokens[0]?.scope) {
      console.log('🔄 No tokens found in google_oauth_tokens or scope missing, checking connected_accounts...');
      const { data: connectedAccount, error: connectedError } = await supabase
        .from('connected_accounts')
        .select('scope, access_token, refresh_token, expires_at')
        .eq('user_id', userId)
        .eq('provider', 'google')
        .eq('service_type', 'youtube')
        .not('scope', 'is', null)
        .order('updated_at', { ascending: false })
        .limit(1);

      if (connectedAccount && connectedAccount.length > 0) {
        fallbackScope = connectedAccount[0].scope;
        console.log('✅ Found scope in connected_accounts:', fallbackScope ? 'PRESENT' : 'MISSING');

        // If we have tokens but missing scope, update the google_oauth_tokens with the scope
        if (tokens && tokens.length > 0 && !tokens[0]?.scope && fallbackScope) {
          console.log('🔄 Updating google_oauth_tokens with scope from connected_accounts...');
          await supabase
            .from('google_oauth_tokens')
            .update({ scope: fallbackScope, updated_at: new Date().toISOString() })
            .eq('user_id', userId)
            .eq('service_type', 'youtube');

          // Update the local tokens object
          tokens[0].scope = fallbackScope;
        }
      }
    }
      
    if (tokensError) {
      console.error('❌ Error fetching YouTube tokens:', tokensError.message);
      return NextResponse.json({ 
        error: 'YouTube authentication required', 
        details: tokensError.message,
        needsReauth: true 
      }, { status: 401 });
    }
    
    if (!tokens || tokens.length === 0) {
      console.warn('❌ No YouTube tokens found for user:', userId);
      return NextResponse.json({ 
        error: 'YouTube authentication required', 
        needsReauth: true 
      }, { status: 401 });
    }
    
    const token = tokens[0];
    const accessToken = token.access_token;
    const refreshToken = token.refresh_token;
    const expiresAt = token.expires_at;
    
    // Check if token is expired or will expire soon (within 5 minutes)
    const now = Date.now();
    let expiryTime = 0;
    
    // Handle expires_at which could be a timestamp string or a bigint
    if (expiresAt) {
      if (typeof expiresAt === 'string') {
        // If it's stored as ISO timestamp string
        expiryTime = new Date(expiresAt).getTime();
      } else {
        // If it's stored as a bigint/number
        expiryTime = Number(expiresAt);
      }
    }
    
    const isExpired = expiryTime ? now >= expiryTime - 5 * 60 * 1000 : false;
    
    // If token is expired and we have a refresh token, try to refresh it
    if (isExpired && refreshToken) {
      try {
        console.log('🔄 Token expired or expiring soon, attempting refresh');
        const newAccessToken = await refreshAccessToken(refreshToken, userId, supabase);
        console.log('✅ Successfully refreshed access token');
        return NextResponse.json({ 
          message: 'YouTube credentials refreshed',
          hasToken: true,
          scope: token.scope,
          expiresAt: new Date(Date.now() + 3600 * 1000).toISOString()
        });
      } catch (refreshError: any) {
        console.error('❌ Error refreshing token:', refreshError);
        return NextResponse.json({ 
          error: 'Token refresh failed', 
          details: refreshError.message,
          needsReauth: true 
        }, { status: 401 });
      }
    }
    
    if (!accessToken) {
      console.warn('❌ Access token is missing for YouTube connection');
      return NextResponse.json({ 
        error: 'YouTube token is missing', 
        needsReauth: true 
      }, { status: 401 });
    }
    
    // Log token details for debugging
    const scope = token.scope;
    console.log('🔑 YouTube token details:', {
      hasAccessToken: !!accessToken,
      hasRefreshToken: !!refreshToken,
      accessTokenLength: accessToken ? accessToken.length : 0,
      refreshTokenLength: refreshToken ? refreshToken.length : 0,
      expiresAt: expiresAt,
      scope: scope ? (scope.length > 50 ? scope.substring(0, 50) + '...' : scope) : 'none',
      channelId: token.metadata?.channel_id
    });
    
    console.log('✅ YouTube token found for user:', userId);
    
    // Return success response with token status
    return NextResponse.json({ 
      message: 'YouTube credentials are valid',
      hasToken: true,
      scope,
      expiresAt,
      channelId: token.metadata?.channel_id
    });
  } catch (error: any) {
    console.error('❌ Unhandled error in YouTube get-token API:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error', 
        details: error.message || 'Unknown error' 
      },
      { status: 500 }
    );
  }
} 