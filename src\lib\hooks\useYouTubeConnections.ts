'use client';

import { useState, useEffect } from 'react';
import { createClient } from '@/lib/supabase/client';

interface YouTubeConnection {
  id: string;
  provider: string;
  service_type: string;
  provider_account_email: string;
  platform_account_name?: string;
  platform_account_id?: string;
  metadata?: {
    channel_id?: string;
    channel_title?: string;
    channel_thumbnail?: string;
  };
  auto_reply_enabled?: boolean;
  created_at: string;
  updated_at: string;
}

interface UseYouTubeConnectionsReturn {
  connections: YouTubeConnection[];
  isLoading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
  getConnectionByChannelId: (channelId: string) => YouTubeConnection | null;
}

/**
 * Hook to fetch ALL YouTube connections for the user
 * This allows proper per-channel auto-comment management
 */
export function useYouTubeConnections(): UseYouTubeConnectionsReturn {
  const [connections, setConnections] = useState<YouTubeConnection[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const supabase = createClient();

  const fetchConnections = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Get the current user
      const { data: { user }, error: userError } = await supabase.auth.getUser();

      if (userError || !user) {
        console.log('User not authenticated, skipping YouTube connections fetch');
        setConnections([]);
        setError(null);
        return;
      }

      // Fetch ALL YouTube connections from the new API endpoint
      const response = await fetch('/api/youtube/connections', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error('Error fetching connections:', errorData);
        setError(`Failed to fetch YouTube connections: ${errorData.error || 'Unknown error'}`);
        setConnections([]);
        return;
      }

      const { connections: connectionsData } = await response.json();

      if (connectionsData && connectionsData.length > 0) {
        console.log(`Found ${connectionsData.length} YouTube connections`);
        setConnections(connectionsData as YouTubeConnection[]);
      } else {
        console.log('No YouTube connections found');
        setConnections([]);
      }

    } catch (err) {
      console.error('Error fetching YouTube connections:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch YouTube connections');
      setConnections([]);
    } finally {
      setIsLoading(false);
    }
  };

  // Helper function to get connection by channel ID
  const getConnectionByChannelId = (channelId: string): YouTubeConnection | null => {
    if (!channelId || connections.length === 0) {
      return null;
    }

    // Find connection that matches the channel ID
    const connection = connections.find(conn => {
      // Check metadata for channel_id
      if (conn.metadata?.channel_id === channelId) {
        return true;
      }

      // Also check platform_account_id
      if (conn.platform_account_id === channelId) {
        return true;
      }

      return false;
    });

    if (connection) {
      console.log(`Found connection ${connection.id} for channel ${channelId}`);
      return connection;
    }

    console.log(`No connection found for channel ${channelId}`);
    console.log('Available connections:', connections.map(c => ({
      id: c.id,
      channelId: c.metadata?.channel_id || c.platform_account_id,
      title: c.metadata?.channel_title || c.platform_account_name
    })));

    return null;
  };

  useEffect(() => {
    fetchConnections();
  }, []);

  return {
    connections,
    isLoading,
    error,
    refetch: fetchConnections,
    getConnectionByChannelId
  };
}
