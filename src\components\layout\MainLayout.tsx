'use client'

import { Bell, Menu, Search, User, Settings, ChevronDown, Grid, Facebook, Instagram, Twitter, Linkedin, MessageCircle, Youtube, FileText, Building2, AtSign, Tag, Grid2X2, HelpCircle, ChevronUp, Home, Globe, Edit3, Calendar, BarChart2, Plus, RefreshCw, LayoutGrid } from 'lucide-react'
import Link from 'next/link'
import { usePathname, useRouter } from 'next/navigation'
import { memo, useCallback, useState } from 'react'
import { usePlatform } from '@/contexts/PlatformContext'
import { useDemoState } from '@/contexts/DemoStateContext'
import { PlatformIcon } from '@/components/shared/PlatformIcon'
import ConnectedYouTubeChannels from '@/components/ConnectedYouTubeChannels'

// Custom icon components for missing Lucide icons
const TikTokIcon = () => (
  <svg
    width="14"
    height="14"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
  >
    <path d="M9 12a4 4 0 1 0 0 8 4 4 0 0 0 0-8z" />
    <path d="M15 8a4 4 0 0 0 0 8" />
    <path d="M15 2v14" />
    <path d="M9 16v6" />
  </svg>
);

const PinterestIcon = () => (
  <svg
    width="14"
    height="14"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
  >
    <path d="M9 19c-5 1.5-5-2.5-7-3m14 6v-3.87a3.37 3.37 0 0 0-.94-2.61c3.14-.35 6.44-1.54 6.44-7A5.44 5.44 0 0 0 20 4.77 5.07 5.07 0 0 0 19.91 1S18.73.65 16 2.48a13.38 13.38 0 0 0-7 0C6.27.65 5.09 1 5.09 1A5.07 5.07 0 0 0 5 4.77a5.44 5.44 0 0 0-1.5 3.78c0 5.42 3.3 6.61 6.44 7A3.37 3.37 0 0 0 9 18.13V22" />
  </svg>
);

const BlueSkyIcon = () => (
  <svg
    width="14"
    height="14"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
  >
    <path d="M12 4a1 1 0 0 1 1 1v4a1 1 0 0 1-2 0V5a1 1 0 0 1 1-1z" />
    <path d="M5 10a7 7 0 0 0 14 0" />
    <path d="M5 18a9 9 0 0 0 14 0" />
  </svg>
);

const BufferLogo = () => (
  <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M16 6L4 12L16 18L28 12L16 6Z" fill="#4461F2" />
    <path d="M4 20L16 26L28 20" fill="#4461F2" />
  </svg>
);

type MainLayoutProps = {
  children: React.ReactNode
}

function MainLayoutComponent({ children }: MainLayoutProps) {
  const [isSidebarOpen, setIsSidebarOpen] = useState(true)
  const [isChannelsExpanded, setIsChannelsExpanded] = useState(false)
  const { platforms } = usePlatform()
  const { cycleUserState, userState } = useDemoState()
  const currentPath = usePathname()
  const router = useRouter()

  const toggleSidebar = useCallback(() => {
    setIsSidebarOpen(prev => !prev)
  }, [])

  const toggleChannels = useCallback(() => {
    setIsChannelsExpanded(prev => !prev)
  }, [])

  const connectedPlatforms = platforms.filter(p => p.status === 'connected')

  const isActive = (path: string) => {
    return currentPath === path || currentPath?.startsWith(path + '/')
  }

  // Check if we're on an analyze page to hide the channels sidebar
  const isAnalyzePage = currentPath === '/analyze' || currentPath?.startsWith('/analyze/')

  return (
    <div className="min-h-screen flex flex-col">
      {/* Header */}
      <header className="h-16 bg-white border-b border-[#E5E5E5] flex items-center px-4">
        <div className="flex items-center justify-between w-full">
          <div className="flex items-center space-x-8">
            {/* Logo */}
            <div className="flex items-center">
              <BufferLogo />
              <span className="ml-2 font-bold text-gray-800">Buffer</span>
            </div>
            
            {/* Main Navigation */}
            <nav className="flex items-center space-x-6">
              <Link 
                href="/create" 
                className={`py-4 px-1 font-medium ${isActive('/create') ? 'text-[#4461F2] border-b-2 border-[#4461F2]' : 'text-gray-600 hover:text-gray-900'}`}
              >
                Create
              </Link>
              <Link 
                href="/publish" 
                className={`py-4 px-1 font-medium ${isActive('/publish') || isActive('/queue') || isActive('/drafts') || isActive('/approvals') || isActive('/sent') ? 'text-[#4461F2] border-b-2 border-[#4461F2]' : 'text-gray-600 hover:text-gray-900'}`}
              >
                Publish
              </Link>
              <Link 
                href="/analyze" 
                className={`py-4 px-1 font-medium ${isActive('/analyze') ? 'text-[#4461F2] border-b-2 border-[#4461F2]' : 'text-gray-600 hover:text-gray-900'}`}
              >
                Analyze
              </Link>
              <Link 
                href="/engage" 
                className={`py-4 px-1 font-medium ${isActive('/engage') ? 'text-[#4461F2] border-b-2 border-[#4461F2]' : 'text-gray-600 hover:text-gray-900'}`}
              >
                Engage
              </Link>
              <Link 
                href="/start-page" 
                className={`py-4 px-1 font-medium ${isActive('/start-page') ? 'text-[#4461F2] border-b-2 border-[#4461F2]' : 'text-gray-600 hover:text-gray-900'}`}
              >
                Start Page
              </Link>
            </nav>
          </div>
          
          <div className="flex items-center space-x-3">
            {/* Demo State Cycle Button */}
            <button 
              onClick={cycleUserState}
              className="px-3 py-1 text-sm border border-gray-300 rounded flex items-center bg-gray-50 hover:bg-gray-100"
              title="Cycle through different user states (New, No Accounts, Connected)"
            >
              <RefreshCw size={14} className="mr-1" />
              <span>
                {userState === 'new' ? 'Demo: New User' : 
                 userState === 'no-accounts' ? 'Demo: No Accounts' : 
                 'Demo: Connected'}
              </span>
            </button>
            
            <button className="px-3 py-1 text-sm text-[#4461F2] border border-[#4461F2] rounded hover:bg-blue-50">
              Upgrade
            </button>
            <button className="px-3 py-1 bg-[#4461F2] text-white rounded flex items-center">
              <Plus size={16} className="mr-1" />
              New
            </button>
            <button className="p-2 text-gray-600 hover:bg-gray-100 rounded-full">
              <Settings size={18} />
            </button>
            <button className="p-2 text-gray-600 hover:bg-gray-100 rounded-full">
              <Bell size={18} />
            </button>
            <button className="p-2 text-gray-600 hover:bg-gray-100 rounded-full">
              <HelpCircle size={18} />
            </button>
            <div className="h-8 w-8 bg-[#4461F2] rounded-full flex items-center justify-center text-white font-medium">
              U
            </div>
          </div>
        </div>
      </header>

      <div className="flex flex-1">
        {/* Sidebar - Only show if not on analyze pages */}
        {!isAnalyzePage && (
          <aside className="w-64 bg-white border-r border-[#E5E5E5] shadow-sm flex flex-col">
            <div className="p-5 border-b border-[#E5E5E5] bg-gradient-to-r from-blue-50 to-indigo-50">
              <h2 className="text-base font-semibold text-gray-800 flex items-center">
                <Grid size={18} className="mr-2 text-blue-600" />
                Channels
              </h2>
            </div>
            
            <nav className="py-3 flex-1 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-200 scrollbar-track-transparent">
              <ul className="space-y-1 px-2">
                <li>
                  <Link
                    href="/all-channels"
                    className={`flex items-center px-3 py-2.5 rounded-lg hover:bg-gray-50 transition-colors duration-200 ${
                      isActive('/all-channels') ? 'bg-indigo-50 text-indigo-600' : 'text-gray-700 hover:text-gray-900'
                    }`}
                  >
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center mr-3 shadow-sm ${
                      isActive('/all-channels') ? 'bg-indigo-100' : 'bg-gray-100'
                    }`}>
                      <LayoutGrid size={16} className={isActive('/all-channels') ? 'text-indigo-600' : 'text-gray-600'} />
                    </div>
                    <span>All Channels</span>
                  </Link>
                </li>
                <li>
                  <Link
                    href="/connect-facebook"
                    className={`flex items-center px-3 py-2.5 rounded-lg hover:bg-gray-50 transition-colors duration-200 ${
                      isActive('/connect-facebook') ? 'bg-blue-50 text-blue-600' : 'text-gray-700 hover:text-gray-900'
                    }`}
                  >
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center mr-3 shadow-sm ${
                      isActive('/connect-facebook') ? 'bg-blue-100' : 'bg-gray-100'
                    }`}>
                      <Facebook size={16} className={isActive('/connect-facebook') ? 'text-blue-600' : 'text-gray-600'} />
                    </div>
                    <span>Connect Facebook</span>
                  </Link>
                </li>
                <li>
                  <Link
                    href="/connect-instagram"
                    className={`flex items-center px-3 py-2.5 rounded-lg hover:bg-gray-50 transition-colors duration-200 ${
                      isActive('/connect-instagram') ? 'bg-pink-50 text-pink-600' : 'text-gray-700 hover:text-gray-900'
                    }`}
                  >
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center mr-3 shadow-sm ${
                      isActive('/connect-instagram') ? 'bg-pink-100' : 'bg-gray-100'
                    }`}>
                      <Instagram size={16} className={isActive('/connect-instagram') ? 'text-pink-600' : 'text-gray-600'} />
                    </div>
                    <span>Connect Instagram</span>
                  </Link>
                </li>
                <li>
                  <Link
                    href="/connect-youtube"
                    className={`flex items-center px-3 py-2.5 rounded-lg hover:bg-gray-50 transition-colors duration-200 ${
                      isActive('/connect-youtube') ? 'bg-red-50 text-red-600' : 'text-gray-700 hover:text-gray-900'
                    }`}
                  >
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center mr-3 shadow-sm ${
                      isActive('/connect-youtube') ? 'bg-red-100' : 'bg-gray-100'
                    }`}>
                      <Youtube size={16} className={isActive('/connect-youtube') ? 'text-red-600' : 'text-gray-600'} />
                    </div>
                    <span>Connect YouTube</span>
                  </Link>
                </li>

                {!isChannelsExpanded ? (
                  <li>
                    <button 
                      onClick={toggleChannels} 
                      className="flex items-center text-gray-700 w-full text-left px-3 py-2.5 rounded-lg hover:bg-gray-50 transition-colors duration-200"
                    >
                      <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center mr-3 shadow-sm">
                        <ChevronDown size={16} className="text-gray-500 transition-transform duration-200" />
                      </div>
                      <span>Show more channels</span>
                    </button>
                  </li>
                ) : (
                  <>
                    <li>
                      <Link 
                        href="/connect-linkedin" 
                        className="flex items-center text-gray-700 px-3 py-2.5 rounded-lg hover:bg-gray-50 transition-colors duration-200"
                      >
                        <div className="w-8 h-8 bg-blue-700 rounded-full flex items-center justify-center mr-3 shadow-sm">
                          <Linkedin size={16} className="text-white" />
                        </div>
                        <span>Connect LinkedIn</span>
                      </Link>
                    </li>
                    
                    <li>
                      <Link 
                        href="/connect-pinterest" 
                        className="flex items-center text-gray-700 px-3 py-2.5 rounded-lg hover:bg-gray-50 transition-colors duration-200"
                      >
                        <div className="w-8 h-8 bg-red-600 rounded-full flex items-center justify-center mr-3 shadow-sm">
                          <div className="text-white">
                            <PinterestIcon />
                          </div>
                        </div>
                        <span>Connect Pinterest</span>
                      </Link>
                    </li>
                    
                    <li>
                      <Link 
                        href="/connect-tiktok" 
                        className="flex items-center text-gray-700 px-3 py-2.5 rounded-lg hover:bg-gray-50 transition-colors duration-200"
                      >
                        <div className="w-8 h-8 bg-black rounded-full flex items-center justify-center mr-3 shadow-sm">
                          <div className="text-white">
                            <TikTokIcon />
                          </div>
                        </div>
                        <span>Connect TikTok</span>
                      </Link>
                    </li>
                    
                    <li>
                      <button 
                        onClick={toggleChannels} 
                        className="flex items-center text-gray-700 w-full text-left px-3 py-2.5 rounded-lg hover:bg-gray-50 transition-colors duration-200"
                      >
                        <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center mr-3 shadow-sm">
                          <ChevronUp size={16} className="text-gray-500 transition-transform duration-200" />
                        </div>
                        <span>Show fewer channels</span>
                      </button>
                    </li>
                  </>
                )}
              </ul>
              
              <div className="mt-6 pt-4 border-t border-gray-200 mx-4">
                <h3 className="text-sm font-medium text-gray-700 px-3 mb-2">Connected Channels</h3>
                
                {/* YouTube Connected Channels Display */}
                <div className="px-3 mb-2">
                  <ConnectedYouTubeChannels className="w-full" />
                </div>
                
                <ul className="space-y-1">
                  <li>
                    <Link 
                      href="/manage-tags" 
                      className="flex items-center text-gray-700 hover:text-gray-900 px-3 py-2.5 rounded-lg hover:bg-gray-50 transition-colors duration-200"
                    >
                      <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center mr-3 shadow-sm">
                        <Tag size={16} className="text-gray-600" />
                      </div>
                      <span>Manage Tags</span>
                    </Link>
                  </li>
                  <li>
                    <Link 
                      href="/manage-channels" 
                      className="flex items-center text-gray-700 hover:text-gray-900 px-3 py-2.5 rounded-lg hover:bg-gray-50 transition-colors duration-200"
                    >
                      <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center mr-3 shadow-sm">
                        <Grid2X2 size={16} className="text-gray-600" />
                      </div>
                      <span>Manage Channels</span>
                    </Link>
                  </li>
                </ul>
              </div>
            </nav>
            
            <div className="p-4 mt-auto border-t border-gray-200 bg-gradient-to-r from-gray-50 to-blue-50">
              <button className="w-full flex items-center justify-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors duration-200 shadow-sm">
                <Plus size={16} />
                <span className="font-medium">Add Channel</span>
              </button>
            </div>
          </aside>
        )}

        {/* Main Content */}
        <main className="flex-1 bg-gray-50 p-6 overflow-auto">
          {children}
        </main>
      </div>
    </div>
  )
}

const MainLayout = memo(MainLayoutComponent)
export default MainLayout 