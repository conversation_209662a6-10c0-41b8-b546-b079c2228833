import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { google } from 'googleapis';

/**
 * Test endpoint to check if YouTube tokens can be refreshed correctly
 * GET /api/youtube/refresh-token-test?accountId=xyz
 */
export async function GET(request: NextRequest) {
  try {
    console.log('🔄 YouTube refresh-token-test endpoint called');
    
    // Create Supabase client
    const supabase = createClient();
    
    // Get the current session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    
    if (sessionError || !session) {
      console.error('❌ No authenticated session found:', sessionError);
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }
    
    const userId = session.user.id;
    console.log('Testing token refresh for user:', userId);
    
    // Get the YouTube token from the database
    const { data: tokens, error: tokenError } = await supabase
      .from('google_oauth_tokens')
      .select('*')
      .eq('user_id', userId)
      .eq('service_type', 'youtube')
      .order('updated_at', { ascending: false })
      .limit(1);
    
    if (tokenError) {
      console.error('Error fetching token:', tokenError);
      return NextResponse.json(
        { error: 'Failed to fetch YouTube token', details: tokenError.message },
        { status: 500 }
      );
    }
    
    if (!tokens || tokens.length === 0) {
      // Try connected_accounts as fallback
      const { data: accounts, error: accountsError } = await supabase
        .from('connected_accounts')
        .select('*')
        .eq('user_id', userId)
        .eq('provider', 'google_youtube')
        .order('updated_at', { ascending: false })
        .limit(1);
        
      if (accountsError || !accounts || accounts.length === 0) {
        return NextResponse.json(
          { error: 'No YouTube token found' },
          { status: 404 }
        );
      }
      
      console.log('Using connected_accounts for token refresh');
      
      // Use access_token and refresh_token from connected_accounts
      const account = accounts[0];
      
      if (!account.refresh_token) {
        return NextResponse.json(
          { error: 'No refresh token available in connected_accounts' },
          { status: 400 }
        );
      }
      
      // Try to refresh the token
      try {
        // Initialize the OAuth2 client
        const oauth2Client = new google.auth.OAuth2(
          process.env.GOOGLE_CLIENT_ID,
          process.env.GOOGLE_CLIENT_SECRET,
          `${process.env.NEXT_PUBLIC_APP_URL}/youtube-callback`
        );
        
        oauth2Client.setCredentials({
          refresh_token: account.refresh_token
        });
        
        // Get a new access token
        const response = await oauth2Client.getAccessToken();
        const newToken = response.token;
        
        if (!newToken) {
          throw new Error('Failed to get new access token');
        }
        
        // Calculate new expiry time (1 hour from now)
        const expiryDate = new Date(Date.now() + 3600 * 1000).toISOString();
        
        // Update the token in the database
        const { error: updateError } = await supabase
          .from('connected_accounts')
          .update({
            access_token: newToken,
            expires_at: expiryDate,
            updated_at: new Date().toISOString(),
            service_type: 'youtube'
          })
          .eq('id', account.id);
          
        if (updateError) {
          console.error('Error updating token in database:', updateError);
          throw new Error(`Failed to update token: ${updateError.message}`);
        }
        
        // Test if the token works with the YouTube API
        try {
          // Set up with the new token
          oauth2Client.setCredentials({
            access_token: newToken,
            refresh_token: account.refresh_token
          });
          
          const youtube = google.youtube({
            version: 'v3',
            auth: oauth2Client
          });
          
          // Make a simple API call to test the token
          const channelResponse = await youtube.channels.list({
            part: ['snippet'],
            mine: true
          });
          
          return NextResponse.json({
            source: 'connected_accounts',
            success: true, 
            message: 'Token refreshed successfully',
            account_id: account.id,
            token: {
              access_token_preview: newToken ? `${newToken.substring(0, 10)}...` : null,
              refresh_token_exists: true,
              expires_at: expiryDate
            },
            api_test: {
              success: true,
              channel_count: channelResponse.data.items?.length || 0,
              channel_id: channelResponse.data.items?.[0]?.id || null
            }
          });
        } catch (apiError) {
          console.error('Error testing token with YouTube API:', apiError);
          return NextResponse.json({
            source: 'connected_accounts',
            success: true,
            message: 'Token refreshed but API test failed',
            account_id: account.id,
            token: {
              access_token_preview: newToken ? `${newToken.substring(0, 10)}...` : null,
              refresh_token_exists: true,
              expires_at: expiryDate
            },
            api_test: {
              success: false,
              error: apiError instanceof Error ? apiError.message : String(apiError)
            }
          });
        }
      } catch (refreshError) {
        console.error('Error refreshing token from connected_accounts:', refreshError);
        return NextResponse.json({
          source: 'connected_accounts',
          success: false,
          message: 'Failed to refresh token',
          error: refreshError instanceof Error ? refreshError.message : String(refreshError),
          account_id: account.id
        });
      }
    }
    
    const tokenData = tokens[0];
    console.log('Found YouTube token, testing refresh:', {
      id: tokenData.id,
      access_token_preview: tokenData.access_token ? `${tokenData.access_token.substring(0, 10)}...` : 'null',
      refresh_token_exists: !!tokenData.refresh_token,
      expires_at: tokenData.expires_at
    });
    
    if (!tokenData.refresh_token) {
      return NextResponse.json({
        source: 'google_oauth_tokens',
        success: false,
        message: 'No refresh token available',
        token_id: tokenData.id
      });
    }
    
    // Try refreshing the token
    try {
      // Initialize the OAuth2 client
      const oauth2Client = new google.auth.OAuth2(
        process.env.GOOGLE_CLIENT_ID,
        process.env.GOOGLE_CLIENT_SECRET,
        `${process.env.NEXT_PUBLIC_APP_URL}/youtube-callback`
      );
      
      oauth2Client.setCredentials({
        refresh_token: tokenData.refresh_token
      });
      
      // Get a new access token
      const response = await oauth2Client.getAccessToken();
      const newToken = response.token;
      
      if (!newToken) {
        throw new Error('Failed to get new access token');
      }
      
      // Calculate new expiry time (1 hour from now)
      const expiryDate = new Date(Date.now() + 3600 * 1000).toISOString();
      
      // Update the token in google_oauth_tokens
      const { error: updateError } = await supabase
        .from('google_oauth_tokens')
        .update({
          access_token: newToken,
          expires_at: expiryDate,
          updated_at: new Date().toISOString()
        })
        .eq('id', tokenData.id);
        
      if (updateError) {
        console.error('Error updating token in database:', updateError);
        throw new Error(`Failed to update token: ${updateError.message}`);
      }
      
      // Also update in connected_accounts if we can find the corresponding account
      try {
        const { data: connectedAccount, error: accountError } = await supabase
          .from('connected_accounts')
          .select('id')
          .eq('provider', 'google_youtube')
          .eq('user_id', userId)
          .maybeSingle();
          
        if (!accountError && connectedAccount) {
          const { error: accountUpdateError } = await supabase
            .from('connected_accounts')
            .update({
              access_token: newToken,
              expires_at: expiryDate,
              updated_at: new Date().toISOString(),
              service_type: 'youtube'
            })
            .eq('id', connectedAccount.id);
            
          if (accountUpdateError) {
            console.error('Error updating connected_accounts:', accountUpdateError);
          } else {
            console.log('Updated connected_accounts with new token');
          }
        }
      } catch (accountError) {
        console.error('Error updating connected_accounts:', accountError);
      }
      
      // Test if the token works with the YouTube API
      try {
        // Set up with the new token
        oauth2Client.setCredentials({
          access_token: newToken,
          refresh_token: tokenData.refresh_token
        });
        
        const youtube = google.youtube({
          version: 'v3',
          auth: oauth2Client
        });
        
        // Make a simple API call to test the token
        const channelResponse = await youtube.channels.list({
          part: ['snippet'],
          mine: true
        });
        
        return NextResponse.json({
          source: 'google_oauth_tokens',
          success: true, 
          message: 'Token refreshed successfully',
          token_id: tokenData.id,
          token: {
            access_token_preview: newToken ? `${newToken.substring(0, 10)}...` : null,
            refresh_token_exists: !!tokenData.refresh_token,
            expires_at: expiryDate
          },
          api_test: {
            success: true,
            channel_count: channelResponse.data.items?.length || 0,
            channel_id: channelResponse.data.items?.[0]?.id || null
          }
        });
      } catch (apiError) {
        console.error('Error testing token with YouTube API:', apiError);
        return NextResponse.json({
          source: 'google_oauth_tokens',
          success: true,
          message: 'Token refreshed but API test failed',
          token_id: tokenData.id,
          token: {
            access_token_preview: newToken ? `${newToken.substring(0, 10)}...` : null,
            refresh_token_exists: !!tokenData.refresh_token,
            expires_at: expiryDate
          },
          api_test: {
            success: false,
            error: apiError instanceof Error ? apiError.message : String(apiError)
          }
        });
      }
    } catch (refreshError) {
      console.error('Error refreshing token:', refreshError);
      
      // Mark token as needing reauth in case it's a refresh token problem
      try {
        const needsReauth = true;
        const fieldsToUpdate: any = {
          updated_at: new Date().toISOString()
        };
        
        // Check if the needs_reauth column exists
        const { data: hasNeedsReauth, error: checkError } = await supabase.rpc(
          'has_column',
          { table_name: 'google_oauth_tokens', column_name: 'needs_reauth' }
        );
        
        if (!checkError && hasNeedsReauth) {
          fieldsToUpdate.needs_reauth = needsReauth;
        }
        
        const { error: updateError } = await supabase
          .from('google_oauth_tokens')
          .update(fieldsToUpdate)
          .eq('id', tokenData.id);
          
        if (updateError) {
          console.error('Error marking token as needing reauth:', updateError);
        }
      } catch (markError) {
        console.error('Error checking/marking token state:', markError);
      }
      
      return NextResponse.json({
        source: 'google_oauth_tokens',
        success: false,
        message: 'Failed to refresh token',
        error: refreshError instanceof Error ? refreshError.message : String(refreshError),
        token_id: tokenData.id,
        token: {
          access_token_preview: tokenData.access_token ? `${tokenData.access_token.substring(0, 10)}...` : null,
          refresh_token_exists: !!tokenData.refresh_token,
          expires_at: tokenData.expires_at
        }
      });
    }
  } catch (error) {
    console.error('❌ Error in refresh token test:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : String(error) },
      { status: 500 }
    );
  }
} 