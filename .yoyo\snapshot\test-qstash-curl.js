require('dotenv').config();
const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Extract the QStash token from the .env file
const envContent = fs.readFileSync(path.resolve('.env'), 'utf8');
const tokenMatch = envContent.match(/QSTASH_TOKEN="(.+)"/);
if (!tokenMatch) {
  console.error("Failed to find QSTASH_TOKEN in .env file");
  process.exit(1);
}
const qstashToken = tokenMatch[1];

// Define the destination URL and escape it properly for the shell
const destinationUrl = "https://7861-2404-8ec0-4-2d98-a950-a216-4887-d919.ngrok-free.app/api/posts/publish-callback";

// Create the test message
const message = JSON.stringify({
  postId: 'test-post-id',
  timestamp: new Date().toISOString()
});

// Create the curl command
const curlCommand = `curl -X POST https://qstash.upstash.io/v2/publish -H "Authorization: Bearer ${qstashToken}" -H "Content-Type: application/json" -H "Upstash-Forward-Url: ${destinationUrl}" -d '${message}'`;

// Log the sanitized command (hide token)
const sanitizedCommand = curlCommand.replace(qstashToken, '[REDACTED]');
console.log('Executing command:');
console.log(sanitizedCommand);

// Execute the curl command
try {
  const result = execSync(curlCommand).toString();
  console.log('Response:');
  console.log(result);
  
  try {
    const jsonResponse = JSON.parse(result);
    console.log('Parsed JSON response:');
    console.log(JSON.stringify(jsonResponse, null, 2));
  } catch (e) {
    console.log('Response is not valid JSON');
  }
} catch (error) {
  console.error('Error executing curl command:');
  console.error(error.message);
  if (error.stdout) {
    console.log('stdout:', error.stdout.toString());
  }
  if (error.stderr) {
    console.log('stderr:', error.stderr.toString());
  }
} 