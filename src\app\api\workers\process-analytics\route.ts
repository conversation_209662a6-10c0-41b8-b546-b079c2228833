/**
 * QStash Worker: Process Analytics
 * 
 * Handles analytics data processing with chunking for large datasets
 * POST /api/workers/process-analytics
 */

import { NextRequest } from 'next/server';
import { createSecureWorkerHandler, extractQStashMetadata, logWorkerRequest, createWorkerResponse as createErrorResponse } from '../utils/signature-verification';
import { processWorkerJob, getSupabaseClient, WorkerJobPayload, createWorkerResponse } from '../utils/worker-helpers';

// ============================================================================
// Types and Interfaces
// ============================================================================

interface AnalyticsJobPayload {
  jobId: string;
  userId?: string; // Optional - if not provided, process all users
  platform?: 'x' | 'linkedin' | 'facebook' | 'instagram'; // Optional - if not provided, process all platforms
  dateRange?: {
    startDate: string;
    endDate: string;
  };
  chunkSize?: number;
  type: 'process_analytics';
}

interface AnalyticsProcessingResult {
  totalRecords: number;
  processedRecords: number;
  failedRecords: number;
  chunksProcessed: number;
  platforms: string[];
  dateRange: {
    startDate: string;
    endDate: string;
  };
  errors: Array<{
    platform: string;
    error: string;
    recordCount: number;
  }>;
}

interface AnalyticsRecord {
  id: string;
  platform: string;
  account_id: string;
  post_id?: string;
  metric_type: string;
  metric_value: number;
  recorded_at: string;
  metadata?: any;
}

// ============================================================================
// Analytics Processing Logic
// ============================================================================

async function processAnalyticsForPlatform(
  platform: string,
  userId?: string,
  dateRange?: { startDate: string; endDate: string },
  chunkSize: number = 1000
): Promise<{
  totalRecords: number;
  processedRecords: number;
  failedRecords: number;
  chunksProcessed: number;
  errors: string[];
}> {
  const supabase = getSupabaseClient();
  let totalRecords = 0;
  let processedRecords = 0;
  let failedRecords = 0;
  let chunksProcessed = 0;
  const errors: string[] = [];

  try {
    // Build query for analytics data
    let query = supabase
      .from('platform_analytics')
      .select('*')
      .eq('platform', platform);

    if (userId) {
      query = query.eq('user_id', userId);
    }

    if (dateRange) {
      query = query
        .gte('recorded_at', dateRange.startDate)
        .lte('recorded_at', dateRange.endDate);
    }

    // Get total count first
    const { count, error: countError } = await supabase
      .from('platform_analytics')
      .select('*', { count: 'exact', head: true })
      .eq('platform', platform);

    if (countError) {
      throw new Error(`Failed to count analytics records: ${countError.message}`);
    }

    totalRecords = count || 0;
    console.log(`Found ${totalRecords} analytics records for ${platform}`);

    if (totalRecords === 0) {
      return {
        totalRecords: 0,
        processedRecords: 0,
        failedRecords: 0,
        chunksProcessed: 0,
        errors: []
      };
    }

    // Process data in chunks
    let offset = 0;
    while (offset < totalRecords) {
      try {
        const { data: chunk, error: chunkError } = await query
          .range(offset, offset + chunkSize - 1)
          .order('recorded_at', { ascending: true });

        if (chunkError) {
          throw new Error(`Failed to fetch chunk: ${chunkError.message}`);
        }

        if (!chunk || chunk.length === 0) {
          break;
        }

        // Process the chunk
        const chunkResult = await processAnalyticsChunk(chunk, platform);
        processedRecords += chunkResult.processed;
        failedRecords += chunkResult.failed;
        
        if (chunkResult.errors.length > 0) {
          errors.push(...chunkResult.errors);
        }

        chunksProcessed++;
        offset += chunkSize;

        console.log(`Processed chunk ${chunksProcessed} for ${platform}: ${chunkResult.processed} processed, ${chunkResult.failed} failed`);

        // Add small delay to prevent overwhelming the database
        await new Promise(resolve => setTimeout(resolve, 100));

      } catch (error) {
        console.error(`Error processing chunk ${chunksProcessed + 1} for ${platform}:`, error);
        errors.push(`Chunk ${chunksProcessed + 1}: ${error instanceof Error ? error.message : 'Unknown error'}`);
        failedRecords += Math.min(chunkSize, totalRecords - offset);
        offset += chunkSize;
      }
    }

    return {
      totalRecords,
      processedRecords,
      failedRecords,
      chunksProcessed,
      errors
    };

  } catch (error) {
    console.error(`Error processing analytics for ${platform}:`, error);
    throw error;
  }
}

async function processAnalyticsChunk(
  records: any[],
  platform: string
): Promise<{
  processed: number;
  failed: number;
  errors: string[];
}> {
  const supabase = getSupabaseClient();
  let processed = 0;
  let failed = 0;
  const errors: string[] = [];

  for (const record of records) {
    try {
      // Process individual analytics record
      const processedRecord = await processAnalyticsRecord(record, platform);
      
      if (processedRecord) {
        // Update or insert processed analytics
        const { error: upsertError } = await supabase
          .from('processed_analytics')
          .upsert(processedRecord, {
            onConflict: 'platform,account_id,post_id,metric_type,recorded_at'
          });

        if (upsertError) {
          throw new Error(`Failed to upsert processed record: ${upsertError.message}`);
        }

        processed++;
      } else {
        failed++;
        errors.push(`Failed to process record ${record.id}: Invalid data`);
      }

    } catch (error) {
      console.error(`Error processing record ${record.id}:`, error);
      failed++;
      errors.push(`Record ${record.id}: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  return { processed, failed, errors };
}

async function processAnalyticsRecord(
  record: any,
  platform: string
): Promise<any | null> {
  try {
    // Platform-specific analytics processing
    switch (platform.toLowerCase()) {
      case 'x':
      case 'twitter':
        return processXAnalytics(record);
        
      case 'linkedin':
        return processLinkedInAnalytics(record);
        
      case 'facebook':
        return processFacebookAnalytics(record);
        
      case 'instagram':
        return processInstagramAnalytics(record);
        
      default:
        console.warn(`Unknown platform for analytics processing: ${platform}`);
        return null;
    }
  } catch (error) {
    console.error(`Error processing ${platform} analytics record:`, error);
    return null;
  }
}

function processXAnalytics(record: any): any {
  // Process X/Twitter analytics
  return {
    id: record.id,
    platform: 'x',
    account_id: record.account_id,
    post_id: record.post_id,
    metric_type: record.metric_type,
    metric_value: record.metric_value,
    recorded_at: record.recorded_at,
    processed_at: new Date().toISOString(),
    metadata: {
      ...record.metadata,
      processed: true,
      platform_specific: {
        engagement_rate: calculateEngagementRate(record),
        reach_ratio: calculateReachRatio(record)
      }
    }
  };
}

function processLinkedInAnalytics(record: any): any {
  // Process LinkedIn analytics
  return {
    id: record.id,
    platform: 'linkedin',
    account_id: record.account_id,
    post_id: record.post_id,
    metric_type: record.metric_type,
    metric_value: record.metric_value,
    recorded_at: record.recorded_at,
    processed_at: new Date().toISOString(),
    metadata: {
      ...record.metadata,
      processed: true,
      platform_specific: {
        professional_engagement: calculateProfessionalEngagement(record),
        industry_relevance: calculateIndustryRelevance(record)
      }
    }
  };
}

function processFacebookAnalytics(record: any): any {
  // Process Facebook analytics
  return {
    id: record.id,
    platform: 'facebook',
    account_id: record.account_id,
    post_id: record.post_id,
    metric_type: record.metric_type,
    metric_value: record.metric_value,
    recorded_at: record.recorded_at,
    processed_at: new Date().toISOString(),
    metadata: {
      ...record.metadata,
      processed: true,
      platform_specific: {
        social_engagement: calculateSocialEngagement(record),
        viral_potential: calculateViralPotential(record)
      }
    }
  };
}

function processInstagramAnalytics(record: any): any {
  // Process Instagram analytics
  return {
    id: record.id,
    platform: 'instagram',
    account_id: record.account_id,
    post_id: record.post_id,
    metric_type: record.metric_type,
    metric_value: record.metric_value,
    recorded_at: record.recorded_at,
    processed_at: new Date().toISOString(),
    metadata: {
      ...record.metadata,
      processed: true,
      platform_specific: {
        visual_engagement: calculateVisualEngagement(record),
        hashtag_performance: calculateHashtagPerformance(record)
      }
    }
  };
}

// Helper functions for analytics calculations
function calculateEngagementRate(record: any): number {
  // Placeholder calculation
  return record.metric_value / (record.metadata?.impressions || 1);
}

function calculateReachRatio(record: any): number {
  // Placeholder calculation
  return record.metadata?.reach / (record.metadata?.followers || 1);
}

function calculateProfessionalEngagement(record: any): number {
  // Placeholder calculation for LinkedIn
  return record.metric_value * 1.2; // Professional content typically has higher value
}

function calculateIndustryRelevance(record: any): number {
  // Placeholder calculation
  return record.metadata?.industry_score || 0.5;
}

function calculateSocialEngagement(record: any): number {
  // Placeholder calculation for Facebook
  return record.metric_value * 0.8;
}

function calculateViralPotential(record: any): number {
  // Placeholder calculation
  return record.metadata?.shares / (record.metadata?.impressions || 1);
}

function calculateVisualEngagement(record: any): number {
  // Placeholder calculation for Instagram
  return record.metric_value * 1.5; // Visual content typically has higher engagement
}

function calculateHashtagPerformance(record: any): number {
  // Placeholder calculation
  return record.metadata?.hashtag_reach || 0;
}

// ============================================================================
// Worker Handler
// ============================================================================

async function processAnalyticsHandler(req: NextRequest): Promise<Response> {
  logWorkerRequest(req, 'ProcessAnalytics');

  const result = await processWorkerJob(req, async (payload: WorkerJobPayload) => {
    const analyticsPayload = payload as unknown as AnalyticsJobPayload;
    const {
      userId,
      platform,
      dateRange = {
        startDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days ago
        endDate: new Date().toISOString()
      },
      chunkSize = 1000
    } = analyticsPayload;
    
    console.log(`Starting analytics processing job`, {
      userId: userId || 'all users',
      platform: platform || 'all platforms',
      dateRange,
      chunkSize
    });

    const platformsToProcess = platform ? [platform] : ['x', 'linkedin', 'facebook', 'instagram'];
    const result: AnalyticsProcessingResult = {
      totalRecords: 0,
      processedRecords: 0,
      failedRecords: 0,
      chunksProcessed: 0,
      platforms: platformsToProcess,
      dateRange,
      errors: []
    };

    // Process each platform
    for (const platformName of platformsToProcess) {
      try {
        console.log(`Processing analytics for platform: ${platformName}`);
        const platformResult = await processAnalyticsForPlatform(
          platformName, 
          userId, 
          dateRange, 
          chunkSize
        );
        
        // Aggregate results
        result.totalRecords += platformResult.totalRecords;
        result.processedRecords += platformResult.processedRecords;
        result.failedRecords += platformResult.failedRecords;
        result.chunksProcessed += platformResult.chunksProcessed;
        
        if (platformResult.errors.length > 0) {
          result.errors.push({
            platform: platformName,
            error: platformResult.errors.join('; '),
            recordCount: platformResult.failedRecords
          });
        }
        
      } catch (error) {
        console.error(`Failed to process analytics for platform ${platformName}:`, error);
        result.errors.push({
          platform: platformName,
          error: error instanceof Error ? error.message : 'Unknown error',
          recordCount: 0
        });
      }
    }

    console.log(`Analytics processing job completed`, {
      totalRecords: result.totalRecords,
      processedRecords: result.processedRecords,
      failedRecords: result.failedRecords,
      chunksProcessed: result.chunksProcessed,
      errorCount: result.errors.length
    });

    return result;
  });

  return createWorkerResponse(result);
}

// ============================================================================
// Route Handlers
// ============================================================================

export const POST = createSecureWorkerHandler(processAnalyticsHandler);

export async function GET() {
  return createErrorResponse({
    success: false,
    error: {
      message: 'Method not allowed. This endpoint only accepts POST requests from QStash.',
      code: 'METHOD_NOT_ALLOWED'
    }
  }, 405);
}

export async function PUT() {
  return createErrorResponse({
    success: false,
    error: {
      message: 'Method not allowed. This endpoint only accepts POST requests from QStash.',
      code: 'METHOD_NOT_ALLOWED'
    }
  }, 405);
}

export async function DELETE() {
  return createErrorResponse({
    success: false,
    error: {
      message: 'Method not allowed. This endpoint only accepts POST requests from QStash.',
      code: 'METHOD_NOT_ALLOWED'
    }
  }, 405);
}
