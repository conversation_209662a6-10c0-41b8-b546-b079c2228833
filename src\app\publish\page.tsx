'use client'

import React, { useState, useRef, useEffect, useMemo, useCallback } from 'react'
import { Calendar, ChevronDown, List, Share2, MapPin, Search, Check, Tag, Grid, Plus, ChevronLeft, ChevronRight, Clock, FileEdit, CheckCircle, Send, Youtube } from 'lucide-react'
import Link from 'next/link'
import CalendarView from '@/components/shared/CalendarView'
import ListView from '@/components/shared/ListView'
import MainLayout from '@/components/layout/MainLayout'

import { PostCreationModal } from '@/lib/dynamic-imports'
import EditPostModal from '@/components/modals/EditPostModal'
import { useYouTubeConnections } from '@/lib/hooks/useYouTubeConnections'

import { useRouter, useSearchParams, usePathname } from 'next/navigation'
import { useToast } from '@/components/ui/toast'
import { SessionRestorer } from '@/components/SessionRestorer'
import { createClient } from '@/lib/supabase/client'

// Timezone data - updated with proper GMT offsets
const timezoneData = [
  {
    region: 'North America',
    timezones: [
      { id: 'America/New_York', name: 'New York, USA (GMT-5)', offset: -5 },
      { id: 'America/Chicago', name: 'Chicago, USA (GMT-6)', offset: -6 },
      { id: 'America/Denver', name: 'Denver, USA (GMT-7)', offset: -7 },
      { id: 'America/Phoenix', name: 'Phoenix, USA (GMT-7)', offset: -7 },
      { id: 'America/Los_Angeles', name: 'Los Angeles, USA (GMT-8)', offset: -8 },
      { id: 'America/Toronto', name: 'Toronto, Canada (GMT-5)', offset: -5 },
      { id: 'America/Vancouver', name: 'Vancouver, Canada (GMT-8)', offset: -8 },
    ]
  },
  {
    region: 'Europe',
    timezones: [
      { id: 'Europe/London', name: 'London, UK (GMT+0)', offset: 0 },
      { id: 'Europe/Paris', name: 'Paris, France (GMT+1)', offset: 1 },
      { id: 'Europe/Berlin', name: 'Berlin, Germany (GMT+1)', offset: 1 },
      { id: 'Europe/Madrid', name: 'Madrid, Spain (GMT+1)', offset: 1 },
      { id: 'Europe/Rome', name: 'Rome, Italy (GMT+1)', offset: 1 },
    ]
  },
  {
    region: 'Asia',
    timezones: [
      { id: 'Asia/Tokyo', name: 'Tokyo, Japan (GMT+9)', offset: 9 },
      { id: 'Asia/Shanghai', name: 'Shanghai, China (GMT+8)', offset: 8 },
      { id: 'Asia/Hong_Kong', name: 'Hong Kong (GMT+8)', offset: 8 },
      { id: 'Asia/Kolkata', name: 'Delhi, India (GMT+5:30)', offset: 5.5 },
      { id: 'Asia/Singapore', name: 'Singapore (GMT+8)', offset: 8 },
    ]
  },
  {
    region: 'Australia & Pacific',
    timezones: [
      { id: 'Australia/Sydney', name: 'Sydney, Australia (GMT+10)', offset: 10 },
      { id: 'Australia/Perth', name: 'Perth, Australia (GMT+8)', offset: 8 },
      { id: 'Pacific/Auckland', name: 'Auckland, New Zealand (GMT+12)', offset: 12 },
    ]
  },
  {
    region: 'South America',
    timezones: [
      { id: 'America/Sao_Paulo', name: 'São Paulo, Brazil (GMT-3)', offset: -3 },
      { id: 'America/Argentina/Buenos_Aires', name: 'Buenos Aires, Argentina (GMT-3)', offset: -3 },
    ]
  },
  {
    region: 'Africa',
    timezones: [
      { id: 'Africa/Cairo', name: 'Cairo, Egypt (GMT+2)', offset: 2 },
      { id: 'Africa/Lagos', name: 'Lagos, Nigeria (GMT+1)', offset: 1 },
      { id: 'Africa/Johannesburg', name: 'Johannesburg, South Africa (GMT+2)', offset: 2 },
    ]
  }
];

// Mock data for the different post categories
const mockPosts = {
  queue: [
    { 
      id: 'q1', 
      title: 'Social Media Strategy for 2025',
      date: new Date('2025-02-15T10:00:00'),
      channels: ['instagram', 'twitter'],
      status: 'scheduled' as const
    },
    { 
      id: 'q2', 
      title: 'New Product Launch Announcement',
      date: new Date('2025-02-16T14:30:00'),
      channels: ['facebook', 'linkedin'],
      status: 'scheduled' as const
    },
    { 
      id: 'q3', 
      title: '10 Tips for Better Social Media Engagement',
      date: new Date('2025-02-18T09:15:00'),
      channels: ['twitter', 'instagram'],
      status: 'scheduled' as const
    }
  ],
  drafts: [
    { 
      id: 'd1', 
      title: 'Customer Success Story - Draft',
      date: new Date('2025-02-20T12:00:00'),
      channels: ['facebook'],
      status: 'draft' as const
    },
    { 
      id: 'd2', 
      title: 'Upcoming Webinar Announcement - Draft',
      date: new Date('2025-02-22T15:45:00'),
      channels: ['linkedin'],
      status: 'draft' as const
    }
  ],
  approvals: [
    { 
      id: 'a1', 
      title: 'Team Retreat Photos',
      date: new Date('2025-02-24T11:00:00'),
      channels: ['instagram', 'facebook'],
      status: 'scheduled' as const
    },
    { 
      id: 'a2', 
      title: 'Monthly Newsletter Preview',
      date: new Date('2025-02-25T16:30:00'),
      channels: ['linkedin', 'twitter'],
      status: 'scheduled' as const
    }
  ],
  sent: [
    { 
      id: 's1', 
      title: 'Holiday Greetings',
      date: new Date('2025-02-01T10:00:00'),
      channels: ['instagram', 'facebook', 'twitter'],
      status: 'sent' as const
    },
    { 
      id: 's2', 
      title: 'Company Update Q1',
      date: new Date('2025-02-05T09:00:00'),
      channels: ['linkedin'],
      status: 'sent' as const
    },
    { 
      id: 's3', 
      title: 'Industry Trends Report',
      date: new Date('2025-02-10T14:00:00'),
      channels: ['twitter', 'linkedin'],
      status: 'sent' as const
    }
  ]
};

// Define the SocialChannel type to match the one in PostCreationModal
type SocialChannel = 'facebook' | 'instagram' | 'twitter' | 'linkedin' | 'youtube' | 'tiktok'

// Define the Channel interface to match PostCreationModal
interface Channel {
  id: string
  name: string
  type: SocialChannel
  avatar: string
}

// Update the mockChannels definition - keep the Facebook and Instagram mock data
const mockChannels: Channel[] = [
  {
    id: 'c1',
    name: 'Yogic Insights',
    type: 'facebook',
    avatar: 'https://i.pravatar.cc/150?img=1'
  },
  {
    id: 'c2',
    name: 'Abhick Vashtyalingam',
    type: 'instagram',
    avatar: 'https://i.pravatar.cc/150?img=2'
  }
  // YouTube channel will be added dynamically from the authenticated data
];

export default function PublishPage() {
  // Add the YouTube connections hook to fetch all YouTube channels
  const { connections: youtubeConnections, isLoading: isYouTubeLoading, error: youtubeError } = useYouTubeConnections();

  // Determine user state based on actual connections instead of demo state
  const userState = useMemo(() => {
    if (isYouTubeLoading) {
      return 'connected'; // Show connected state while loading
    }

    if (youtubeError) {
      return 'no-accounts'; // Show no accounts if there's an error
    }

    if (!youtubeConnections || youtubeConnections.length === 0) {
      return 'no-accounts'; // Show no accounts if no YouTube connections
    }

    return 'connected'; // Show connected state if there are YouTube connections
  }, [youtubeConnections, isYouTubeLoading, youtubeError]);
  const [mockPostsState, setMockPostsState] = useState(mockPosts);
  const [isLoading, setIsLoading] = useState(true);
  const [viewMode, setViewMode] = useState<'list' | 'calendar'>('list')
  const [calendarViewMode, setCalendarViewMode] = useState<'week' | 'month'>('week')
  const [currentTab, setCurrentTab] = useState<'queue' | 'drafts' | 'approvals' | 'sent'>('queue')
  const [isChannelsDropdownOpen, setIsChannelsDropdownOpen] = useState(false)
  const [isTagsDropdownOpen, setIsTagsDropdownOpen] = useState(false)
  const [isViewTypeDropdownOpen, setIsViewTypeDropdownOpen] = useState(false)
  const [isAllPostsDropdownOpen, setIsAllPostsDropdownOpen] = useState(false)
  const [tagSearchQuery, setTagSearchQuery] = useState('')
  const [selectedTimezone, setSelectedTimezone] = useState(
    timezoneData[0].timezones.find(tz => tz.id === 'America/Los_Angeles') || 
    { id: 'America/Los_Angeles', name: 'Los Angeles, USA (GMT-8)', offset: -8 }
  )
  const [isTimezoneOpen, setIsTimezoneOpen] = useState(false)
  const [timezoneSearch, setTimezoneSearch] = useState('')
  
  // New Post modal state
  const [isPostModalOpen, setIsPostModalOpen] = useState(false)
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(undefined)
  const [openDrivePicker, setOpenDrivePicker] = useState(false)
  
  // Edit Post modal state
  const [isEditModalOpen, setIsEditModalOpen] = useState(false)
  const [postToEdit, setPostToEdit] = useState<any>(null)
  
  // Update ref types to be more specific
  const channelsDropdownRef = useRef<HTMLDivElement>(null)
  const tagsDropdownRef = useRef<HTMLDivElement>(null)
  const timezoneDropdownRef = useRef<HTMLDivElement>(null)
  const viewTypeDropdownRef = useRef<HTMLDivElement>(null)
  const allPostsDropdownRef = useRef<HTMLDivElement>(null)



  // Create a state to hold the combined channels (mock + real YouTube)
  const [combinedChannels, setCombinedChannels] = useState<Channel[]>(mockChannels);

  // Update combined channels when YouTube data is fetched
  useEffect(() => {
    try {
      // Filter out any existing YouTube channels from mockChannels (not from state to avoid circular dependency)
      const nonYouTubeChannels = mockChannels.filter(channel => channel.type !== 'youtube');

      // Handle YouTube error case
      if (youtubeError) {
        console.error('YouTube connections error:', youtubeError);
        setCombinedChannels(nonYouTubeChannels);
        return;
      }

      if (youtubeConnections && youtubeConnections.length > 0) {
        // Create channel objects from all YouTube connections
        const youtubeChannels: Channel[] = youtubeConnections.map((connection, index) => ({
          id: connection.id, // Use the connection ID as the channel ID
          name: connection.metadata?.channel_title || connection.platform_account_name || `YouTube Channel ${index + 1}`,
          type: 'youtube',
          avatar: connection.metadata?.channel_thumbnail || 'https://i.pravatar.cc/150?img=3'
        }));

        console.log(`Adding ${youtubeChannels.length} YouTube channels:`, youtubeChannels.map(c => ({ id: c.id, name: c.name })));

        // Set the combined channels with all YouTube channels
        setCombinedChannels([...nonYouTubeChannels, ...youtubeChannels]);
        console.log(`Added ${youtubeChannels.length} authenticated YouTube channels`);
      } else {
        // If no YouTube connections, show only non-YouTube channels
        setCombinedChannels(nonYouTubeChannels);
        console.log('No YouTube channels connected, showing only other channels');
      }
    } catch (error) {
      console.error('Error updating combined channels:', error);
      // Fallback to mock channels only
      setCombinedChannels(mockChannels);
    }
  }, [youtubeConnections, youtubeError]);

  // Close dropdowns when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (channelsDropdownRef.current && !channelsDropdownRef.current.contains(event.target as Node)) {
        setIsChannelsDropdownOpen(false)
      }
      if (tagsDropdownRef.current && !tagsDropdownRef.current.contains(event.target as Node)) {
        setIsTagsDropdownOpen(false)
      }
      if (timezoneDropdownRef.current && !timezoneDropdownRef.current.contains(event.target as Node)) {
        setIsTimezoneOpen(false)
      }
      if (viewTypeDropdownRef.current && !viewTypeDropdownRef.current.contains(event.target as Node)) {
        setIsViewTypeDropdownOpen(false)
      }
      if (allPostsDropdownRef.current && !allPostsDropdownRef.current.contains(event.target as Node)) {
        setIsAllPostsDropdownOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  // Add this useEffect to check for the openDrivePicker parameter
  useEffect(() => {
    // Check if we should automatically open the post creation modal
    if (typeof window !== 'undefined') {
      const urlParams = new URLSearchParams(window.location.search);
      const openDrivePickerParam = urlParams.get('openDrivePicker');
      
      if (openDrivePickerParam === 'true') {
        console.log('Auto-opening post creation modal with Drive picker');
        setOpenDrivePicker(true);
        
        // Wait a moment to ensure the component is fully mounted
        setTimeout(() => {
          handleOpenPostModal();
          
          // Remove the parameter from the URL to prevent reopening on refresh
          const newUrl = window.location.pathname;
          window.history.replaceState({}, document.title, newUrl);
        }, 500);
      }
    }
  }, []);

  // Memoized function to fetch posts with authentication
  const fetchPosts = useCallback(async () => {
      try {
        setIsLoading(true);

        // Get Supabase session for authentication
        const supabase = createClient();
        const { data: { session }, error: sessionError } = await supabase.auth.getSession();

        if (sessionError || !session) {
          console.log('No active session, skipping posts fetch');
          setIsLoading(false);
          return;
        }

        const response = await fetch('/api/posts', {
          headers: {
            'Authorization': `Bearer ${session.access_token}`,
            'Content-Type': 'application/json'
          }
        });

        if (!response.ok) {
          if (response.status === 401) {
            console.log('Authentication required for posts API');
            setIsLoading(false);
            return;
          }
          throw new Error('Failed to fetch posts');
        }
        
        const data = await response.json();
        if (data.success && data.posts) {
          // Transform and categorize posts
          const scheduledPosts = data.posts.filter((post: any) => post.status === 'scheduled').map((post: any) => {
            // Handle different possible API response structures
            const channels = post.channels || post.post_channels || [];
            return {
            id: post.id,
            title: post.title || 'New Post',
            date: new Date(post.scheduled_at),
              channels: channels.map((pc: any) => {
                // Extract provider name from any of the possible locations
                return pc.provider || 
                      (pc.connected_account ? pc.connected_account.provider : undefined) || 
                      'unknown';
              }),
            status: 'scheduled' as const
            };
          });
          
          const draftPosts = data.posts.filter((post: any) => post.status === 'draft').map((post: any) => {
            // Handle different possible API response structures
            const channels = post.channels || post.post_channels || [];
            return {
            id: post.id,
            title: post.title || 'Draft Post',
            date: new Date(post.created_at),
              channels: channels.map((pc: any) => {
                // Extract provider name from any of the possible locations
                return pc.provider || 
                      (pc.connected_account ? pc.connected_account.provider : undefined) || 
                      'unknown';
              }),
            status: 'draft' as const
            };
          });
          
          const publishedPosts = data.posts.filter((post: any) => post.status === 'published').map((post: any) => {
            // Handle different possible API response structures
            const channels = post.channels || post.post_channels || [];
            return {
            id: post.id,
            title: post.title || 'Published Post',
            date: new Date(post.published_at || post.created_at),
              channels: channels.map((pc: any) => {
                // Extract provider name from any of the possible locations
                return pc.provider || 
                      (pc.connected_account ? pc.connected_account.provider : undefined) || 
                      'unknown';
              }),
            status: 'sent' as const
            };
          });
          
          // Merge with demo posts if needed
          setMockPostsState({
            queue: [...scheduledPosts, ...mockPosts.queue],
            drafts: [...draftPosts, ...mockPosts.drafts],
            approvals: [...mockPosts.approvals],
            sent: [...publishedPosts, ...mockPosts.sent]
          });
        }
      } catch (error) {
        console.error('Error fetching posts:', error);
      } finally {
        setIsLoading(false);
      }
  }, []);

  // Fetch real scheduled posts from the database on component mount
  useEffect(() => {
    fetchPosts();
  }, [fetchPosts]);

  // Add a listener for the YouTube channel removal event
  useEffect(() => {
    // Function to handle YouTube channel removal event
    const handleYouTubeChannelRemoved = () => {
      console.log('YouTube channel removed event detected, refreshing channels');
      // Remove any YouTube channels from the combined channels list immediately
      setCombinedChannels(prev => prev.filter(channel => channel.type !== 'youtube'));
    };
    
    // Listen for the custom event
    window.addEventListener('youtube_channel_removed', handleYouTubeChannelRemoved);
    
    // Also check if we have the youtube_removed URL parameter
    if (typeof window !== 'undefined') {
      const urlParams = new URLSearchParams(window.location.search);
      if (urlParams.get('youtube_removed') === 'true') {
        console.log('YouTube removal detected from URL parameter');
        // Remove the parameter from the URL to prevent reapplying on refresh
        const newUrl = window.location.pathname;
        window.history.replaceState({}, document.title, newUrl);
        
        // Remove any YouTube channels from the combined channels immediately
        handleYouTubeChannelRemoved();
      }
    }
    
    return () => {
      window.removeEventListener('youtube_channel_removed', handleYouTubeChannelRemoved);
    };
  }, []);

  // New effect to check for scheduledDate parameter in URL
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const urlParams = new URLSearchParams(window.location.search);
      const openModal = urlParams.get('openPostModal');
      const scheduledDateParam = urlParams.get('scheduledDate');
      
      if (openModal === 'true' && scheduledDateParam) {
        try {
          // Parse the ISO date string from the URL
          const scheduledDate = new Date(decodeURIComponent(scheduledDateParam));
          
          console.log('Opening post modal with recommended date:', scheduledDate.toString());
          console.log('Time details:', `Hour: ${scheduledDate.getHours()}, Minute: ${scheduledDate.getMinutes()}`);
          
          // Set the selected date and open the modal
          setSelectedDate(scheduledDate);
          setIsPostModalOpen(true);
          
          // Switch to calendar view to show scheduled posts
          setViewMode('calendar');
          
          // Remove the parameters from the URL to prevent reopening on refresh
          const newUrl = window.location.pathname;
          window.history.replaceState({}, document.title, newUrl);
        } catch (error) {
          console.error('Error parsing scheduled date:', error);
        }
      }
    }
  }, []);  // Empty dependency array ensures this runs once on mount

  const handleCalendarViewChange = (view: 'week' | 'month') => {
    setCalendarViewMode(view)
    setIsViewTypeDropdownOpen(false)
  }

  // Get the count for each tab
  const getTabCount = (tab: 'queue' | 'drafts' | 'approvals' | 'sent') => {
    return mockPostsState[tab].length;
  }

  // Get the appropriate icon for each tab
  const getTabIcon = (tab: 'queue' | 'drafts' | 'approvals' | 'sent') => {
    switch(tab) {
      case 'queue':
        return <Clock size={16} className="text-gray-500" />;
      case 'drafts':
        return <FileEdit size={16} className="text-gray-500" />;
      case 'approvals':
        return <CheckCircle size={16} className="text-gray-500" />;
      case 'sent':
        return <Send size={16} className="text-gray-500" />;
    }
  }

  // Add handlers for the post modal
  const handleOpenPostModal = (date?: Date) => {
    if (date) {
      console.log("OPENING MODAL WITH EXACT DATE:", date.toString());
      console.log("EXACT TIME DETAILS:", `Hour: ${date.getHours()}, Minute: ${date.getMinutes()}`);
      
      // Create a new date object to avoid reference issues
      const selectedDateCopy = new Date(date);
      
      // Ensure we're preserving the exact time
      console.log("PRESERVED TIME:", selectedDateCopy.toTimeString());
      
      setSelectedDate(selectedDateCopy);
    } else {
      setSelectedDate(undefined);
    }
    setIsPostModalOpen(true);
  }
  
  const handleClosePostModal = () => {
    setIsPostModalOpen(false)
    setSelectedDate(undefined)
    setOpenDrivePicker(false)
  }
  
  // Add handlers for the edit modal
  const handleOpenEditModal = (postId: string) => {
    // Find the post to edit
    const allPosts = [
      ...mockPostsState.queue, 
      ...mockPostsState.drafts, 
      ...mockPostsState.sent,
      ...mockPostsState.approvals
    ];
    const post = allPosts.find(p => p.id === postId);
    
    if (post) {
      // Find the channel for this post
      const channel = combinedChannels.find(ch => post.channels.includes(ch.type));
      
      if (channel) {
        setPostToEdit({
          id: post.id,
          content: post.title, // Using title as content for demo
          scheduledDate: post.date,
          channel: channel,
          postType: 'Post' // Default to Post type
        });
        
        setIsEditModalOpen(true);
      }
    }
  }
  
  const handleCloseEditModal = () => {
    setIsEditModalOpen(false)
    setPostToEdit(null)
  }
  
  const handleSaveEditedPost = (postData: any) => {
    // Update the post in the queue
    const postIndex = mockPostsState.queue.findIndex(p => p.id === postData.id);
    
    if (postIndex !== -1) {
      mockPostsState.queue[postIndex] = {
        ...mockPostsState.queue[postIndex],
        title: postData.content,
        date: postData.scheduledDate,
        channels: [postData.channel.type]
      };
    }
    
    setIsEditModalOpen(false);
    setPostToEdit(null);
  }
  
  const handleSaveAsDraft = (postData: any) => {
    // Here you would typically save the post data to your state/API
    console.log('Saving as draft:', postData)
    // Add to drafts tab
    mockPostsState.drafts.unshift({
      id: `d${Date.now()}`,
      title: postData.content?.slice(0, 30) || 'New Draft',
      date: new Date(),
      channels: postData.channels.map((channel: any) => {
        return channel ? channel.type : 'unknown';
      }),
      status: 'draft' as const
    });
    
    setIsPostModalOpen(false)
    setCurrentTab('drafts')
  }
  
  const handleAddToQueue = (postData: any) => {
    // Here you would typically save the post data to your state/API
    console.log('Adding to queue:', postData);
    
    // Create a new post with the appropriate date
    let postDate;
    
    if (postData.scheduledDate) {
      // Use the scheduledDate directly from the modal
      postDate = postData.scheduledDate;
      
      console.log('SCHEDULED POST FOR:', postDate.toString());
      console.log('DATE DETAILS:', {
        day: postDate.getDate(),
        month: postDate.getMonth() + 1,
        year: postDate.getFullYear(),
        hour: postDate.getHours(),
        minute: postDate.getMinutes()
      });
    } else {
      // Default to tomorrow if no scheduled date
      postDate = new Date();
      postDate.setDate(postDate.getDate() + 1);
      postDate.setHours(9, 0, 0, 0); // Default to 9:00 AM
    }
    
    // Add to queue tab with the scheduled date
    const newPost = {
      id: `q${Date.now()}`,
      title: postData.content?.slice(0, 30) || 'New Post',
      date: postDate, // Use the scheduled date if available
      channels: postData.channels.map((channel: any) => {
        return channel ? channel.type : 'unknown';
      }),
      status: 'scheduled' as const
    };
    
    console.log('NEW POST ADDED TO QUEUE:', {
      title: newPost.title,
      date: newPost.date.toString(),
      formattedDate: `${newPost.date.getFullYear()}-${newPost.date.getMonth()+1}-${newPost.date.getDate()} ${newPost.date.getHours()}:${newPost.date.getMinutes()}`
    });
    
    mockPostsState.queue.unshift(newPost);
    setIsPostModalOpen(false);
    
    // If scheduling was used, switch to calendar view to show the scheduled post
    if (postData.scheduledDate) {
      setViewMode('calendar');
    } else {
      // If not scheduling, just go to queue tab in list view
      setCurrentTab('queue');
    }
  }

  // Add handlers for duplicating posts
  const handleDuplicatePost = (postId: string) => {
    // Find the post to duplicate
    const allPosts = [
      ...mockPostsState.queue, 
      ...mockPostsState.drafts, 
      ...mockPostsState.sent,
      ...mockPostsState.approvals
    ];
    const postToDuplicate = allPosts.find(p => p.id === postId);
    
    if (postToDuplicate) {
      // Create a new post with a new ID but the same content
      const newPost = {
        ...postToDuplicate,
        id: `post-${Date.now()}`, // Generate a new unique ID
        title: `${postToDuplicate.title} (Copy)`,
        date: new Date(postToDuplicate.date) // Create a new date object
      };
      
      // Add the duplicated post to the appropriate tab
      const updatedMockPosts = { ...mockPostsState };
      
      if (postToDuplicate.status === 'scheduled') {
        updatedMockPosts.queue = [...updatedMockPosts.queue, { ...newPost, status: 'scheduled' as const }];
      } else if (postToDuplicate.status === 'draft') {
        updatedMockPosts.drafts = [...updatedMockPosts.drafts, { ...newPost, status: 'draft' as const }];
      } else if (postToDuplicate.status === 'sent') {
        // For sent posts, add them as drafts instead
        updatedMockPosts.drafts = [...updatedMockPosts.drafts, { 
          ...newPost, 
          status: 'draft' as const 
        }];
      }
      
      // Update state
      setMockPostsState(updatedMockPosts);
      
      // Show a success message
      alert(`Post duplicated successfully!`);
    }
  }
  
  // Add handlers for deleting posts
  const handleDeletePost = (postId: string) => {
    // Ask for confirmation before deleting
    if (confirm('Are you sure you want to delete this post?')) {
      // Create a copy of the current state
      const updatedMockPosts = { ...mockPostsState };
      let found = false;
      
      // Check queue
      const queueIndex = updatedMockPosts.queue.findIndex(p => p.id === postId);
      if (queueIndex !== -1) {
        updatedMockPosts.queue.splice(queueIndex, 1);
        found = true;
      }
      
      // Check drafts
      if (!found) {
        const draftsIndex = updatedMockPosts.drafts.findIndex(p => p.id === postId);
        if (draftsIndex !== -1) {
          updatedMockPosts.drafts.splice(draftsIndex, 1);
          found = true;
        }
      }
      
      // Check sent
      if (!found) {
        const sentIndex = updatedMockPosts.sent.findIndex(p => p.id === postId);
        if (sentIndex !== -1) {
          updatedMockPosts.sent.splice(sentIndex, 1);
          found = true;
        }
      }
      
      // Check approvals
      if (!found) {
        const approvalsIndex = updatedMockPosts.approvals.findIndex(p => p.id === postId);
        if (approvalsIndex !== -1) {
          updatedMockPosts.approvals.splice(approvalsIndex, 1);
          found = true;
        }
      }
      
      // Update state if a post was found and deleted
      if (found) {
        setMockPostsState(updatedMockPosts);
        // Show a success message
        alert('Post deleted successfully!');
      }
    }
  }

  // Render different content based on user state
  const renderContent = () => {
    if (userState === 'new') {
      return <NewUserState />
    } else if (userState === 'no-accounts') {
      return <NoAccountsState />
    } else {
      return (
        <ConnectedState
          viewMode={viewMode}
          setViewMode={setViewMode}
          calendarViewMode={calendarViewMode}
          currentTab={currentTab}
          setCurrentTab={setCurrentTab}
          isChannelsDropdownOpen={isChannelsDropdownOpen}
          setIsChannelsDropdownOpen={setIsChannelsDropdownOpen}
          isTagsDropdownOpen={isTagsDropdownOpen}
          setIsTagsDropdownOpen={setIsTagsDropdownOpen}
          isViewTypeDropdownOpen={isViewTypeDropdownOpen}
          setIsViewTypeDropdownOpen={setIsViewTypeDropdownOpen}
          isAllPostsDropdownOpen={isAllPostsDropdownOpen}
          setIsAllPostsDropdownOpen={setIsAllPostsDropdownOpen}
          tagSearchQuery={tagSearchQuery}
          setTagSearchQuery={setTagSearchQuery}
          selectedTimezone={selectedTimezone}
          setSelectedTimezone={setSelectedTimezone}
          isTimezoneOpen={isTimezoneOpen}
          setIsTimezoneOpen={setIsTimezoneOpen}
          timezoneSearch={timezoneSearch}
          setTimezoneSearch={setTimezoneSearch}
          channelsDropdownRef={channelsDropdownRef}
          tagsDropdownRef={tagsDropdownRef}
          timezoneDropdownRef={timezoneDropdownRef}
          viewTypeDropdownRef={viewTypeDropdownRef}
          allPostsDropdownRef={allPostsDropdownRef}
          handleCalendarViewChange={handleCalendarViewChange}
          getTabCount={getTabCount}
          getTabIcon={getTabIcon}
          openPostModal={handleOpenPostModal}
          openEditModal={handleOpenEditModal}
          mockPostsState={mockPostsState}
          handleEditPost={handleOpenEditModal}
          handleDuplicatePost={handleDuplicatePost}
          handleDeletePost={handleDeletePost}
        />
      )
    }
  }
  
  const router = useRouter();
  const searchParams = useSearchParams();
  const pathname = usePathname();
  
  // Get error parameters from URL if present
  const error = searchParams?.get('error');
  const authError = searchParams?.get('auth_error');
  const errorMessage = searchParams?.get('message');
  
  // Toast for notifications
  const { toast } = useToast();
  
  useEffect(() => {
    // Only process if we have error parameters to avoid infinite loops
    if (!error && !authError) {
      return;
    }

    // Show error toast if there's an error in the URL parameters
    toast({
      title: authError ? 'Authentication Required' : 'Error',
      description: errorMessage || 'Something went wrong. Please try again.',
      variant: 'destructive',
    });

    // Remove error params from URL
    const newUrl = pathname || '/publish';
    window.history.replaceState({}, '', newUrl);
  }, []); // Empty dependency array to run only once on mount
  
  return (
    <MainLayout>
      {/* Session restorer for handling force_reauth */}
      <SessionRestorer />
      
      {renderContent()}
      
      {/* Post Creation Modal */}
      <PostCreationModal 
        isOpen={isPostModalOpen}
        onClose={handleClosePostModal}
        channels={combinedChannels}
        onSaveAsDraft={handleSaveAsDraft}
        onAddToQueue={handleAddToQueue}
        initialDate={selectedDate}
        openDrivePicker={openDrivePicker}
      />
      
      {/* Edit Post Modal */}
      {postToEdit && (
        <EditPostModal 
          isOpen={isEditModalOpen}
          onClose={handleCloseEditModal}
          post={postToEdit}
          onSave={handleSaveEditedPost}
        />
      )}


    </MainLayout>
  )
}

// New User State Component
function NewUserState() {
  return (
    <div className="flex-1 flex flex-col items-center justify-center bg-gray-50 p-10">
      <div className="bg-white rounded-xl border border-gray-200 p-10 text-center shadow-sm hover:shadow-md transition-all duration-300 max-w-2xl w-full">
        <div className="max-w-md mx-auto">
          <div className="w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6 shadow-inner">
            <Calendar size={32} className="text-blue-600" />
          </div>
          <h2 className="text-3xl font-bold text-gray-900 mb-4">Welcome to Publishing</h2>
          <p className="text-gray-600 mb-8 text-lg">
            Connect your social media accounts to start scheduling and publishing content across all your platforms from one place.
          </p>
          <button className="px-8 py-4 bg-gradient-to-r from-blue-600 to-blue-500 text-white rounded-lg font-medium hover:from-blue-700 hover:to-blue-600 transition-all duration-300 shadow-md hover:shadow-lg transform hover:-translate-y-1">
            Connect Your Accounts
          </button>
          <p className="mt-6 text-sm text-gray-500">
            You&apos;ll be able to schedule, draft, and publish posts once your accounts are connected.
          </p>
        </div>
      </div>
    </div>
  );
}

// No Accounts State Component
function NoAccountsState() {
  return (
    <div className="flex-1 flex flex-col">
      {/* All Channels Header */}
      <div className="bg-white border-b border-[#E5E5E5] px-6 py-3 flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Grid size={20} className="text-gray-600" />
          <h1 className="text-xl font-semibold">All Channels</h1>
          <span className="px-2 py-0.5 text-xs font-medium bg-gray-100 text-gray-600 rounded">New</span>
        </div>
        
        <div className="flex items-center gap-4">
          <button className="flex items-center gap-2 px-3 py-1.5 rounded-lg text-gray-600">
            <Share2 size={18} />
            Share Feedback
          </button>
          
          <div className="flex border border-gray-200 rounded-md">
            <button className="flex items-center gap-1 px-3 py-1.5 bg-gray-100">
              <List size={18} />
              <span className="text-sm">List</span>
            </button>
            <button className="flex items-center gap-1 px-3 py-1.5 border-l border-gray-200">
              <Calendar size={18} />
              <span className="text-sm">Calendar</span>
            </button>
          </div>
          
          <button className="flex items-center gap-2 px-3 py-1.5 rounded-md bg-[#4461F2] text-white">
            <Plus size={18} />
            <span className="text-sm">New Post</span>
          </button>
        </div>
      </div>

      {/* Tabs for Queue, Drafts, Approvals, Sent */}
      <div className="bg-white border-b border-[#E5E5E5] px-6 py-2 flex items-center">
        <div className="flex space-x-1">
          {(['queue', 'drafts', 'approvals', 'sent'] as const).map((tab) => (
            <button
              key={tab}
              className={`flex items-center gap-2 px-4 py-2 text-sm rounded-md transition-colors ${
                tab === 'queue'
                  ? 'bg-blue-50 text-[#4461F2] font-medium'
                  : 'text-gray-600 hover:bg-gray-50'
              }`}
            >
              {tab === 'queue' && <Clock size={16} className="text-gray-500" />}
              {tab === 'drafts' && <FileEdit size={16} className="text-gray-500" />}
              {tab === 'approvals' && <CheckCircle size={16} className="text-gray-500" />}
              {tab === 'sent' && <Send size={16} className="text-gray-500" />}
              <span className="capitalize">{tab}</span>
              <span className="ml-1 px-1.5 py-0.5 text-xs rounded-full bg-gray-100 text-gray-600">
                0
              </span>
            </button>
          ))}
        </div>
      </div>

      {/* Main Content Area */}
      <div className="flex-1 flex flex-col items-center justify-center bg-gray-50 p-10">
        <div className="bg-white rounded-xl border border-gray-200 p-10 text-center shadow-sm max-w-2xl w-full">
          <div className="max-w-md mx-auto">
            <div className="w-20 h-20 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-6 shadow-inner">
              <Grid size={32} className="text-yellow-600" />
            </div>
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Connect Your Channels</h2>
            <p className="text-gray-600 mb-8 text-lg">
              You haven&apos;t connected any social media channels yet. Connect your accounts to start scheduling and publishing content.
            </p>
            <button className="px-8 py-4 bg-gradient-to-r from-blue-600 to-blue-500 text-white rounded-lg font-medium hover:from-blue-700 hover:to-blue-600 transition-all duration-300 shadow-md hover:shadow-lg transform hover:-translate-y-1">
              Connect Channels
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

// Connected State Component
interface ConnectedStateProps {
  viewMode: 'list' | 'calendar';
  setViewMode: (mode: 'list' | 'calendar') => void;
  calendarViewMode: 'week' | 'month';
  currentTab: 'queue' | 'drafts' | 'approvals' | 'sent';
  setCurrentTab: (tab: 'queue' | 'drafts' | 'approvals' | 'sent') => void;
  isChannelsDropdownOpen: boolean;
  setIsChannelsDropdownOpen: (open: boolean) => void;
  isTagsDropdownOpen: boolean;
  setIsTagsDropdownOpen: (open: boolean) => void;
  isViewTypeDropdownOpen: boolean;
  setIsViewTypeDropdownOpen: (open: boolean) => void;
  isAllPostsDropdownOpen: boolean;
  setIsAllPostsDropdownOpen: (open: boolean) => void;
  tagSearchQuery: string;
  setTagSearchQuery: (query: string) => void;
  selectedTimezone: { id: string; name: string; offset: number };
  setSelectedTimezone: (timezone: { id: string; name: string; offset: number }) => void;
  isTimezoneOpen: boolean;
  setIsTimezoneOpen: (open: boolean) => void;
  timezoneSearch: string;
  setTimezoneSearch: (search: string) => void;
  channelsDropdownRef: React.RefObject<HTMLDivElement>;
  tagsDropdownRef: React.RefObject<HTMLDivElement>;
  timezoneDropdownRef: React.RefObject<HTMLDivElement>;
  viewTypeDropdownRef: React.RefObject<HTMLDivElement>;
  allPostsDropdownRef: React.RefObject<HTMLDivElement>;
  handleCalendarViewChange: (view: 'week' | 'month') => void;
  getTabCount: (tab: 'queue' | 'drafts' | 'approvals' | 'sent') => number;
  getTabIcon: (tab: 'queue' | 'drafts' | 'approvals' | 'sent') => React.ReactNode;
  openPostModal: (date?: Date) => void;
  openEditModal: (postId: string) => void;
  mockPostsState: {
    queue: Array<{
      id: string;
      title: string;
      date: Date;
      channels: string[];
      status: 'scheduled';
    }>;
    drafts: Array<{
      id: string;
      title: string;
      date: Date;
      channels: string[];
      status: 'draft';
    }>;
    approvals: Array<{
      id: string;
      title: string;
      date: Date;
      channels: string[];
      status: string;
    }>;
    sent: Array<{
      id: string;
      title: string;
      date: Date;
      channels: string[];
      status: 'sent';
    }>;
  };
  handleEditPost: (postId: string) => void;
  handleDuplicatePost: (postId: string) => void;
  handleDeletePost: (postId: string) => void;
}

function ConnectedState({
  viewMode,
  setViewMode,
  calendarViewMode,
  currentTab,
  setCurrentTab,
  isChannelsDropdownOpen,
  setIsChannelsDropdownOpen,
  isTagsDropdownOpen,
  setIsTagsDropdownOpen,
  isViewTypeDropdownOpen,
  setIsViewTypeDropdownOpen,
  isAllPostsDropdownOpen,
  setIsAllPostsDropdownOpen,
  tagSearchQuery,
  setTagSearchQuery,
  selectedTimezone,
  setSelectedTimezone,
  isTimezoneOpen,
  setIsTimezoneOpen,
  timezoneSearch,
  setTimezoneSearch,
  channelsDropdownRef,
  tagsDropdownRef,
  timezoneDropdownRef,
  viewTypeDropdownRef,
  allPostsDropdownRef,
  handleCalendarViewChange,
  getTabCount,
  getTabIcon,
  openPostModal,
  openEditModal,
  mockPostsState,
  handleEditPost,
  handleDuplicatePost,
  handleDeletePost
}: ConnectedStateProps) {
  // Get the YouTube connections and combined channels from the parent component
  const { connections: youtubeConnections } = useYouTubeConnections();
  const [combinedChannels, setCombinedChannels] = useState<Channel[]>([]);

  // Update combined channels when YouTube data is fetched
  useEffect(() => {
    try {
      // Filter out any existing YouTube channels from mockChannels
      const nonYouTubeChannels = mockChannels.filter(channel => channel.type !== 'youtube');

      if (youtubeConnections && youtubeConnections.length > 0) {
        // Create channel objects from all YouTube connections
        const youtubeChannels: Channel[] = youtubeConnections.map((connection, index) => ({
          id: connection.id,
          name: connection.metadata?.channel_title || connection.platform_account_name || `YouTube Channel ${index + 1}`,
          type: 'youtube',
          avatar: connection.metadata?.channel_thumbnail || 'https://i.pravatar.cc/150?img=3'
        }));

        // Set the combined channels with all YouTube channels
        setCombinedChannels([...nonYouTubeChannels, ...youtubeChannels]);
      } else {
        // If no YouTube connections, show only non-YouTube channels
        setCombinedChannels(nonYouTubeChannels);
      }
    } catch (error) {
      console.error('Error updating combined channels:', error);
      // Fallback to mock channels only
      setCombinedChannels(mockChannels);
    }
  }, [youtubeConnections]);
  return (
    <div className="flex-1 flex flex-col">
      {/* All Channels Header */}
      <div className="bg-white border-b border-[#E5E5E5] px-6 py-4 flex items-center justify-between shadow-sm">
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
            <Grid size={18} className="text-blue-600" />
          </div>
          <h1 className="text-xl font-semibold text-gray-900">All Channels</h1>
          <span className="px-2 py-0.5 text-xs font-medium bg-blue-100 text-blue-700 rounded-full">New</span>
        </div>
        
        <div className="flex items-center gap-4">
          <button className="flex items-center gap-2 px-3 py-1.5 rounded-lg text-gray-600 hover:bg-gray-50 transition-colors duration-200">
            <Share2 size={18} />
            <span className="font-medium">Share Feedback</span>
          </button>
          
          <div className="flex border border-gray-200 rounded-lg overflow-hidden shadow-sm">
            <button
              className={`flex items-center gap-1 px-3 py-2 ${viewMode === 'list' ? 'bg-blue-50 text-blue-600 font-medium' : 'bg-white text-gray-600 hover:bg-gray-50'} transition-colors duration-200`}
              onClick={() => setViewMode('list')}
            >
              <List size={18} />
              <span className="text-sm">List</span>
            </button>
            <button
              className={`flex items-center gap-1 px-3 py-2 border-l border-gray-200 ${viewMode === 'calendar' ? 'bg-blue-50 text-blue-600 font-medium' : 'bg-white text-gray-600 hover:bg-gray-50'} transition-colors duration-200`}
              onClick={() => setViewMode('calendar')}
            >
              <Calendar size={18} />
              <span className="text-sm">Calendar</span>
            </button>
          </div>
          
          <button 
            className="flex items-center gap-2 px-4 py-2 rounded-lg bg-blue-600 text-white hover:bg-blue-700 transition-colors duration-200 shadow-sm"
            onClick={() => openPostModal()}
          >
            <Plus size={18} />
            <span className="text-sm font-medium">New Post</span>
          </button>
        </div>
      </div>

      {/* Tabs for Queue, Drafts, Approvals, Sent */}
      <div className="bg-white border-b border-[#E5E5E5] px-6 py-2 flex items-center">
        <div className="flex space-x-1">
          {(['queue', 'drafts', 'approvals', 'sent'] as const).map((tab) => (
            <button
              key={tab}
              onClick={() => setCurrentTab(tab)}
              className={`flex items-center gap-2 px-4 py-2.5 text-sm rounded-md transition-colors ${
                currentTab === tab
                  ? 'bg-blue-50 text-blue-600 font-medium'
                  : 'text-gray-600 hover:bg-gray-50'
              }`}
            >
              {getTabIcon(tab)}
              <span className="capitalize">{tab}</span>
              <span className="ml-1 px-2 py-0.5 text-xs rounded-full bg-gray-100 text-gray-600 font-medium">
                {getTabCount(tab)}
              </span>
            </button>
          ))}
        </div>
      </div>

      <div className="bg-white border-b border-[#E5E5E5] px-6 py-3 flex items-center justify-between shadow-sm">
        <div className="flex items-center">
          <div ref={allPostsDropdownRef} className="relative mr-2">
            <button
              onClick={() => setIsAllPostsDropdownOpen(!isAllPostsDropdownOpen)}
              className="flex items-center gap-2 px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded-md transition-colors duration-200"
            >
              <span className="font-medium">All Posts</span>
              <ChevronDown size={16} className={`text-gray-500 transition-transform duration-200 ${isAllPostsDropdownOpen ? 'rotate-180' : ''}`} />
            </button>
            {isAllPostsDropdownOpen && (
              <div className="absolute top-full left-0 mt-1 bg-white rounded-lg shadow-lg border border-gray-200 z-10 min-w-[180px] overflow-hidden">
                <button className="block w-full text-left px-4 py-2.5 text-sm bg-blue-50 text-blue-600 font-medium hover:bg-blue-100 transition-colors duration-200">
                  All Posts
                </button>
                <button className="block w-full text-left px-4 py-2.5 text-sm hover:bg-gray-50 transition-colors duration-200">
                  Scheduled Posts
                </button>
                <button className="block w-full text-left px-4 py-2.5 text-sm hover:bg-gray-50 transition-colors duration-200">
                  Draft Posts
                </button>
              </div>
            )}
          </div>
        </div>

        <div className="flex items-center gap-2">
          <div ref={channelsDropdownRef} className="relative">
            <button
              onClick={() => setIsChannelsDropdownOpen(!isChannelsDropdownOpen)}
              className="flex items-center gap-2 px-3 py-2 text-sm text-gray-700 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors duration-200 shadow-sm"
            >
              <Grid size={16} className="text-gray-500" />
              <span>Channels</span>
              <ChevronDown size={16} className={`text-gray-500 transition-transform duration-200 ${isChannelsDropdownOpen ? 'rotate-180' : ''}`} />
            </button>
            {isChannelsDropdownOpen && (
              <div className="absolute top-full right-0 mt-2 w-[320px] bg-white rounded-lg shadow-lg border border-gray-200 z-10 max-h-96 overflow-y-auto">
                {combinedChannels.length > 0 ? (
                  <div className="p-4">
                    <div className="mb-3">
                      <h3 className="text-sm font-medium text-gray-900 mb-2">Connected Channels</h3>
                    </div>
                    <div className="space-y-2 mb-4">
                      {combinedChannels.map((channel) => (
                        <div key={channel.id} className="flex items-center gap-3 p-2 rounded-lg hover:bg-gray-50 transition-colors">
                          <div className="w-8 h-8 rounded-full overflow-hidden flex-shrink-0">
                            <img
                              src={channel.avatar}
                              alt={channel.name}
                              className="w-full h-full object-cover"
                              onError={(e) => {
                                const target = e.target as HTMLImageElement;
                                target.src = 'https://i.pravatar.cc/150?img=3';
                              }}
                            />
                          </div>
                          <div className="flex-1 min-w-0">
                            <p className="text-sm font-medium text-gray-900 truncate">{channel.name}</p>
                            <p className="text-xs text-gray-500 capitalize">{channel.type}</p>
                          </div>
                          <div className="w-2 h-2 bg-green-400 rounded-full flex-shrink-0" title="Connected"></div>
                        </div>
                      ))}
                    </div>
                    <div className="border-t border-gray-200 pt-3">
                      <Link
                        href="/connect/youtube"
                        className="flex items-center gap-2 w-full px-3 py-2 text-sm text-blue-600 hover:bg-blue-50 rounded-lg transition-colors duration-200"
                        onClick={() => setIsChannelsDropdownOpen(false)}
                      >
                        <Youtube size={16} />
                        <span>Connect YouTube Channel</span>
                      </Link>
                    </div>
                  </div>
                ) : (
                  <div className="flex flex-col items-center justify-center py-8 px-5">
                    <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-4 shadow-inner">
                      <Grid size={28} className="text-blue-600" />
                    </div>
                    <p className="text-gray-900 font-medium mb-2 text-lg">No channels connected</p>
                    <p className="text-gray-500 text-sm mb-6 text-center">Get started by connecting your social media accounts</p>
                    <Link
                      href="/connect/youtube"
                      className="flex items-center gap-2 px-4 py-2.5 bg-blue-600 text-white rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors duration-200 shadow-sm"
                      onClick={() => setIsChannelsDropdownOpen(false)}
                    >
                      <Plus size={16} />
                      Connect a Channel
                    </Link>
                  </div>
                )}
              </div>
            )}
          </div>

          <div ref={tagsDropdownRef} className="relative">
            <button
              onClick={() => setIsTagsDropdownOpen(!isTagsDropdownOpen)}
              className="flex items-center gap-2 px-3 py-2 text-sm text-gray-700 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors duration-200 shadow-sm"
            >
              <Tag size={16} className="text-gray-500" />
              <span>Tags</span>
              <ChevronDown size={16} className={`text-gray-500 transition-transform duration-200 ${isTagsDropdownOpen ? 'rotate-180' : ''}`} />
            </button>
            {isTagsDropdownOpen && (
              <div className="absolute top-full right-0 mt-2 w-[300px] bg-white rounded-lg shadow-lg border border-gray-200 p-5 z-10">
                <div className="mb-4">
                  <div className="relative">
                    <Search size={16} className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400" />
                    <input
                      type="text"
                      placeholder="Search tags..."
                      value={tagSearchQuery}
                      onChange={(e) => setTagSearchQuery(e.target.value)}
                      className="w-full pl-10 pr-4 py-2.5 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                    />
                  </div>
                </div>
                <div className="flex flex-col items-center justify-center py-8">
                  <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-4 shadow-inner">
                    <Tag size={28} className="text-blue-600" />
                  </div>
                  <p className="text-gray-900 font-medium mb-2 text-lg">No tags</p>
                  <p className="text-gray-500 text-sm text-center">Create a tag to organize your content</p>
                </div>
              </div>
            )}
          </div>

          <div className="relative" ref={timezoneDropdownRef}>
            <button 
              className="flex items-center gap-2 px-3 py-2 text-sm text-gray-700 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors duration-200 shadow-sm"
              onClick={() => setIsTimezoneOpen(!isTimezoneOpen)}
            >
              <MapPin size={16} className="text-gray-500" />
              <span>{selectedTimezone.name.split(',')[0]}</span>
              <ChevronDown size={16} className={`text-gray-500 transition-transform duration-200 ${isTimezoneOpen ? 'rotate-180' : ''}`} />
            </button>

            {isTimezoneOpen && (
              <div className="absolute top-full right-0 mt-2 w-[320px] bg-white border border-gray-200 rounded-lg shadow-lg max-h-80 overflow-y-auto z-10">
                {/* Search box */}
                <div className="sticky top-0 bg-white p-3 border-b border-gray-200">
                  <div className="relative">
                    <Search size={16} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                    <input
                      type="text"
                      placeholder="Search for a city or timezone..."
                      className="w-full pl-9 pr-9 py-2.5 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                      value={timezoneSearch}
                      onChange={(e) => setTimezoneSearch(e.target.value)}
                      autoFocus
                    />
                    
                    {timezoneSearch && (
                      <button 
                        className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors duration-200"
                        onClick={() => setTimezoneSearch('')}
                      >
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                        </svg>
                      </button>
                    )}
                  </div>
                </div>
                
                {/* Timezone options */}
                <div>
                  {timezoneData.map(({ region, timezones }) => {
                    // Filter timezones based on search
                    const filteredTimezones = timezones.filter(tz => 
                      tz.name.toLowerCase().includes(timezoneSearch.toLowerCase()) ||
                      tz.id.toLowerCase().includes(timezoneSearch.toLowerCase())
                    );
                    
                    if (filteredTimezones.length === 0) return null;
                    
                    return (
                      <div key={region}>
                        <div className="sticky top-[60px] bg-gray-100 px-3 py-1.5 text-xs font-medium text-gray-500 uppercase">
                          {region}
                        </div>
                        
                        {filteredTimezones.map((tz) => (
                          <div
                            key={tz.id}
                            className={`px-4 py-2.5 hover:bg-gray-50 cursor-pointer flex justify-between items-center transition-colors duration-200 ${
                              selectedTimezone.id === tz.id ? 'bg-blue-50' : ''
                            }`}
                            onClick={() => {
                              setSelectedTimezone(tz);
                              setIsTimezoneOpen(false);
                            }}
                          >
                            <div className="flex items-center">
                              <MapPin size={16} className="mr-2 text-gray-400" />
                              <span className="text-gray-700">{tz.name}</span>
                            </div>
                            <div className="flex items-center">
                              <span className="text-gray-500 text-sm mr-2">GMT{tz.offset >= 0 ? '+' : ''}{tz.offset}</span>
                              {selectedTimezone.id === tz.id && <Check size={16} className="text-blue-600" />}
                            </div>
                          </div>
                        ))}
                      </div>
                    );
                  })}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Main Content Area */}
      <div className="flex-1 flex flex-col h-[calc(100vh-170px)] p-6 bg-gray-50">
        {viewMode === 'list' ? (
          <>
            {mockPostsState[currentTab].length > 0 ? (
              <ListView 
                posts={mockPostsState[currentTab]} 
                onEdit={handleEditPost}
                onDuplicate={handleDuplicatePost}
                onDelete={handleDeletePost}
              />
            ) : (
              <div className="flex flex-col items-center justify-center py-16 bg-white rounded-xl border border-gray-200 shadow-sm">
                <div className="w-20 h-20 bg-gray-100 rounded-full flex items-center justify-center mb-6 shadow-inner">
                  {currentTab === 'queue' && <Clock size={36} className="text-gray-400" />}
                  {currentTab === 'drafts' && <FileEdit size={36} className="text-gray-400" />}
                  {currentTab === 'approvals' && <CheckCircle size={36} className="text-gray-400" />}
                  {currentTab === 'sent' && <Send size={36} className="text-gray-400" />}
                </div>
                <h2 className="text-2xl font-semibold text-gray-900 mb-3">
                  No {currentTab === 'queue' ? 'scheduled posts' : 
                     currentTab === 'drafts' ? 'draft posts' : 
                     currentTab === 'approvals' ? 'posts for approval' : 'sent posts'}
                </h2>
                <p className="text-gray-500 mb-8 text-center max-w-md">
                  {currentTab === 'queue' ? 'Schedule posts to see them here' : 
                   currentTab === 'drafts' ? 'Save drafts to see them here' : 
                   currentTab === 'approvals' ? 'Submit posts for approval to see them here' : 'Posts that have been sent will appear here'}
                </p>
                <button 
                  className="flex items-center gap-2 px-5 py-2.5 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200 shadow-sm"
                  onClick={() => openPostModal()}
                >
                  <Plus size={18} />
                  <span className="font-medium">
                    {currentTab === 'queue' ? 'Schedule a Post' : 
                     currentTab === 'drafts' ? 'Create a Draft' : 
                     currentTab === 'approvals' ? 'Submit for Approval' : 'Create a Post'}
                  </span>
                </button>
              </div>
            )}
          </>
        ) : (
          <div className="flex-1 overflow-hidden bg-white rounded-xl border border-gray-200 shadow-sm">
            <CalendarView 
              view={calendarViewMode} 
              onViewChange={handleCalendarViewChange}
              timezone={selectedTimezone.id}
              onAddPost={openPostModal}
              onEditPost={openEditModal}
              scheduledPosts={mockPostsState.queue.filter(post => post.status === 'scheduled')}
            />
          </div>
        )}
      </div>
    </div>
  );
} 