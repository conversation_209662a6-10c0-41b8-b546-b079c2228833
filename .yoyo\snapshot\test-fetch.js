// Use node-fetch for Node.js environment
const fetch = require('node-fetch');

// Test URL
const url = 'https://7861-2404-8ec0-4-2d98-a950-a216-4887-d919.ngrok-free.app/api/qstash-test';

console.log(`Testing endpoint: ${url}`);

// Use POST method
fetch(url, {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    test: 'fetch-test',
    timestamp: new Date().toISOString()
  })
})
.then(response => {
  console.log(`Status: ${response.status} ${response.statusText}`);
  console.log('Headers:');
  response.headers.forEach((value, name) => {
    console.log(`${name}: ${value}`);
  });
  return response.json();
})
.then(data => {
  console.log('Response data:');
  console.log(JSON.stringify(data, null, 2));
})
.catch(error => {
  console.error('Error:', error);
}); 