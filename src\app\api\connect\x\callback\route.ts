/**
 * X (Twitter) OAuth Callback Endpoint
 * 
 * Handles X OAuth 1.0a callback and completes authentication
 * GET /api/connect/x/callback
 */

import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { XAuthService } from '@/lib/services/xAuthService';
import { 
  with<PERSON><PERSON>r<PERSON><PERSON><PERSON>, 
  requireAuth,
  ErrorFactory
} from '@/lib/error-handler';

/**
 * Internal handler for X OAuth callback
 */
async function xOAuthCallbackHandler(req: NextRequest) {
  const { searchParams } = new URL(req.url);
  
  // Extract OAuth parameters from callback URL
  const oauthToken = searchParams.get('oauth_token');
  const oauthVerifier = searchParams.get('oauth_verifier');
  const state = searchParams.get('state');
  const denied = searchParams.get('denied');

  try {
    // Handle OAuth denial
    if (denied) {
      const frontendUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3002';
      const errorUrl = `${frontendUrl}/integrations?error=access_denied&description=${encodeURIComponent('User denied X authorization')}`;
      return NextResponse.redirect(errorUrl);
    }

    // Validate required parameters
    if (!oauthToken || !oauthVerifier || !state) {
      const frontendUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3002';
      const errorUrl = `${frontendUrl}/integrations?error=invalid_request&description=${encodeURIComponent('Missing required OAuth parameters')}`;
      return NextResponse.redirect(errorUrl);
    }

    // Authenticate user (this will validate the session)
    const { user } = await requireAuth(req);

    // Initialize X authentication service
    const supabase = createClient();
    const xAuthService = new XAuthService({
      supabase,
      supabaseAdmin: supabase
    });

    // Exchange OAuth verifier for access tokens
    const authResult = await xAuthService.handleCallback(oauthToken, oauthVerifier, state);

    if (!authResult.success) {
      const frontendUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3002';
      const errorMessage = authResult.error?.message || 'Authentication failed';
      const errorUrl = `${frontendUrl}/integrations?error=auth_failed&description=${encodeURIComponent(errorMessage)}`;
      return NextResponse.redirect(errorUrl);
    }

    const { accessToken, accessSecret, userInfo } = authResult.data!;

    // Store X account integration in platform_connections table
    const storeResult = await storeXIntegration(supabase, user.id, userInfo, accessToken, accessSecret);

    if (!storeResult.success) {
      const frontendUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3002';
      const errorUrl = `${frontendUrl}/integrations?error=store_failed&description=${encodeURIComponent('Failed to store X account integration')}`;
      return NextResponse.redirect(errorUrl);
    }

    // Redirect to success page
    const frontendUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3002';
    const successUrl = `${frontendUrl}/integrations?success=true&provider=x&account=${encodeURIComponent(userInfo.username)}`;
    return NextResponse.redirect(successUrl);

  } catch (error) {
    console.error('X OAuth callback error:', error);
    
    const frontendUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3002';
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    const errorUrl = `${frontendUrl}/integrations?error=callback_failed&description=${encodeURIComponent(errorMessage)}`;
    return NextResponse.redirect(errorUrl);
  }
}

/**
 * Store X account integration in platform_connections table
 */
async function storeXIntegration(
  supabase: any,
  userId: string,
  userInfo: any,
  accessToken: string,
  accessSecret: string
) {
  try {
    // Check if connection already exists
    const { data: existingConnection } = await supabase
      .from('connected_accounts')
      .select('id')
      .eq('user_id', userId)
      .eq('provider', 'x')
      .eq('provider_account_id', userInfo.id)
      .single();

    const connectionData = {
      user_id: userId,
      provider: 'x',
      service_type: 'social_media',
      provider_account_id: userInfo.id,
      account_username: userInfo.username,
      platform_account_name: userInfo.name,
      profile_picture_url: userInfo.profileImageUrl,
      access_token: accessToken, // Will be encrypted by database trigger
      access_token_secret: accessSecret, // Will be encrypted by database trigger
      refresh_token: null, // X OAuth 1.0a doesn't use refresh tokens
      expires_at: null, // X tokens don't expire
      scopes_granted: ['tweet.read', 'tweet.write', 'users.read'],
      status: 'active',
      last_sync_at: new Date().toISOString(),
      metadata: {
        verified: userInfo.verified,
        followers_count: userInfo.followersCount,
        following_count: userInfo.followingCount,
        tweet_count: userInfo.tweetCount
      }
    };

    let result;
    if (existingConnection) {
      // Update existing connection
      result = await supabase
        .from('connected_accounts')
        .update(connectionData)
        .eq('id', existingConnection.id);
    } else {
      // Create new connection
      result = await supabase
        .from('connected_accounts')
        .insert(connectionData);
    }

    if (result.error) {
      throw new Error(`Database error: ${result.error.message}`);
    }

    return { success: true };
  } catch (error) {
    console.error('Error storing X integration:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}

/**
 * GET handler for OAuth callback
 */
export const GET = withErrorHandler(xOAuthCallbackHandler);

/**
 * Handle unsupported methods
 */
export async function POST() {
  return NextResponse.json({
    success: false,
    error: {
      message: 'Method not allowed. OAuth callback should use GET.',
      code: 'METHOD_NOT_ALLOWED'
    }
  }, { status: 405 });
}

export async function PUT() {
  return NextResponse.json({
    success: false,
    error: {
      message: 'Method not allowed. OAuth callback should use GET.',
      code: 'METHOD_NOT_ALLOWED'
    }
  }, { status: 405 });
}

export async function DELETE() {
  return NextResponse.json({
    success: false,
    error: {
      message: 'Method not allowed. OAuth callback should use GET.',
      code: 'METHOD_NOT_ALLOWED'
    }
  }, { status: 405 });
}
