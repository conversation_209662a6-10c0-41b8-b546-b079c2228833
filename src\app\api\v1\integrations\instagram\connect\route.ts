/**
 * API v1 - Instagram Integration Connect Route
 * 
 * Initiates Instagram OAuth flow for business account integration
 */

import { NextRequest } from 'next/server';
import { InstagramService } from '@/lib/services/instagramService';
import { 
  withErrorHandler, 
  requireAuth
} from '@/lib/error-handler';
import { withVersioning } from '@/lib/versioning/middleware';

/**
 * Internal handler for Instagram OAuth initiation
 */
async function instagramConnectHandler(req: NextRequest) {
  // 1. Authenticate user
  const { user } = await requireAuth(req);

  // 2. Get redirect URI from environment or construct it
  const baseUrl = process.env.NEXTAUTH_URL || process.env.VERCEL_URL || 'http://localhost:3000';
  const redirectUri = `${baseUrl}/api/v1/integrations/instagram/callback`;

  // 3. Initialize Instagram service
  const { instagramService } = await import('@/lib/services');

  // 4. Generate OAuth authorization URL
  const result = await instagramService.generateAuthUrl(user.id, redirectUri);

  if (!result.success) {
    throw result.error;
  }

  // 5. Return authorization URL for client to redirect to
  return {
    success: true,
    data: {
      authUrl: result.data!.authUrl,
      state: result.data!.state,
      redirectUri
    },
    metadata: {
      provider: 'instagram',
      scopes: [
        'instagram_basic',
        'pages_show_list',
        'pages_read_engagement',
        'business_management',
        'instagram_content_publish',
        'instagram_manage_comments',
        'instagram_manage_insights'
      ]
    }
  };
}

/**
 * GET /api/v1/integrations/instagram/connect
 * Initiate Instagram OAuth flow
 */
export const GET = withVersioning(withErrorHandler(instagramConnectHandler));
