import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';
import { getTokenForService } from '@/lib/authUtils';

/**
 * API route to publish a post to social media platforms
 * This is triggered by QStash for immediate publishing (not scheduled)
 */
export async function POST(req: NextRequest) {
  try {
    // Get post ID and channel IDs from request body
    const { postId, channelIds = [] } = await req.json();
    
    if (!postId) {
      return NextResponse.json({ error: 'Missing post ID' }, { status: 400 });
    }
    
    console.log(`Publishing post ${postId} to channels:`, channelIds);
    
    // Get the post from the database
    const { data: post, error } = await supabase
      .from('posts')
      .select(`
        *,
        post_media (
          media_assets (*)
        ),
        post_channels (
          *,
          connected_accounts (*)
        )
      `)
      .eq('id', postId)
      .single();
    
    if (error || !post) {
      console.error(`Error retrieving post ${postId}:`, error);
      return NextResponse.json({ error: 'Post not found' }, { status: 404 });
    }
    
    // Update post status to processing
    await supabase
      .from('posts')
      .update({
        status: 'processing',
        processed_at: new Date().toISOString()
      })
      .eq('id', postId);
    
    // Filter channels if channelIds array is provided
    const channels = channelIds.length > 0
      ? post.post_channels.filter((ch: any) => channelIds.includes(ch.id))
      : post.post_channels;
    
    // Process each channel (social media platform)
    const results = [];
    
    for (const channel of channels || []) {
      try {
        // Update channel status to processing
        await supabase
          .from('post_channels')
          .update({
            status: 'processing'
          })
          .eq('id', channel.id);
        
        const connectedAccount = channel.connected_accounts;
        if (!connectedAccount) {
          throw new Error('No connected account found for channel');
        }
        
        const result = await publishToSocialPlatform(post, channel, connectedAccount);
        results.push({ channelId: channel.id, ...result });
        
        // Update channel status to published
        await supabase
          .from('post_channels')
          .update({
            status: 'published',
            published_at: new Date().toISOString(),
            platform_post_id: (result as any).platformPostId || null,
            platform_post_url: (result as any).platformPostUrl || null
          })
          .eq('id', channel.id);
      } catch (error) {
        console.error(`Error publishing to channel ${channel.id}:`, error);
        
        // Update channel status to failed
        await supabase
          .from('post_channels')
          .update({
            status: 'failed',
            error_message: error instanceof Error ? error.message : String(error)
          })
          .eq('id', channel.id);
        
        results.push({ 
          channelId: channel.id, 
          success: false, 
          error: error instanceof Error ? error.message : String(error) 
        });
      }
    }
    
    // Update post status based on channel results
    const allSucceeded = results.every(r => r.success !== false);
    await supabase
      .from('posts')
      .update({
        status: allSucceeded ? 'published' : 'partial',
        published_at: allSucceeded ? new Date().toISOString() : null
      })
      .eq('id', postId);
    
    return NextResponse.json({ success: true, results });
  } catch (error) {
    console.error('Error in publish-to-social worker:', error);
    return NextResponse.json({ 
      error: error instanceof Error ? error.message : String(error) 
    }, { status: 500 });
  }
}

/**
 * Publish to the appropriate social platform based on the connected account provider
 */
async function publishToSocialPlatform(post: any, channel: any, connectedAccount: any) {
  const platform = connectedAccount.provider;
  
  console.log(`Publishing post ${post.id} to ${platform} platform`);
  
  // Get media asset if available
  const mediaAsset = post.post_media?.[0]?.media_assets;
  
  switch (platform) {
    case 'youtube':
    case 'google_youtube':
      return await publishToYouTube(post, channel, connectedAccount, mediaAsset);
    
    // Add cases for other platforms like Twitter, Facebook, Instagram, etc.
    
    default:
      throw new Error(`Unsupported platform: ${platform}`);
  }
}

/**
 * Publish a post to YouTube
 */
async function publishToYouTube(post: any, channel: any, connectedAccount: any, mediaAsset: any) {
  if (!mediaAsset || !mediaAsset.file_id) {
    throw new Error('No media asset found for YouTube post');
  }
  
  // Ensure the media is a video type
  if (!mediaAsset.file_type?.startsWith('video/')) {
    throw new Error(`Unsupported media type for YouTube: ${mediaAsset.file_type}`);
  }
  
  // Get access token for YouTube
  const tokenData = await getTokenForService('youtube', connectedAccount.id);
  if (!tokenData || !tokenData.accessToken) {
    throw new Error('Could not retrieve valid YouTube access token');
  }
  
  // Get Drive access token - needed for accessing the Drive file
  const driveTokenData = await getTokenForService('google_drive');
  if (!driveTokenData || !driveTokenData.accessToken) {
    throw new Error('Could not retrieve valid Google Drive access token');
  }
  
  const driveFileId = mediaAsset.file_id;
  
  // YouTube-specific metadata
  const youtubePrivacy = channel.youtube_privacy || post.youtube_privacy || 'private';
  const youtubeTags = (post.tags || '')
    .split(',')
    .map((tag: string) => tag.trim())
    .filter((tag: string) => tag);
  
  console.log(`Uploading to YouTube with privacy '${youtubePrivacy}'`);
  
  // TODO: Implement Drive to YouTube upload
  // This functionality requires the googleapis library which is not available in the current build
  console.log('Drive to YouTube upload is not currently implemented');

  return {
    success: false,
    error: 'Drive to YouTube upload is not currently implemented'
  };
} 