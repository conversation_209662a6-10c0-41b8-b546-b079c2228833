import { NextRequest, NextResponse } from 'next/server';
import { supabaseAdmin } from '@/lib/supabase';
import { createClient } from '@/lib/supabase/server';

export async function DELETE(req: NextRequest) {
  try {
    // Check if supabaseAdmin is available
    if (!supabaseAdmin) {
      return NextResponse.json({ 
        success: false,
        error: 'Database connection not available' 
      }, { status: 500 });
    }
    
    // Get params from query params
    const { searchParams } = new URL(req.url);
    const accountId = searchParams.get('accountId');
    const channelId = searchParams.get('channelId');
    
    if (!accountId && !channelId) {
      return NextResponse.json({ 
        success: false,
        error: 'Either accountId or channelId is required' 
      }, { status: 400 });
    }
    
    // Get user ID from session
    const supabase = createClient();
    const { data: { session } } = await supabase.auth.getSession();
    
    if (!session) {
      return NextResponse.json({ 
        success: false,
        error: 'Authentication required' 
      }, { status: 401 });
    }
    
    const userId = session.user.id;

    // First, check if we have a YouTube connection in the youtube_tokens table
    const { data: youtubeToken, error: youtubeTokenError } = await supabaseAdmin
      .from('youtube_tokens')
      .select('*')
      .eq('user_id', userId)
      .maybeSingle();

    if (youtubeTokenError) {
      console.error('Error checking youtube_tokens:', youtubeTokenError);
    }

    // If we have a YouTube token, handle removal from youtube_tokens table
    if (youtubeToken) {
      console.log('Found YouTube token, removing from youtube_tokens table');

      try {
        // Delete from youtube_tokens table
        const { error: deleteYoutubeTokenError } = await supabaseAdmin
          .from('youtube_tokens')
          .delete()
          .eq('user_id', userId);

        if (deleteYoutubeTokenError) {
          throw new Error(`Failed to delete from youtube_tokens: ${deleteYoutubeTokenError.message}`);
        }

        // Also clean up any related data in connected_accounts if it exists
        const { error: deleteConnectedAccountError } = await supabaseAdmin
          .from('connected_accounts')
          .delete()
          .eq('user_id', userId)
          .eq('provider', 'google')
          .in('service_type', ['youtube', 'youtube_test']);

        if (deleteConnectedAccountError) {
          console.warn('Error deleting from connected_accounts:', deleteConnectedAccountError);
        }

        // Clean up any post_channels entries for this user
        const { error: deletePostChannelsError } = await supabaseAdmin
          .from('post_channels')
          .delete()
          .eq('user_id', userId);

        if (deletePostChannelsError) {
          console.warn('Error deleting from post_channels:', deletePostChannelsError);
        }

        return NextResponse.json({
          success: true,
          message: 'YouTube channel removed successfully',
          removed_from: ['youtube_tokens', 'connected_accounts', 'post_channels']
        });
      } catch (error) {
        console.error('Error removing YouTube token:', error);
        return NextResponse.json({
          success: false,
          error: error instanceof Error ? error.message : String(error)
        }, { status: 500 });
      }
    }

    // If no YouTube token found, fall back to the original logic for connected_accounts
    // If we have accountId, use it directly
    let targetAccountId = accountId;
    
    // If accountId is not provided but channelId is, try to find the accountId
    if (!targetAccountId && channelId) {
      console.log(`Looking for account matching channelId: ${channelId} and userId: ${userId}`);
      
      // First try connected_accounts table
      let { data: accounts } = await supabaseAdmin
        .from('connected_accounts')
        .select('*')
        .eq('user_id', userId)
        .or('service_type.eq.youtube,provider.eq.google_youtube');
      
      if (!accounts || accounts.length === 0) {
        // Try general google accounts with youtube scope
        const { data: googleAccounts } = await supabaseAdmin
          .from('connected_accounts')
          .select('*')
          .eq('user_id', userId)
          .eq('provider', 'google');
          
        if (googleAccounts && googleAccounts.length > 0) {
          accounts = googleAccounts.filter(account => 
            account.scope && (
              account.scope.includes('youtube') || 
              account.scope.includes('googleapis.com/auth/youtube')
            )
          );
        }
      }
      
      if (accounts && accounts.length > 0) {
        console.log(`Found ${accounts.length} matching accounts`);
        
        // For safety, we'll only remove the first matching account
        // In a real app, you'd need a better way to match the specific channel
        targetAccountId = accounts[0].id;
        console.log(`Using accountId: ${targetAccountId}`);
      } else {
        return NextResponse.json({ 
          success: false,
          error: 'No matching YouTube account found for this channel' 
        }, { status: 404 });
      }
    }
    
    console.log(`API: Removing YouTube channel with account ID: ${targetAccountId}`);
    
    // Verify the account exists and belongs to the user
    const { data: account, error: accountError } = await supabaseAdmin
      .from('connected_accounts')
      .select('*')
      .eq('id', targetAccountId)
      .eq('user_id', userId)
      .maybeSingle();
      
    if (accountError) {
      console.error('Error fetching account:', accountError);
      return NextResponse.json({ 
        success: false,
        error: 'Failed to fetch account' 
      }, { status: 500 });
    }
    
    if (!account) {
      return NextResponse.json({ 
        success: false,
        error: 'Account not found or does not belong to you' 
      }, { status: 404 });
    }
    
    console.log('Account found:', {
      id: account.id,
      provider: account.provider,
      service_type: account.service_type,
      token_id: account.token_id
    });
    
    // Begin transaction to delete from all related tables
    const results: {
      connected_accounts: any[] | null;
      google_oauth_tokens: any[] | null;
      youtube_tokens: any[] | null;
      youtube_connections: any[] | null;
      post_channels: any[] | null;
    } = {
      connected_accounts: null,
      google_oauth_tokens: null,
      youtube_tokens: null,
      youtube_connections: null,
      post_channels: null,
    };
    
    // Step 1: Get the token_id from the connected account
    const tokenId = account.token_id;
    
    try {
      // Step 2: Delete from connected_accounts table
      const { data: deletedAccount, error: deleteAccountError } = await supabaseAdmin
        .from('connected_accounts')
        .delete()
        .eq('id', targetAccountId)
        .select();
      
      if (deleteAccountError) {
        throw new Error(`Failed to delete from connected_accounts: ${deleteAccountError.message}`);
      }
      
      results.connected_accounts = deletedAccount;
      console.log('Successfully deleted from connected_accounts:', deletedAccount);
      
      // Step 3: If there's a token_id, also delete from google_oauth_tokens
      if (tokenId) {
        const { data: deletedToken, error: deleteTokenError } = await supabaseAdmin
          .from('google_oauth_tokens')
          .delete()
          .eq('id', tokenId)
          .select();
        
        if (deleteTokenError) {
          console.warn(`Error deleting from google_oauth_tokens: ${deleteTokenError.message}`);
        } else {
          results.google_oauth_tokens = deletedToken;
          console.log('Successfully deleted from google_oauth_tokens:', deletedToken);
        }
      }
      
      // Step 4: Delete from the new youtube_connections table
      const channelIdToDelete = account.platform_account_id || channelId;
      if (channelIdToDelete) {
        const { data: deletedYoutubeConnection, error: deleteYoutubeConnectionError } = await supabaseAdmin
          .from('youtube_connections')
          .delete()
          .eq('user_id', userId)
          .eq('youtube_channel_id', channelIdToDelete)
          .select();

        if (deleteYoutubeConnectionError) {
          console.warn(`Error deleting from youtube_connections: ${deleteYoutubeConnectionError.message}`);
        } else {
          results.youtube_connections = deletedYoutubeConnection;
          console.log('Successfully deleted from youtube_connections:', deletedYoutubeConnection);
        }
      }

      // Step 5: Also try to delete from youtube_tokens table (legacy)
      const { data: deletedYoutubeToken, error: deleteYoutubeTokenError } = await supabaseAdmin
        .from('youtube_tokens')
        .delete()
        .eq('user_id', userId)
        .select();

      if (deleteYoutubeTokenError) {
        console.warn(`Error deleting from youtube_tokens: ${deleteYoutubeTokenError.message}`);
      } else {
        results.youtube_tokens = deletedYoutubeToken;
        console.log('Successfully deleted from youtube_tokens:', deletedYoutubeToken);
      }
      
      // Step 6: Delete any post_channels entries for this account
      const { data: deletedPostChannels, error: deletePostChannelsError } = await supabaseAdmin
        .from('post_channels')
        .delete()
        .eq('account_id', targetAccountId)
        .select();
      
      if (deletePostChannelsError) {
        console.warn(`Error deleting from post_channels: ${deletePostChannelsError.message}`);
      } else {
        results.post_channels = deletedPostChannels;
        console.log('Successfully deleted from post_channels:', deletedPostChannels);
      }
      
      return NextResponse.json({ 
        success: true,
        message: 'YouTube channel successfully removed from all tables',
        data: results
      });
    } catch (error) {
      console.error('Error in transaction:', error);
      return NextResponse.json({ 
        success: false,
        error: error instanceof Error ? error.message : String(error) 
      }, { status: 500 });
    }
  } catch (error) {
    console.error('Error in remove-channel route:', error);
    return NextResponse.json({ 
      success: false,
      error: error instanceof Error ? error.message : String(error) 
    }, { status: 500 });
  }
} 