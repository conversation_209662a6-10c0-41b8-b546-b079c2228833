require('dotenv').config();
const https = require('https');

// Load token directly from file
const fs = require('fs');
const path = require('path');
const envContent = fs.readFileSync(path.resolve('.env'), 'utf8');

// Get token
const tokenMatch = envContent.match(/QSTASH_TOKEN="(.+)"/);
if (!tokenMatch) {
  console.error("Failed to find QSTASH_TOKEN in .env file");
  process.exit(1);
}
const qstashToken = tokenMatch[1];
console.log(`Using token (first 10 chars): ${qstashToken.substring(0, 10)}...`);

// Set destination URL
const destinationUrl = encodeURIComponent("https://7861-2404-8ec0-4-2d98-a950-a216-4887-d919.ngrok-free.app/api/posts/publish-callback");
console.log(`Raw destination URL: https://7861-2404-8ec0-4-2d98-a950-a216-4887-d919.ngrok-free.app/api/posts/publish-callback`);
console.log(`Encoded destination URL: ${destinationUrl}`);

// Create payload
const postData = JSON.stringify({
  postId: 'test-post-id',
  timestamp: new Date().toISOString()
});

// Create request options - use the URL as part of the path
const options = {
  hostname: 'qstash.upstash.io',
  port: 443,
  path: `/v2/publish/${destinationUrl}`,
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${qstashToken}`
  }
};

console.log(`Request URL: https://${options.hostname}${options.path}`);
console.log("Request headers:");
Object.keys(options.headers).forEach(key => {
  const value = key === 'Authorization' 
    ? 'Bearer [REDACTED]' 
    : options.headers[key];
  console.log(`- ${key}: ${value}`);
});

// Make request
const req = https.request(options, (res) => {
  console.log(`Status Code: ${res.statusCode}`);
  
  let data = '';
  res.on('data', (chunk) => {
    data += chunk;
  });
  
  res.on('end', () => {
    console.log(`Response: ${data}`);
    try {
      const jsonResponse = JSON.parse(data);
      console.log('Parsed JSON response:');
      console.log(JSON.stringify(jsonResponse, null, 2));
    } catch (e) {
      console.log('Response is not valid JSON');
    }
  });
});

req.on('error', (e) => {
  console.error(`Error: ${e.message}`);
});

// Send request
req.write(postData);
req.end(); 