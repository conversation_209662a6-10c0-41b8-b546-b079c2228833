import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { AutoReplyService } from '@/lib/services/autoReplyService';
import { verifySignatureAppRouter } from '@upstash/qstash/nextjs';

// Initialize Supabase client for server-side operations
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

// Initialize auto-reply service
const autoReplyService = new AutoReplyService(supabase);

interface CommentProcessingJob {
  videoId: string;
  connectionId: string;
  userId: string;
  forceProcess?: boolean;
}

/**
 * QStash Worker: Process Comments for Auto-Reply
 * This worker fetches comments for a video and processes them for auto-reply
 */
async function handler(request: NextRequest) {
  try {
    console.log('Comment processing worker started');

    // Parse the job data
    const body: CommentProcessingJob = await request.json();
    const { videoId, connectionId, userId, forceProcess = false } = body;

    if (!videoId || !connectionId || !userId) {
      console.error('Missing required parameters:', { videoId, connectionId, userId });
      return NextResponse.json(
        { error: 'Missing required parameters: videoId, connectionId, userId' },
        { status: 400 }
      );
    }

    console.log(`Processing comments for video ${videoId}, connection ${connectionId}, user ${userId}`);

    // Verify the connection belongs to the user
    const { data: connection, error: connectionError } = await supabase
      .from('connected_accounts')
      .select('id, provider, auto_reply_enabled')
      .eq('id', connectionId)
      .eq('user_id', userId)
      .single();

    if (connectionError || !connection) {
      console.error('Connection not found or access denied:', connectionError);
      return NextResponse.json(
        { error: 'Connection not found or access denied' },
        { status: 404 }
      );
    }

    // Check if auto-reply is enabled for this connection
    if (!connection.auto_reply_enabled && !forceProcess) {
      console.log(`Auto-reply disabled for connection ${connectionId}, skipping processing`);
      return NextResponse.json({
        success: true,
        message: 'Auto-reply disabled for this connection',
        processed: 0
      });
    }

    // Fetch comments from YouTube API (using existing endpoint)
    const commentsResponse = await fetch(`${process.env.NEXT_PUBLIC_APP_URL}/api/youtube/comments?videoId=${videoId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        // Note: This internal call doesn't include user session, so we'll need to handle auth differently
      }
    });

    if (!commentsResponse.ok) {
      console.error('Failed to fetch comments from YouTube API');
      return NextResponse.json(
        { error: 'Failed to fetch comments' },
        { status: 500 }
      );
    }

    const commentsData = await commentsResponse.json();
    const comments = commentsData.comments || [];

    console.log(`Fetched ${comments.length} comments for video ${videoId}`);

    // Process each comment for auto-reply
    let processedCount = 0;
    const errors: string[] = [];

    for (const comment of comments) {
      try {
        // Transform comment data to match our service interface
        const commentForProcessing = {
          id: comment.id,
          text: comment.text,
          author_name: comment.authorDisplayName,
          author_channel_id: comment.authorChannelId,
          video_id: videoId,
          published_at: comment.publishedAt,
          connection_id: connectionId,
          platform: 'youtube'
        };

        // Process comment for auto-reply (this is async and won't block)
        await autoReplyService.processCommentForAutoReply(commentForProcessing);
        processedCount++;

      } catch (error) {
        const errorMessage = `Failed to process comment ${comment.id}: ${error instanceof Error ? error.message : 'Unknown error'}`;
        console.error(errorMessage);
        errors.push(errorMessage);
      }
    }

    console.log(`Comment processing completed. Processed: ${processedCount}, Errors: ${errors.length}`);

    return NextResponse.json({
      success: true,
      message: 'Comment processing completed',
      processed: processedCount,
      total: comments.length,
      errors: errors.length > 0 ? errors : undefined
    });

  } catch (error) {
    console.error('Comment processing worker error:', error);
    
    return NextResponse.json(
      { 
        error: 'Comment processing failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// Export the handler with QStash signature verification
export const POST = verifySignatureAppRouter(handler);

/**
 * Health check endpoint for the worker
 */
export async function GET() {
  return NextResponse.json({
    status: 'healthy',
    worker: 'process-comments',
    timestamp: new Date().toISOString()
  });
}

// Note: Utility functions should be moved to a separate file, not exported from route handlers
// This function has been moved to avoid Next.js build errors
