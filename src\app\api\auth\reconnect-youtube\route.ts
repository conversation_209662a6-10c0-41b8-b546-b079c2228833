import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

/**
 * Endpoint to initiate the YouTube reconnection flow using Supabase OAuth
 */
export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const accountId = searchParams.get('accountId');
    
    if (!accountId) {
      return NextResponse.json({
        success: false,
        error: 'Account ID is required'
      }, { status: 400 });
    }
    
    // Initialize Supabase client
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
    );
    
    // Generate state parameter containing the account ID for reconnection
    const stateData = {
      accountId: accountId,
      reconnect: true,
      timestamp: Date.now()
    };
    
    const state = Buffer.from(JSON.stringify(stateData)).toString('base64');
    
    // Get the sign in URL with provider-specific options
    const { data, error } = await supabase.auth.signInWithOAuth({
      provider: 'google',
      options: {
        redirectTo: `${process.env.NEXT_PUBLIC_APP_URL}/auth/callback`,
        queryParams: {
          access_type: 'offline',
          prompt: 'consent',
          scope: [
            'https://www.googleapis.com/auth/youtube.readonly',
            'https://www.googleapis.com/auth/youtube.upload',
            'https://www.googleapis.com/auth/youtube.force-ssl',
            'https://www.googleapis.com/auth/youtube',
            'https://www.googleapis.com/auth/userinfo.email',
            'https://www.googleapis.com/auth/userinfo.profile',
            'https://www.googleapis.com/auth/yt-analytics.readonly',
            'https://www.googleapis.com/auth/yt-analytics-monetary.readonly'
          ].join(' '),
          youtube_reconnect: 'true',
          state: state
        }
      }
    });
    
    if (error) {
      throw error;
    }
    
    if (!data.url) {
      throw new Error('No OAuth URL returned from Supabase');
    }
    
    console.log('Redirecting to Supabase OAuth for YouTube reconnection');
    
    // Redirect the user to Supabase's OAuth flow
    return NextResponse.redirect(data.url);
    
  } catch (error) {
    console.error('Error in reconnect-youtube route:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
} 