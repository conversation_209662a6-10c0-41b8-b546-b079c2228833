import { Metada<PERSON> } from 'next';
import Link from 'next/link';
import { Calendar, Clock, User, ArrowRight } from 'lucide-react';

export const metadata: Metadata = {
  title: 'Blog - Social Media Tips & Insights | Postbee',
  description: 'Discover the latest social media strategies, tips, and insights to grow your online presence and engage your audience effectively.',
};

// Mock blog posts for now
const mockPosts = [
  {
    id: '1',
    title: 'How to Create Engaging Instagram Content That Converts',
    slug: 'engaging-instagram-content-converts',
    description: 'Learn the secrets to creating Instagram content that not only engages your audience but also drives real business results.',
    featured_image: '/images/blog/instagram-content.jpg',
    published_at: '2024-01-15T10:00:00Z',
    reading_time: 8,
    author_name: '<PERSON>',
    tags: ['Instagram', 'Content Creation', 'Engagement'],
  },
  {
    id: '2',
    title: 'The Ultimate Guide to Facebook Marketing in 2024',
    slug: 'ultimate-facebook-marketing-guide-2024',
    description: 'Everything you need to know about Facebook marketing, from organic reach to paid advertising strategies.',
    featured_image: '/images/blog/facebook-marketing.jpg',
    published_at: '2024-01-12T14:30:00Z',
    reading_time: 12,
    author_name: '<PERSON>',
    tags: ['Facebook', 'Marketing', 'Strategy'],
  },
  {
    id: '3',
    title: 'YouTube Analytics: Understanding Your Audience',
    slug: 'youtube-analytics-understanding-audience',
    description: 'Dive deep into YouTube analytics to understand your audience better and create content that resonates.',
    featured_image: '/images/blog/youtube-analytics.jpg',
    published_at: '2024-01-10T09:15:00Z',
    reading_time: 10,
    author_name: 'Emily Rodriguez',
    tags: ['YouTube', 'Analytics', 'Audience'],
  },
];

const mockCategories = [
  { id: '1', name: 'Instagram Tips', slug: 'instagram-tips' },
  { id: '2', name: 'Facebook Marketing', slug: 'facebook-marketing' },
  { id: '3', name: 'YouTube Strategy', slug: 'youtube-strategy' },
  { id: '4', name: 'Content Creation', slug: 'content-creation' },
  { id: '5', name: 'Social Media Analytics', slug: 'social-media-analytics' },
];

function formatDate(dateString: string) {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });
}

function BlogPostCard({ post }: { post: any }) {
  return (
    <article className="group bg-white rounded-xl border border-gray-200 overflow-hidden hover:shadow-lg transition-all duration-300">
      {/* Featured Image */}
      {post.featured_image && (
        <div className="aspect-video overflow-hidden bg-gray-200">
          <div className="w-full h-full flex items-center justify-center text-gray-500">
            📷 Featured Image
          </div>
        </div>
      )}
      
      <div className="p-6">
        {/* Tags */}
        {post.tags && post.tags.length > 0 && (
          <div className="flex flex-wrap gap-2 mb-3">
            {post.tags.slice(0, 2).map((tag: string, index: number) => (
              <span
                key={index}
                className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
              >
                {tag}
              </span>
            ))}
          </div>
        )}

        {/* Title */}
        <h2 className="text-xl font-bold text-gray-900 mb-3 group-hover:text-blue-600 transition-colors line-clamp-2">
          <Link href={`/landing/blog/${post.slug}`}>
            {post.title}
          </Link>
        </h2>

        {/* Description */}
        {post.description && (
          <p className="text-gray-600 mb-4 line-clamp-3">
            {post.description}
          </p>
        )}

        {/* Meta Information */}
        <div className="flex items-center justify-between text-sm text-gray-500">
          <div className="flex items-center space-x-4">
            {post.author_name && (
              <div className="flex items-center space-x-1">
                <User className="w-4 h-4" />
                <span>{post.author_name}</span>
              </div>
            )}
            <div className="flex items-center space-x-1">
              <Calendar className="w-4 h-4" />
              <span>{formatDate(post.published_at)}</span>
            </div>
            <div className="flex items-center space-x-1">
              <Clock className="w-4 h-4" />
              <span>{post.reading_time} min read</span>
            </div>
          </div>
          
          <Link
            href={`/landing/blog/${post.slug}`}
            className="inline-flex items-center text-blue-600 hover:text-blue-700 font-medium"
          >
            Read more
            <ArrowRight className="w-4 h-4 ml-1" />
          </Link>
        </div>
      </div>
    </article>
  );
}

export default function BlogPage() {
  const posts = mockPosts;
  const categories = mockCategories;

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Social Media Insights & Tips
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Discover the latest strategies, tips, and insights to grow your social media presence and engage your audience effectively.
            </p>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col lg:flex-row gap-12">
            {/* Blog Posts */}
            <div className="flex-1">
              {posts.length > 0 ? (
                <>
                  {/* Featured Post */}
                  {posts[0] && (
                    <div className="mb-12">
                      <h2 className="text-2xl font-bold text-gray-900 mb-6">Featured Post</h2>
                      <article className="bg-white rounded-xl border border-gray-200 overflow-hidden shadow-lg">
                        {posts[0].featured_image && (
                          <div className="aspect-video overflow-hidden bg-gray-200">
                            <div className="w-full h-full flex items-center justify-center text-gray-500">
                              📷 Featured Image
                            </div>
                          </div>
                        )}
                        <div className="p-8">
                          <h3 className="text-3xl font-bold text-gray-900 mb-4">
                            <Link href={`/landing/blog/${posts[0].slug}`} className="hover:text-blue-600 transition-colors">
                              {posts[0].title}
                            </Link>
                          </h3>
                          {posts[0].description && (
                            <p className="text-lg text-gray-600 mb-6">
                              {posts[0].description}
                            </p>
                          )}
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-4 text-sm text-gray-500">
                              {posts[0].author_name && (
                                <div className="flex items-center space-x-1">
                                  <User className="w-4 h-4" />
                                  <span>{posts[0].author_name}</span>
                                </div>
                              )}
                              <div className="flex items-center space-x-1">
                                <Calendar className="w-4 h-4" />
                                <span>{formatDate(posts[0].published_at)}</span>
                              </div>
                              <div className="flex items-center space-x-1">
                                <Clock className="w-4 h-4" />
                                <span>{posts[0].reading_time} min read</span>
                              </div>
                            </div>
                            <Link
                              href={`/landing/blog/${posts[0].slug}`}
                              className="inline-flex items-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors"
                            >
                              Read Full Article
                              <ArrowRight className="w-4 h-4 ml-2" />
                            </Link>
                          </div>
                        </div>
                      </article>
                    </div>
                  )}

                  {/* Recent Posts Grid */}
                  <div>
                    <h2 className="text-2xl font-bold text-gray-900 mb-6">Recent Posts</h2>
                    <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                      {posts.slice(1).map((post) => (
                        <BlogPostCard key={post.id} post={post} />
                      ))}
                    </div>
                  </div>
                </>
              ) : (
                <div className="text-center py-16">
                  <h2 className="text-2xl font-bold text-gray-900 mb-4">No posts yet</h2>
                  <p className="text-gray-600">
                    We're working on creating amazing content for you. Check back soon!
                  </p>
                </div>
              )}
            </div>

            {/* Sidebar */}
            <aside className="lg:w-80">
              <div className="space-y-8">
                {/* Categories */}
                {categories.length > 0 && (
                  <div className="bg-white rounded-xl border border-gray-200 p-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Categories</h3>
                    <div className="space-y-2">
                      {categories.map((category) => (
                        <Link
                          key={category.id}
                          href={`/landing/blog?category=${category.slug}`}
                          className="block px-3 py-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                        >
                          {category.name}
                        </Link>
                      ))}
                    </div>
                  </div>
                )}

                {/* Newsletter Signup */}
                <div className="bg-blue-50 rounded-xl border border-blue-200 p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">Stay Updated</h3>
                  <p className="text-gray-600 text-sm mb-4">
                    Get the latest social media tips and insights delivered to your inbox.
                  </p>
                  <form className="space-y-3">
                    <input
                      type="email"
                      placeholder="Enter your email"
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                    <button
                      type="submit"
                      className="w-full px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors"
                    >
                      Subscribe
                    </button>
                  </form>
                </div>
              </div>
            </aside>
          </div>
        </div>
      </section>
    </div>
  );
}
