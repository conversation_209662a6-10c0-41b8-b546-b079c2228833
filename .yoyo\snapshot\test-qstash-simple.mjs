// Simple QStash test using ES modules
import { promises as fs } from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __dirname = path.dirname(fileURLToPath(import.meta.url));

async function main() {
  try {
    // Read .env file directly
    const envPath = path.resolve(__dirname, '.env');
    const envContent = await fs.readFile(envPath, 'utf-8');
    
    // Extract QStash token
    const tokenMatch = envContent.match(/QSTASH_TOKEN="(.+)"/);
    if (!tokenMatch) {
      console.error("Failed to find QSTASH_TOKEN in .env file");
      return;
    }
    const qstashToken = tokenMatch[1];
    console.log(`Found QStash token: ${qstashToken.substring(0, 5)}...`);
    
    // Define destination URL
    const destinationUrl = "https://7861-2404-8ec0-4-2d98-a950-a216-4887-d919.ngrok-free.app/api/qstash-test";
    console.log(`Using destination URL: ${destinationUrl}`);
    
    // Create message payload
    const payload = {
      postId: 'simple-test-esm',
      timestamp: new Date().toISOString()
    };
    
    // Send request to QStash using fetch
    console.log('Sending request to QStash...');
    const response = await fetch('https://qstash.upstash.io/v2/publish', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${qstashToken}`,
        'Upstash-Forward-Url': destinationUrl
      },
      body: JSON.stringify(payload)
    });
    
    // Log response
    console.log(`Response status: ${response.status} ${response.statusText}`);
    console.log('Response headers:');
    response.headers.forEach((value, name) => {
      console.log(`${name}: ${value}`);
    });
    
    // Parse response body
    const responseBody = await response.text();
    console.log(`Response body: ${responseBody}`);
    try {
      const jsonData = JSON.parse(responseBody);
      console.log('Parsed JSON response:');
      console.log(JSON.stringify(jsonData, null, 2));
    } catch (e) {
      console.log('Response is not valid JSON');
    }
  } catch (error) {
    console.error('Error:', error);
  }
}

main(); 