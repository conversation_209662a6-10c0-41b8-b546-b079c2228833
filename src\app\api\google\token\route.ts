import { NextResponse } from 'next/server';
import { OAuth2Client } from 'google-auth-library';
import { supabaseAdmin } from '@/lib/supabase';

// Google OAuth configuration
const GOOGLE_CLIENT_ID = process.env.GOOGLE_CLIENT_ID || '';
const GOOGLE_CLIENT_SECRET = process.env.GOOGLE_CLIENT_SECRET || '';

export async function GET() {
  try {
    // Get the current authenticated user
    const { data: { user } } = await supabaseAdmin.auth.getUser();
    
    if (!user) {
      return NextResponse.json(
        { error: 'User not authenticated' },
        { status: 401 }
      );
    }

    // Get the Google account from Supabase
    const { data: account, error } = await supabaseAdmin
      .from('connected_accounts')
      .select('id, access_token, refresh_token, expires_at')
      .eq('user_id', user.id)
      .eq('provider', 'google')
      .single();

    if (error || !account) {
      return NextResponse.json(
        { error: 'No Google account connected' },
        { status: 401 }
      );
    }

    // Check if token is expired and needs refresh
    const isTokenExpired = account.expires_at && new Date(account.expires_at) <= new Date();
    
    if (isTokenExpired && account.refresh_token) {
      try {
        // Initialize OAuth client
        const oauth2Client = new OAuth2Client(
          GOOGLE_CLIENT_ID,
          GOOGLE_CLIENT_SECRET
        );

        // Set refresh token
        oauth2Client.setCredentials({
          refresh_token: account.refresh_token
        });

        // Refresh the token
        const { credentials } = await oauth2Client.refreshAccessToken();
        
        // Update the token in the database
        const expiresAt = credentials.expiry_date 
          ? new Date(credentials.expiry_date).toISOString()
          : new Date(Date.now() + 3600 * 1000).toISOString();
        
        await supabaseAdmin
          .from('connected_accounts')
          .update({
            access_token: credentials.access_token,
            refresh_token: credentials.refresh_token || account.refresh_token,
            expires_at: expiresAt,
            updated_at: new Date().toISOString()
          })
          .eq('id', account.id);
        
        // Return the new access token
        return NextResponse.json({ 
          access_token: credentials.access_token,
          developer_key: process.env.GOOGLE_API_KEY
        });
      } catch (refreshError) {
        console.error('Error refreshing token:', refreshError);
        return NextResponse.json(
          { error: 'Failed to refresh token' },
          { status: 401 }
        );
      }
    }

    // Return the current access token
    return NextResponse.json({ 
      access_token: account.access_token,
      developer_key: process.env.GOOGLE_API_KEY
    });
  } catch (error) {
    console.error('Error retrieving Google token:', error);
    return NextResponse.json(
      { error: 'Error retrieving Google token' },
      { status: 500 }
    );
  }
} 