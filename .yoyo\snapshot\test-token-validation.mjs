import { promises as fs } from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __dirname = path.dirname(fileURLToPath(import.meta.url));

async function main() {
  try {
    // Read .env file
    const envPath = path.resolve(__dirname, '.env');
    const envContent = await fs.readFile(envPath, 'utf-8');
    
    // Extract QStash token
    const tokenMatch = envContent.match(/QSTASH_TOKEN="(.+)"/);
    if (!tokenMatch) {
      console.error("Failed to find QSTASH_TOKEN in .env file");
      return;
    }
    const qstashToken = tokenMatch[1];
    
    console.log("Token analysis:");
    console.log(`- Length: ${qstashToken.length} characters`);
    console.log(`- First 10 chars: ${qstashToken.substring(0, 10)}...`);
    console.log(`- Last 10 chars: ...${qstashToken.substring(qstashToken.length - 10)}`);
    
    // Check if it's base64 encoded
    const isBase64 = /^[A-Za-z0-9+/=]+$/.test(qstashToken);
    console.log(`- Appears to be Base64: ${isBase64}`);
    
    // Attempt to decode if it's base64
    if (isBase64) {
      try {
        // In Node.js, buffers have a toString('base64') method to convert to base64
        // and Buffer.from(str, 'base64') to decode from base64
        const decodedBuffer = Buffer.from(qstashToken, 'base64');
        const decodedText = decodedBuffer.toString('utf-8');
        
        // If we can parse it as JSON, it's likely a structured token
        try {
          const jsonData = JSON.parse(decodedText);
          console.log("- Base64 decodes to valid JSON:");
          console.log(JSON.stringify(jsonData, null, 2));
        } catch (e) {
          console.log(`- Base64 decodes to text: ${decodedText.substring(0, 30)}...`);
        }
      } catch (e) {
        console.log("- Failed to decode as Base64");
      }
    }
    
    // Make a simple test request to the QStash API to check token validity
    console.log("\nTesting token with QStash API...");
    const response = await fetch('https://qstash.upstash.io/v2/topics', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${qstashToken}`
      }
    });
    
    console.log(`API response status: ${response.status} ${response.statusText}`);
    const responseBody = await response.text();
    
    if (response.status === 200) {
      console.log("✅ Token is valid! You can access the QStash API.");
      console.log("Response:", responseBody);
    } else if (response.status === 401 || response.status === 403) {
      console.log("❌ Token is invalid or unauthorized.");
      console.log("Response:", responseBody);
      console.log("\nTroubleshooting steps:");
      console.log("1. Verify you have the correct token from your Upstash QStash console");
      console.log("2. Check if your QStash account is active and not in a trial period");
      console.log("3. Ensure billing information is up to date if required");
    } else {
      console.log("⚠️ Unexpected response from QStash API");
      console.log("Response:", responseBody);
    }
  } catch (error) {
    console.error('Error:', error);
  }
}

main(); 