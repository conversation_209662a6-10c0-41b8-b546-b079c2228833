/**
 * API v1 - Instagram Integration Callback Route
 * 
 * Handles Instagram OAuth callback and completes integration
 */

import { NextRequest, NextResponse } from 'next/server';
import { InstagramService } from '@/lib/services/instagramService';
import { 
  withE<PERSON><PERSON><PERSON><PERSON><PERSON>, 
  requireA<PERSON>,
  ErrorFactory
} from '@/lib/error-handler';
import { withVersioning } from '@/lib/versioning/middleware';

/**
 * Internal handler for Instagram OAuth callback
 */
async function instagramCallbackHandler(req: NextRequest) {
  const url = new URL(req.url);
  const code = url.searchParams.get('code');
  const state = url.searchParams.get('state');
  const error = url.searchParams.get('error');
  const errorDescription = url.searchParams.get('error_description');

  // 1. Check for OAuth errors
  if (error) {
    const frontendUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';
    const errorUrl = `${frontendUrl}/integrations?error=${encodeURIComponent(error)}&description=${encodeURIComponent(errorDescription || 'Instagram OAuth failed')}`;
    return NextResponse.redirect(errorUrl);
  }

  // 2. Validate required parameters
  if (!code || !state) {
    const frontendUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';
    const errorUrl = `${frontendUrl}/integrations?error=invalid_request&description=${encodeURIComponent('Missing authorization code or state')}`;
    return NextResponse.redirect(errorUrl);
  }

  try {
    // 3. Authenticate user (this will validate the session)
    const { user } = await requireAuth(req);

    // 4. Get redirect URI
    const baseUrl = process.env.NEXTAUTH_URL || process.env.VERCEL_URL || 'http://localhost:3000';
    const redirectUri = `${baseUrl}/api/v1/integrations/instagram/callback`;

    // 5. Initialize Instagram service
    const { instagramService } = await import('@/lib/services');

    // 6. Exchange code for access token
    const authResult = await instagramService.authenticate(code, state, redirectUri);

    if (!authResult.success) {
      const frontendUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';
      const errorMessage = authResult.error?.message || 'Authentication failed';
      const errorUrl = `${frontendUrl}/integrations?error=auth_failed&description=${encodeURIComponent(errorMessage)}`;
      return NextResponse.redirect(errorUrl);
    }

    const { accessToken, userInfo } = authResult.data!;

    // 7. Get user's Instagram Business accounts
    const accountsResult = await instagramService.getInstagramAccounts(accessToken);

    if (!accountsResult.success) {
      const frontendUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';
      const errorUrl = `${frontendUrl}/integrations?error=accounts_failed&description=${encodeURIComponent('Failed to fetch Instagram accounts')}`;
      return NextResponse.redirect(errorUrl);
    }

    const accounts = accountsResult.data!;

    if (accounts.length === 0) {
      const frontendUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';
      const errorUrl = `${frontendUrl}/integrations?error=no_accounts&description=${encodeURIComponent('No Instagram Business accounts found. Please convert your Instagram account to a Business account and link it to a Facebook page.')}`;
      return NextResponse.redirect(errorUrl);
    }

    // 8. If only one account, auto-connect it
    if (accounts.length === 1) {
      const account = accounts[0] as any; // The account already has page_id and page_name from getInstagramAccounts
      const storeResult = await instagramService.storeAccountIntegration(user.id, account, accessToken);

      if (!storeResult.success) {
        const frontendUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';
        const errorUrl = `${frontendUrl}/integrations?error=store_failed&description=${encodeURIComponent('Failed to store Instagram account integration')}`;
        return NextResponse.redirect(errorUrl);
      }

      // Redirect to success page
      const frontendUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';
      const successUrl = `${frontendUrl}/integrations?success=instagram&account=${encodeURIComponent(account.username)}&integration_id=${storeResult.data}`;
      return NextResponse.redirect(successUrl);
    }

    // 9. Multiple accounts - redirect to account selection
    const frontendUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';
    
    // Store temporary data for account selection
    const tempData = {
      user_id: user.id,
      access_token: accessToken, // In production, encrypt this
      accounts: accounts.map((a: any) => ({ // The account already has page_id and page_name from getInstagramAccounts
        id: a.id,
        name: a.name,
        username: a.username,
        profile_picture_url: a.profile_picture_url,
        followers_count: a.followers_count,
        account_type: a.account_type,
        page_id: a.page_id,
        page_name: a.page_name
      }))
    };
    
    const selectUrl = `${frontendUrl}/integrations/instagram/select?data=${encodeURIComponent(btoa(JSON.stringify(tempData)))}`;
    return NextResponse.redirect(selectUrl);

  } catch (error) {
    console.error('Instagram callback error:', error);
    const frontendUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';
    const errorUrl = `${frontendUrl}/integrations?error=callback_failed&description=${encodeURIComponent('Instagram integration callback failed')}`;
    return NextResponse.redirect(errorUrl);
  }
}

/**
 * GET /api/v1/integrations/instagram/callback
 * Handle Instagram OAuth callback
 */
export const GET = withVersioning(withErrorHandler(instagramCallbackHandler));
