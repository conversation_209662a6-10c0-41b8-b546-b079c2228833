import { NextResponse } from 'next/server';
import { supabaseAdmin } from '@/lib/supabase';

export async function GET() {
  try {
    // Get the current authenticated user
    const { data: { user } } = await supabaseAdmin.auth.getUser();
    
    if (!user) {
      return NextResponse.json(
        { error: 'User not authenticated' },
        { status: 401 }
      );
    }

    // Get the Google accounts from Supabase
    const { data: accounts, error } = await supabaseAdmin
      .from('connected_accounts')
      .select('id, provider, provider_user_email, scopes, created_at, updated_at')
      .eq('user_id', user.id);

    if (error) {
      return NextResponse.json(
        { error: 'Error fetching connected accounts', details: error.message },
        { status: 500 }
      );
    }

    // Format response with helpful diagnostics
    const googleAccounts = accounts.filter(account => account.provider === 'google');
    
    return NextResponse.json({
      authenticated: true,
      userId: user.id,
      email: user.email,
      googleAccounts: googleAccounts.map(account => ({
        id: account.id,
        email: account.provider_user_email,
        scopes: account.scopes || [],
        hasYoutubeScope: account.scopes && (
          account.scopes.includes('https://www.googleapis.com/auth/youtube') ||
          account.scopes.includes('https://www.googleapis.com/auth/youtube.readonly') ||
          account.scopes.includes('https://www.googleapis.com/auth/youtube.upload')
        ),
        createdAt: account.created_at,
        updatedAt: account.updated_at
      })),
      totalGoogleAccounts: googleAccounts.length
    });
  } catch (error) {
    console.error('Debug endpoint error:', error);
    return NextResponse.json(
      { error: 'Server error', details: String(error) },
      { status: 500 }
    );
  }
} 