/**
 * PAY-004: Multi-Provider Webhook Router
 * 
 * API endpoint for handling webhooks from multiple payment providers.
 * Routes webhooks to appropriate handlers based on provider.
 */

import { NextRequest, NextResponse } from 'next/server';
import { WebhookService } from '@/lib/services/webhookService';
import { PaymentProvider } from '@/lib/payment/types';
import { AppError } from '@/lib/errors/AppError';

const webhookService = new WebhookService();

interface RouteParams {
  params: {
    provider: string;
  };
}

/**
 * POST /api/v1/webhooks/payment/[provider]
 * Handle webhook from specific payment provider
 */
export async function POST(request: NextRequest, { params }: RouteParams) {
  const startTime = Date.now();
  
  try {
    const { provider } = params;
    
    // Validate provider
    const validProviders: PaymentProvider[] = ['stripe', 'paypal', 'square', 'razorpay', 'paddle'];
    if (!validProviders.includes(provider as PaymentProvider)) {
      return NextResponse.json(
        { 
          success: false, 
          error: { 
            message: `Unsupported payment provider: ${provider}`, 
            code: 'UNSUPPORTED_PROVIDER' 
          } 
        },
        { status: 400 }
      );
    }

    // Get request headers and body
    const headers: Record<string, string> = {};
    request.headers.forEach((value, key) => {
      headers[key] = value;
    });

    const body = await request.text();

    // Log webhook received (for debugging)
    console.log(`Webhook received from ${provider}:`, {
      headers: Object.keys(headers),
      bodyLength: body.length,
      timestamp: new Date().toISOString()
    });

    // Process the webhook
    const result = await webhookService.processWebhook(
      provider as PaymentProvider,
      headers,
      body
    );

    const processingTime = Date.now() - startTime;

    // Log processing result
    console.log(`Webhook processed:`, {
      provider,
      eventId: result.id,
      eventType: result.type,
      processed: result.processed,
      processingTime: `${processingTime}ms`,
      error: result.error
    });

    if (result.processed) {
      return NextResponse.json({
        success: true,
        data: {
          eventId: result.id,
          eventType: result.type,
          provider: result.provider,
          billingEventId: result.billingEventId,
          processingTime: `${processingTime}ms`
        }
      });
    } else {
      return NextResponse.json(
        { 
          success: false, 
          error: { 
            message: result.error || 'Failed to process webhook', 
            code: 'WEBHOOK_PROCESSING_ERROR',
            eventId: result.id,
            provider: result.provider
          } 
        },
        { status: 400 }
      );
    }

  } catch (error) {
    const processingTime = Date.now() - startTime;
    
    console.error('Webhook processing error:', {
      provider: params.provider,
      error: error instanceof Error ? error.message : String(error),
      processingTime: `${processingTime}ms`,
      stack: error instanceof Error ? error.stack : undefined
    });
    
    if (error instanceof AppError) {
      return NextResponse.json(
        { 
          success: false, 
          error: { 
            message: error.message, 
            code: error.errorCode,
            provider: params.provider
          } 
        },
        { status: error.statusCode }
      );
    }

    return NextResponse.json(
      { 
        success: false, 
        error: { 
          message: 'Internal webhook processing error', 
          code: 'INTERNAL_WEBHOOK_ERROR',
          provider: params.provider
        } 
      },
      { status: 500 }
    );
  }
}

/**
 * GET /api/v1/webhooks/payment/[provider]
 * Get webhook endpoint information for a provider
 */
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const { provider } = params;
    
    // Validate provider
    const validProviders: PaymentProvider[] = ['stripe', 'paypal', 'square', 'razorpay', 'paddle'];
    if (!validProviders.includes(provider as PaymentProvider)) {
      return NextResponse.json(
        { 
          success: false, 
          error: { 
            message: `Unsupported payment provider: ${provider}`, 
            code: 'UNSUPPORTED_PROVIDER' 
          } 
        },
        { status: 400 }
      );
    }

    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3002';
    const webhookUrl = `${baseUrl}/api/v1/webhooks/payment/${provider}`;

    return NextResponse.json({
      success: true,
      data: {
        provider,
        webhookUrl,
        supportedEvents: getSupportedEvents(provider as PaymentProvider),
        configuration: getWebhookConfiguration(provider as PaymentProvider)
      }
    });

  } catch (error) {
    console.error('Error getting webhook info:', error);
    
    return NextResponse.json(
      { 
        success: false, 
        error: { 
          message: 'Failed to get webhook information', 
          code: 'WEBHOOK_INFO_ERROR' 
        } 
      },
      { status: 500 }
    );
  }
}

/**
 * Get supported events for a payment provider
 */
function getSupportedEvents(provider: PaymentProvider): string[] {
  const commonEvents = [
    'customer.created',
    'customer.updated',
    'customer.deleted',
    'subscription.created',
    'subscription.updated',
    'subscription.deleted',
    'invoice.payment_succeeded',
    'invoice.payment_failed',
    'checkout.session.completed'
  ];

  switch (provider) {
    case 'stripe':
      return [
        ...commonEvents,
        'subscription.trial_will_end',
        'payment_method.attached',
        'payment_method.detached'
      ];
    
    case 'paypal':
      return [
        ...commonEvents,
        'billing.subscription.activated',
        'billing.subscription.cancelled',
        'payment.sale.completed'
      ];
    
    case 'square':
      return [
        ...commonEvents,
        'subscription.charged',
        'subscription.deactivated'
      ];
    
    default:
      return commonEvents;
  }
}

/**
 * Get webhook configuration instructions for a provider
 */
function getWebhookConfiguration(provider: PaymentProvider): any {
  const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3002';
  const webhookUrl = `${baseUrl}/api/v1/webhooks/payment/${provider}`;

  switch (provider) {
    case 'stripe':
      return {
        url: webhookUrl,
        method: 'POST',
        headers: {
          'stripe-signature': 'Required for signature verification'
        },
        events: getSupportedEvents(provider),
        documentation: 'https://stripe.com/docs/webhooks'
      };
    
    case 'paypal':
      return {
        url: webhookUrl,
        method: 'POST',
        headers: {
          'paypal-transmission-id': 'Required for signature verification',
          'paypal-cert-id': 'Required for signature verification',
          'paypal-transmission-time': 'Required for signature verification',
          'paypal-transmission-sig': 'Required for signature verification'
        },
        events: getSupportedEvents(provider),
        documentation: 'https://developer.paypal.com/docs/api/webhooks/'
      };
    
    case 'square':
      return {
        url: webhookUrl,
        method: 'POST',
        headers: {
          'square-signature': 'Required for signature verification'
        },
        events: getSupportedEvents(provider),
        documentation: 'https://developer.squareup.com/docs/webhooks'
      };
    
    default:
      return {
        url: webhookUrl,
        method: 'POST',
        events: getSupportedEvents(provider)
      };
  }
}
