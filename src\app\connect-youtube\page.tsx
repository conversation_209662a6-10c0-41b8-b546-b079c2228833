'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'

export default function ConnectYouTubePage() {
  const router = useRouter()

  useEffect(() => {
    // Redirect to the working YouTube direct auth endpoint
    window.location.href = '/api/auth/youtube-direct?return_to=/publish'
  }, []);

  // Show minimal loading UI
  return (
    <div className="h-screen flex items-center justify-center bg-gray-50">
      <div className="text-center">
        <div className="w-16 h-16 border-4 border-t-blue-600 border-blue-200 rounded-full animate-spin mx-auto mb-4"></div>
        <h2 className="text-xl font-semibold mb-2">Connecting to YouTube...</h2>
        <p className="text-gray-500">You&apos;ll be redirected to Google&apos;s authorization page.</p>
      </div>
    </div>
  )
}