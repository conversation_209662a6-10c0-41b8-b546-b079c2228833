import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

export function middleware(request: NextRequest) {
  // Clone the response
  const response = NextResponse.next();
  
  // Construct CSP header
  const csp = `
    default-src 'self';
    script-src 'self' 'unsafe-inline' 'unsafe-eval' https://apis.google.com https://www.googleapis.com https://ssl.gstatic.com https://accounts.google.com https://www.google.com https://*.googleusercontent.com https://*.gstatic.com;
    connect-src 'self' https://apis.google.com https://www.googleapis.com https://accounts.google.com https://oauth2.googleapis.com https://youtube.googleapis.com https://www.youtube.com https://*.supabase.co https://drive.google.com wss://*.supabase.co https://api.supabase.co https://youtubeanalytics.googleapis.com https://*.googleusercontent.com;
    img-src 'self' data: blob: https: https://*.ytimg.com https://*.ggpht.com https://img.youtube.com https://*.googleusercontent.com;
    style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://*.gstatic.com;
    frame-src 'self' https://accounts.google.com https://drive.google.com https://www.youtube.com https://docs.google.com https://*.google.com;
    font-src 'self' https://fonts.gstatic.com data:;
    media-src 'self' https: blob:;
    object-src 'none';
    worker-src 'self' blob:;
  `.replace(/\s{2,}/g, ' ').trim();
  
  // Apply CSP header
  response.headers.set('Content-Security-Policy', csp);
  
  return response;
}

// Only run middleware on specific paths - modify as needed
export const config = {
  matcher: [
    '/((?!api/csp-debug|_next/static|_next/image|favicon.ico).*)',
  ],
}; 