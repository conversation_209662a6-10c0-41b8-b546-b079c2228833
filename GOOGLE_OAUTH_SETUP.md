# Google OAuth Setup for YouTube Integration

## ✅ ISSUE RESOLVED

**Problem:** The multi-channel implementation introduced a regression that broke the YouTube OAuth flow with "Access blocked: This app's request is invalid" error.

**Root Cause:** Multiple conflicting OAuth endpoints were using different redirect URIs:
- Working endpoint: `/api/auth/youtube-direct` → `http://localhost:3002/api/auth/youtube-direct-callback`
- Broken endpoint: `/api/connect/youtube/reconnect` → `http://localhost:3002/auth/callback/google` (doesn't exist)

**Solution:** Updated MainLayout to use the working OAuth endpoint for both initial and additional channel connections.

## Current Configuration Status

The OAuth flow is now working correctly with the proper redirect URI configuration.

## Required Google Cloud Console Setup

### 1. Go to Google Cloud Console
- Visit: https://console.cloud.google.com/
- Select your project or create a new one

### 2. Enable Required APIs
- Go to "APIs & Services" > "Library"
- Enable these APIs:
  - YouTube Data API v3
  - YouTube Analytics API
  - YouTube Reporting API
  - Google+ API (for user info)

### 3. Configure OAuth 2.0 Credentials
- Go to "APIs & Services" > "Credentials"
- Click "Create Credentials" > "OAuth 2.0 Client IDs"
- Application type: "Web application"
- Name: "Postbee YouTube Integration"

### 4. Add Authorized Redirect URIs
**CRITICAL**: Add these EXACT URIs to your OAuth 2.0 client:

For Development:
```
http://localhost:3002/api/auth/youtube-direct-callback
```

For Production (replace with your domain):
```
https://yourdomain.com/api/auth/youtube-direct-callback
```

### 5. Current Environment Variables
Based on your `.env.local`, you have:
- `GOOGLE_CLIENT_ID=242813273441-qjumlll447u89rkbngfrsofp0gg78en0.apps.googleusercontent.com`
- `GOOGLE_CLIENT_SECRET=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`

### 6. Verification Steps
1. Check the debug endpoint: http://localhost:3002/api/debug/oauth-config
2. Verify the redirect URI in Google Cloud Console matches exactly: `http://localhost:3002/api/auth/youtube-direct-callback`
3. Ensure the OAuth client ID matches your environment variable

### 7. Common Issues
- **Redirect URI mismatch**: The URI in Google Cloud Console must match exactly (including protocol, port, path)
- **Missing APIs**: Ensure YouTube Data API v3 is enabled
- **Wrong application type**: Must be "Web application", not "Desktop application"
- **Case sensitivity**: URIs are case-sensitive

### 8. Testing
After fixing the configuration:
1. Restart your development server
2. Try the YouTube connection again
3. Check browser developer tools for any console errors
