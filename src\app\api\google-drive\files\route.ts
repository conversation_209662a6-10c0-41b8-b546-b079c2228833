import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabaseClient } from '@/lib/supabase/server';
import { listFiles, refreshAccessToken } from '@/lib/google/drive';
import { OAuth2Client } from 'google-auth-library';

/**
 * API endpoint to list files from Google Drive
 * GET /api/google-drive/files
 */
export async function GET(request: NextRequest) {
  try {
    // Get query parameters
    const { searchParams } = new URL(request.url);
    const pageSize = searchParams.get('pageSize') ? parseInt(searchParams.get('pageSize')!) : 10;
    const query = searchParams.get('query') || undefined;
    const fields = searchParams.get('fields') || undefined;
    const videoOnly = searchParams.get('videoOnly') === 'true';
    
    // Initialize Supabase client
    const supabase = createServerSupabaseClient();
    
    // Get the current user from Supabase
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'User not authenticated' },
        { status: 401 }
      );
    }
    
    // Get the Google Drive connected account for the user
    const { data: connectedAccount, error: accountError } = await supabase
      .from('connected_accounts')
      .select('*')
      .eq('user_id', user.id)
      .eq('provider', 'google_drive')
      .single();
    
    if (accountError || !connectedAccount) {
      return NextResponse.json(
        { success: false, error: 'Google Drive not connected' },
        { status: 404 }
      );
    }
    
    // Check if the access token is expired and refresh if needed
    const now = Math.floor(Date.now() / 1000);
    let accessToken = connectedAccount.access_token;
    
    if (connectedAccount.expires_at && connectedAccount.expires_at < now) {
      // Token is expired, refresh it
      if (!connectedAccount.refresh_token) {
        return NextResponse.json(
          { success: false, error: 'Refresh token not available, please reconnect Google Drive' },
          { status: 401 }
        );
      }
      
      try {
        // Create OAuth2Client for token refresh
        const oauth2Client = new OAuth2Client(
          process.env.GOOGLE_CLIENT_ID,
          process.env.GOOGLE_CLIENT_SECRET
        );

        const newCredentials = await refreshAccessToken(oauth2Client, connectedAccount.refresh_token);
        
        // Update the tokens in the database
        await supabase
          .from('connected_accounts')
          .update({
            access_token: newCredentials.access_token,
            expires_at: newCredentials.expiry_date ? Math.floor(newCredentials.expiry_date / 1000) : null,
            token_type: newCredentials.token_type,
            scope: newCredentials.scope,
          })
          .eq('id', connectedAccount.id);
        
        accessToken = newCredentials.access_token;
      } catch (refreshError) {
        console.error('Error refreshing access token:', refreshError);
        return NextResponse.json(
          { success: false, error: 'Failed to refresh access token' },
          { status: 401 }
        );
      }
    }
    
    // Build the query for file listing
    let fileQuery = query;
    if (videoOnly) {
      fileQuery = fileQuery 
        ? `${fileQuery} and mimeType contains 'video/'` 
        : "mimeType contains 'video/'";
    }
    
    // List files from Google Drive
    const files = await listFiles(accessToken, {
      pageSize,
      query: fileQuery,
      fields
    });
    
    return NextResponse.json({ success: true, files });
  } catch (error) {
    console.error('Error listing Google Drive files:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'An error occurred while listing files' 
      },
      { status: 500 }
    );
  }
} 