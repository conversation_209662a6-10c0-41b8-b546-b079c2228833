import { NextResponse } from 'next/server'

/**
 * Debug endpoint to check OAuth configuration
 * This helps verify that environment variables are set correctly
 */
export async function GET() {
  const config = {
    // Environment variables (without exposing secrets)
    hasGoogleClientId: !!process.env.GOOGLE_CLIENT_ID,
    hasGoogleClientSecret: !!process.env.GOOGLE_CLIENT_SECRET,
    hasNextPublicUrl: !!process.env.NEXT_PUBLIC_URL,
    
    // Actual values (safe to expose)
    nextPublicUrl: process.env.NEXT_PUBLIC_URL,
    nextPublicAppUrl: process.env.NEXT_PUBLIC_APP_URL,
    
    // Computed redirect URIs
    youtubeDirectCallbackUri: process.env.NEXT_PUBLIC_URL 
      ? `${process.env.NEXT_PUBLIC_URL}/api/auth/youtube-direct-callback`
      : 'http://localhost:3002/api/auth/youtube-direct-callback',
    
    // Google Client ID (safe to expose)
    googleClientId: process.env.GOOGLE_CLIENT_ID,
    
    // Current timestamp
    timestamp: new Date().toISOString()
  }
  
  return NextResponse.json(config, { status: 200 })
}
