'use client';

import { useState, useEffect, useRef } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { useYouTubeChannel } from '@/lib/hooks/useYouTubeChannel';
import { useYouTubeConnections } from '@/lib/hooks/useYouTubeConnections';
import { Youtube, MoreVertical, Trash, RefreshCw, AlertTriangle, AlertCircle, CheckCircle, ExternalLink } from 'lucide-react';
import { Button } from '@/components/ui/Button';
import ConnectYouTube from '@/components/ConnectYouTube';
import { useToast } from '@/components/ui/toast';
import { createClient } from '@/lib/supabase/client';

interface ConnectedYouTubeChannelProps {
  className?: string;
}

export default function ConnectedYouTubeChannel({ className }: ConnectedYouTubeChannelProps) {
  // Use the new multi-channel hook instead of the single channel hook
  const { connections: youtubeConnections, isLoading, error, refetch } = useYouTubeConnections();
  const [imgErrors, setImgErrors] = useState<Record<string, boolean>>({});
  const [showMenus, setShowMenus] = useState<Record<string, boolean>>({});
  const [removingChannels, setRemovingChannels] = useState<Record<string, boolean>>({});
  const [permissionStatuses, setPermissionStatuses] = useState<Record<string, 'checking' | 'has_permission' | 'missing_permission' | 'error'>>({});
  const [reconnectUrls, setReconnectUrls] = useState<Record<string, string | null>>({});
  const [reconnectingChannels, setReconnectingChannels] = useState<Record<string, boolean>>({});
  
  const menuRefs = useRef<Record<string, HTMLDivElement | null>>({});
  const menuButtonRefs = useRef<Record<string, HTMLButtonElement | null>>({});
  const { toast } = useToast();

  // Close menu when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      Object.keys(showMenus).forEach(channelId => {
        const menuRef = menuRefs.current[channelId];
        const menuButtonRef = menuButtonRefs.current[channelId];

        if (showMenus[channelId] &&
            menuRef && !menuRef.contains(event.target as Node) &&
            menuButtonRef && !menuButtonRef.contains(event.target as Node)) {
          setShowMenus(prev => ({ ...prev, [channelId]: false }));
        }
      });
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showMenus]);
  
  useEffect(() => {
    // Check permissions for all connected channels
    if (youtubeConnections && youtubeConnections.length > 0) {
      youtubeConnections.forEach(connection => {
        if (connection.id) {
          checkYouTubePermissions(connection.id);
        }
      });
    }
  }, [youtubeConnections]);
  
  const checkYouTubePermissions = async (channelId: string) => {
    if (!channelId) return;

    try {
      setPermissionStatuses(prev => ({ ...prev, [channelId]: 'checking' }));

      // Get Supabase session for authentication
      const supabase = createClient();
      const { data: { session }, error: sessionError } = await supabase.auth.getSession();

      if (sessionError || !session) {
        setPermissionStatuses(prev => ({ ...prev, [channelId]: 'error' }));
        console.error('No active session for permission check');
        return;
      }

      const response = await fetch(`/api/check-youtube-permissions/unified?accountId=${channelId}`, {
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
          'Content-Type': 'application/json'
        }
      });
      const data = await response.json();

      if (data.success) {
        if (data.hasUploadPermission) {
          setPermissionStatuses(prev => ({ ...prev, [channelId]: 'has_permission' }));
        } else {
          setPermissionStatuses(prev => ({ ...prev, [channelId]: 'missing_permission' }));
          setReconnectUrls(prev => ({ ...prev, [channelId]: data.reconnectUrl || null }));
        }
      } else {
        setPermissionStatuses(prev => ({ ...prev, [channelId]: 'error' }));
        console.error('Error checking YouTube permissions:', data.message);
      }
    } catch (error) {
      setPermissionStatuses(prev => ({ ...prev, [channelId]: 'error' }));
      console.error('Error checking YouTube permissions:', error);
    }
  };
  
  const handleReconnect = () => {
    if (reconnectUrl) {
      setIsReconnecting(true);
      // Redirect to reconnection URL which includes OAuth authorization flow
      window.location.href = reconnectUrl;
    }
  };
  
  // Function to remove the YouTube channel
  async function removeYouTubeChannel() {
    console.log("Full channel info for debugging:", channelInfo);
    
    if (!channelInfo) {
      console.error('Cannot remove YouTube channel: No channel info available');
      toast({
        title: 'Error',
        description: 'Missing channel information. Please try again.',
        variant: 'destructive',
      });
      return;
    }
    
    // Get the accountId - it might be directly on the object or nested
    const accountId = channelInfo.accountId;
    
    if (!accountId) {
      console.log('No accountId available, falling back to using channelId');
      await removeChannelWithId(null);
      return;
    }
    
    await removeChannelWithId(accountId);
  }
  
  // Helper function to remove channel with a specific ID
  async function removeChannelWithId(accountId: string | null) {
    // If we don't have an accountId, but we have a channelId, use that instead
    const hasAccountId = !!accountId;
    const channelId = channelInfo?.channelId;
    
    if (!hasAccountId && !channelId) {
      console.error('Cannot remove YouTube channel: No accountId or channelId available');
      toast({
        title: 'Error',
        description: 'Missing channel information. Please try again.',
        variant: 'destructive',
      });
      return;
    }
    
    if (hasAccountId) {
      console.log('Attempting to remove YouTube channel with account ID:', accountId);
    } else {
      console.log('Attempting to remove YouTube channel with channel ID:', channelId);
    }
    
    try {
      setIsRemoving(true);
      
      // Use fetch to call a dedicated server-side API endpoint
      let endpoint = '/api/youtube/remove-channel?';
      if (hasAccountId) {
        endpoint += `accountId=${accountId}`;
      } else {
        endpoint += `channelId=${channelId}`;
      }
      
      // Get Supabase session for authentication
      const supabase = createClient();
      const { data: { session }, error: sessionError } = await supabase.auth.getSession();

      if (sessionError || !session) {
        throw new Error('Authentication required. Please sign in again.');
      }

      console.log('Sending request to:', endpoint);
      const response = await fetch(endpoint, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
          'Content-Type': 'application/json'
        }
      });
      
      const result = await response.json();
      console.log('Channel removal API response:', result);
      
      if (!response.ok) {
        throw new Error(result.error || 'Failed to remove channel');
      }
      
      toast({
        title: 'Channel removed',
        description: 'Your YouTube channel has been disconnected.',
      });
      
      // Clear localStorage cache to ensure we don't use cached data
      clearChannelCache();
      
      // The component will now show null for the channel data and display the connect button
      setChannelInfo(null);
      
      // Dispatch a custom event to notify all components about the YouTube channel removal
      const channelRemovedEvent = new CustomEvent('youtube_channel_removed', {
        detail: { timestamp: Date.now() }
      });
      window.dispatchEvent(channelRemovedEvent);
      
      // Force refresh the localStorage and sessionStorage to clear any cached channel info
      try {
        // Clear all YouTube-related items from localStorage
        Object.keys(localStorage).forEach(key => {
          if (key.includes('youtube') || key.includes('channel')) {
            localStorage.removeItem(key);
          }
        });
        
        // Add a timestamp to localStorage to force components to refetch
        localStorage.setItem('youtube_channel_removed_at', Date.now().toString());
      } catch (e) {
        console.warn('Error clearing localStorage:', e);
      }
      
      // Refetch to show the connect button with a slight delay
      // to ensure the database has time to update
      setTimeout(() => {
        fetchChannelInfo(true);
        
        // Force reload important components by navigating to the current page
        const currentPath = window.location.pathname;
        if (currentPath === '/publish') {
          // For the publish page, we'll do a soft reload to refresh all components
          window.location.href = `${currentPath}?youtube_removed=true&t=${Date.now()}`;
        }
      }, 500);
    } catch (error) {
      console.error('Error removing YouTube channel:', error);
      toast({
        title: 'Error',
        description: 'Failed to remove YouTube channel. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsRemoving(false);
      setShowMenu(false);
    }
  }

  // Show the connect button if we need to authorize or there's an error
  if (needsReauth || error || !channelInfo?.id) {
    return (
      <ConnectYouTube
        variant="outline"
        size="sm"
        className={className}
        returnTo="/publish"
        onSuccess={fetchChannelInfo}
        onFail={(error) => toast({
          title: 'Connection Failed',
          description: error,
          variant: 'destructive'
        })}
      />
    );
  }

  // Show a loading skeleton while fetching data
  if (isLoading) {
    return (
      <div className={`flex items-center px-3 py-2.5 rounded-lg border border-gray-200 ${className}`}>
        <div className="w-8 h-8 bg-gray-200 rounded-full animate-pulse mr-3"></div>
        <div className="flex-1">
          <div className="h-4 bg-gray-200 rounded animate-pulse mb-1 w-3/4"></div>
          <div className="h-3 bg-gray-200 rounded animate-pulse w-1/2"></div>
        </div>
      </div>
    );
  }

  // If we have channel info, display it
  if (channelInfo) {
    // Use fallback icon if thumbnail is not available or had an error
    const showFallbackIcon = !channelInfo.thumbnail || imgError;
    
    return (
      <div className="relative">
        <div className={`flex items-center px-3 py-2.5 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors duration-200 ${className}`}>
          <Link 
            href="/analyze/youtube"
            className="flex items-center flex-1 overflow-hidden"
          >
            {!showFallbackIcon ? (
              <div className="w-8 h-8 mr-3 relative overflow-hidden rounded-full bg-gray-100">
                <Image 
                  src={channelInfo.thumbnail || '/placeholder-avatar.png'} 
                  alt={channelInfo.title || 'YouTube Channel'} 
                  width={32} 
                  height={32}
                  className="rounded-full object-cover"
                  onError={() => setImgError(true)}
                  unoptimized
                />
              </div>
            ) : (
              <div className="w-8 h-8 bg-red-600 rounded-full flex items-center justify-center mr-3">
                <Youtube size={16} className="text-white" />
              </div>
            )}
            <div className="flex-1 overflow-hidden">
              <p className="font-medium text-gray-800 truncate">{channelInfo.title || 'YouTube Channel'}</p>
              <p className="text-xs text-gray-500">YouTube Channel</p>
              
              {/* Only show permission warning when it's missing, not during check */}
              {permissionStatus === 'missing_permission' && (
                <div className="flex items-center text-amber-600 text-xs mt-1">
                  <AlertTriangle size={12} className="mr-1" />
                  <span>Missing upload permission</span>
                </div>
              )}
            </div>
          </Link>
          
          {/* Three dots menu button */}
          <button 
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              setShowMenu(!showMenu);
            }}
            ref={menuButtonRef}
            className="p-1 rounded-full hover:bg-gray-200 transition-colors"
          >
            <MoreVertical size={16} className="text-gray-500" />
          </button>
        </div>
        
        {/* Dropdown menu */}
        {showMenu && (
          <div 
            ref={menuRef}
            className="absolute right-0 top-full mt-1 w-48 bg-white rounded-md shadow-lg z-10 border border-gray-200"
          >
            <div className="py-1">
              {permissionStatus === 'missing_permission' && reconnectUrl && (
                <Button
                  variant="default"
                  className="w-full justify-start text-amber-600 hover:bg-amber-50"
                  onClick={handleReconnect}
                  disabled={isReconnecting}
                >
                  <RefreshCw size={16} className="mr-2" />
                  {isReconnecting ? (
                    <>
                      Reconnecting...
                    </>
                  ) : (
                    <>Reconnect with Permissions</>
                  )}
                </Button>
              )}
              <button
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  removeYouTubeChannel();
                }}
                disabled={isRemoving}
                className="flex w-full items-center px-4 py-2 text-sm text-red-600 hover:bg-red-50"
              >
                <Trash size={16} className="mr-2" />
                {isRemoving ? 'Removing...' : 'Remove channel'}
              </button>
            </div>
          </div>
        )}
      </div>
    );
  }

  // Fallback to the connect button if we have no channel info
  return (
    <ConnectYouTube
      variant="outline"
      size="sm"
      className={className}
      onSuccess={fetchChannelInfo}
      onFail={(error) => toast({
        title: 'Connection Failed',
        description: error,
        variant: 'destructive'
      })}
    />
  );
} 