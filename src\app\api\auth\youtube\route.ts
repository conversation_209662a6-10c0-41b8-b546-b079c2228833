import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { google } from 'googleapis';

/**
 * Direct YouTube authorization endpoint
 * This bypasses Supabase auth for YouTube and uses Google OAuth directly
 */
export async function GET(request: NextRequest) {
  try {
    console.log('🎬 YouTube direct authorization endpoint called');
    
    // Get query parameters
    const url = new URL(request.url);
    const returnPath = url.searchParams.get('return_path') || '/publish';
    
    // Create Supabase client - only for session validation
    const supabase = createClient();
    
    // Verify user is logged in
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    
    if (sessionError) {
      console.error('❌ Session error:', sessionError);
      return NextResponse.redirect(`${process.env.NEXT_PUBLIC_APP_URL}/login?redirect=${encodeURIComponent(returnPath)}`);
    }
    
    if (!session) {
      console.log('❌ No active session found');
      return NextResponse.redirect(`${process.env.NEXT_PUBLIC_APP_URL}/login?redirect=${encodeURIComponent(returnPath)}`);
    }
    
    // Define the scopes needed for YouTube - including analytics scopes
    const scopes = [
      'https://www.googleapis.com/auth/youtube.readonly',
      'https://www.googleapis.com/auth/youtube.upload',
      'https://www.googleapis.com/auth/youtube.force-ssl',
      'https://www.googleapis.com/auth/youtube',
      'https://www.googleapis.com/auth/userinfo.profile',
      'https://www.googleapis.com/auth/userinfo.email',
      'https://www.googleapis.com/auth/yt-analytics.readonly',
      'https://www.googleapis.com/auth/yt-analytics-monetary.readonly'
    ];
    
    // Initialize the OAuth2 client
    const redirectUri = `${process.env.NEXT_PUBLIC_APP_URL}/youtube-callback`;
    console.log(`Using redirect URI for authorization: ${redirectUri}`);
    
    const oauth2Client = new google.auth.OAuth2(
      process.env.GOOGLE_CLIENT_ID,
      process.env.GOOGLE_CLIENT_SECRET,
      redirectUri
    );
    
    // Generate the authorization URL
    const authUrl = oauth2Client.generateAuthUrl({
      access_type: 'offline',
      scope: scopes,
      include_granted_scopes: true,
      prompt: 'consent',
      state: returnPath
    });
    
    console.log('✅ Redirecting to Google OAuth consent screen');
    console.log('Redirect URI:', `${process.env.NEXT_PUBLIC_APP_URL}/youtube-callback`);
    
    return NextResponse.redirect(authUrl);
  } catch (error: any) {
    console.error('❌ Error initiating YouTube OAuth flow:', error);
    
    // Get return path from request
    const url = new URL(request.url);
    const returnPath = url.searchParams.get('return_path') || '/publish';
    
    // Redirect with error
    return NextResponse.redirect(
      `${process.env.NEXT_PUBLIC_APP_URL}${returnPath}?youtube_error=${encodeURIComponent(error.message || 'Unknown error')}`
    );
  }
} 