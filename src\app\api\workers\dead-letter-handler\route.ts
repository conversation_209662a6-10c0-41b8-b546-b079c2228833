/**
 * QStash Worker: Dead Letter Queue Handler
 * 
 * <PERSON><PERSON> failed jobs with retry logic, error analysis, and notifications
 * POST /api/workers/dead-letter-handler
 */

import { NextRequest } from 'next/server';
import { createSecureWorkerHandler, extractQStashMetadata, logWorkerRequest, createWorkerResponse as createErrorResponse } from '../utils/signature-verification';
import { processWorkerJob, getSupabaseClient, shouldRetryJob, calculateRetryDelay, WorkerJobPayload, createWorkerResponse } from '../utils/worker-helpers';
import { publishToQStash } from '../utils/qstash-client';

// ============================================================================
// Types and Interfaces
// ============================================================================

interface DeadLetterJobPayload {
  jobId: string;
  originalJobPayload: any;
  failureReason: string;
  failureCount: number;
  lastFailureAt: string;
  originalEndpoint: string;
  type: 'dead_letter_processing';
}

interface DeadLetterProcessingResult {
  action: 'retried' | 'archived' | 'notified' | 'ignored';
  retryScheduled?: boolean;
  retryDelay?: number;
  notificationSent?: boolean;
  archivedAt?: string;
  reason: string;
}

interface FailedJobRecord {
  id: string;
  job_id: string;
  original_payload: any;
  failure_reason: string;
  failure_count: number;
  first_failure_at: string;
  last_failure_at: string;
  original_endpoint: string;
  status: 'pending' | 'retrying' | 'archived' | 'resolved';
  retry_count: number;
  max_retries: number;
  next_retry_at?: string;
  archived_at?: string;
  resolved_at?: string;
  metadata?: any;
}

// ============================================================================
// Dead Letter Processing Logic
// ============================================================================

async function processDeadLetterJob(payload: DeadLetterJobPayload): Promise<DeadLetterProcessingResult> {
  const supabase = getSupabaseClient();
  
  try {
    // Store or update failed job record
    const failedJobRecord = await storeFailedJob(payload);
    
    // Analyze failure and determine action
    const analysis = analyzeFailure(payload, failedJobRecord);
    
    switch (analysis.action) {
      case 'retry':
        return await scheduleRetry(payload, failedJobRecord, analysis.retryDelay!);
        
      case 'archive':
        return await archiveFailedJob(failedJobRecord, analysis.reason);
        
      case 'notify':
        return await sendFailureNotification(payload, failedJobRecord, analysis.reason);
        
      case 'ignore':
        return {
          action: 'ignored',
          reason: analysis.reason
        };
        
      default:
        throw new Error(`Unknown analysis action: ${analysis.action}`);
    }
    
  } catch (error) {
    console.error('Error processing dead letter job:', error);
    throw error;
  }
}

async function storeFailedJob(payload: DeadLetterJobPayload): Promise<FailedJobRecord> {
  const supabase = getSupabaseClient();
  
  try {
    // Check if failed job record already exists
    const { data: existingRecord } = await supabase
      .from('failed_jobs')
      .select('*')
      .eq('job_id', payload.jobId)
      .single();
    
    if (existingRecord) {
      // Update existing record
      const { data: updatedRecord, error } = await supabase
        .from('failed_jobs')
        .update({
          failure_reason: payload.failureReason,
          failure_count: payload.failureCount,
          last_failure_at: payload.lastFailureAt,
          retry_count: existingRecord.retry_count + 1,
          updated_at: new Date().toISOString()
        })
        .eq('job_id', payload.jobId)
        .select()
        .single();
      
      if (error) {
        throw new Error(`Failed to update failed job record: ${error.message}`);
      }
      
      return updatedRecord;
    } else {
      // Create new record
      const newRecord: Partial<FailedJobRecord> = {
        job_id: payload.jobId,
        original_payload: payload.originalJobPayload,
        failure_reason: payload.failureReason,
        failure_count: payload.failureCount,
        first_failure_at: payload.lastFailureAt,
        last_failure_at: payload.lastFailureAt,
        original_endpoint: payload.originalEndpoint,
        status: 'pending',
        retry_count: 0,
        max_retries: 3, // Default max retries
        metadata: {
          originalJobType: payload.originalJobPayload?.type,
          platform: payload.originalJobPayload?.platform,
          userId: payload.originalJobPayload?.userId
        }
      };
      
      const { data: createdRecord, error } = await supabase
        .from('failed_jobs')
        .insert(newRecord)
        .select()
        .single();
      
      if (error) {
        throw new Error(`Failed to create failed job record: ${error.message}`);
      }
      
      return createdRecord;
    }
  } catch (error) {
    console.error('Error storing failed job:', error);
    throw error;
  }
}

function analyzeFailure(
  payload: DeadLetterJobPayload, 
  record: FailedJobRecord
): { action: 'retry' | 'archive' | 'notify' | 'ignore'; reason: string; retryDelay?: number } {
  
  // Check if max retries exceeded
  if (record.retry_count >= record.max_retries) {
    return {
      action: 'archive',
      reason: `Maximum retries (${record.max_retries}) exceeded`
    };
  }
  
  // Analyze failure reason to determine if retry is worthwhile
  const failureReason = payload.failureReason.toLowerCase();
  
  // Non-retryable errors
  const nonRetryablePatterns = [
    'invalid credentials',
    'unauthorized',
    'forbidden',
    'not found',
    'validation error',
    'invalid format',
    'malformed request'
  ];
  
  const isNonRetryable = nonRetryablePatterns.some(pattern => 
    failureReason.includes(pattern)
  );
  
  if (isNonRetryable) {
    return {
      action: 'archive',
      reason: `Non-retryable error: ${payload.failureReason}`
    };
  }
  
  // Retryable errors with exponential backoff
  const retryablePatterns = [
    'rate limit',
    'timeout',
    'connection error',
    'service unavailable',
    'internal server error',
    'network error'
  ];
  
  const isRetryable = retryablePatterns.some(pattern => 
    failureReason.includes(pattern)
  );
  
  if (isRetryable) {
    const retryDelay = calculateRetryDelay(record.retry_count);
    return {
      action: 'retry',
      reason: `Retryable error detected, scheduling retry with ${retryDelay}ms delay`,
      retryDelay
    };
  }
  
  // Critical errors that need immediate attention
  const criticalPatterns = [
    'database error',
    'configuration error',
    'missing environment variable'
  ];
  
  const isCritical = criticalPatterns.some(pattern => 
    failureReason.includes(pattern)
  );
  
  if (isCritical) {
    return {
      action: 'notify',
      reason: `Critical error requiring immediate attention: ${payload.failureReason}`
    };
  }
  
  // Default to retry for unknown errors
  const retryDelay = calculateRetryDelay(record.retry_count);
  return {
    action: 'retry',
    reason: `Unknown error, attempting retry with ${retryDelay}ms delay`,
    retryDelay
  };
}

async function scheduleRetry(
  payload: DeadLetterJobPayload,
  record: FailedJobRecord,
  retryDelay: number
): Promise<DeadLetterProcessingResult> {
  const supabase = getSupabaseClient();
  
  try {
    // Schedule retry with QStash
    const retryResult = await publishToQStash({
      url: `${process.env.NEXT_PUBLIC_APP_URL}${payload.originalEndpoint}`,
      body: payload.originalJobPayload,
      delay: Math.floor(retryDelay / 1000), // Convert to seconds
      retries: 1, // Single retry attempt
      deduplicationId: `retry_${payload.jobId}_${record.retry_count + 1}`
    });
    
    // Update failed job record
    const nextRetryAt = new Date(Date.now() + retryDelay).toISOString();
    await supabase
      .from('failed_jobs')
      .update({
        status: 'retrying',
        next_retry_at: nextRetryAt,
        updated_at: new Date().toISOString()
      })
      .eq('id', record.id);
    
    console.log(`Scheduled retry for job ${payload.jobId} with delay ${retryDelay}ms`);
    
    return {
      action: 'retried',
      retryScheduled: true,
      retryDelay,
      reason: `Retry scheduled with ${retryDelay}ms delay`
    };
    
  } catch (error) {
    console.error('Error scheduling retry:', error);
    throw error;
  }
}

async function archiveFailedJob(
  record: FailedJobRecord,
  reason: string
): Promise<DeadLetterProcessingResult> {
  const supabase = getSupabaseClient();
  
  try {
    const archivedAt = new Date().toISOString();
    
    await supabase
      .from('failed_jobs')
      .update({
        status: 'archived',
        archived_at: archivedAt,
        updated_at: archivedAt,
        metadata: {
          ...record.metadata,
          archiveReason: reason
        }
      })
      .eq('id', record.id);
    
    console.log(`Archived failed job ${record.job_id}: ${reason}`);
    
    return {
      action: 'archived',
      archivedAt,
      reason
    };
    
  } catch (error) {
    console.error('Error archiving failed job:', error);
    throw error;
  }
}

async function sendFailureNotification(
  payload: DeadLetterJobPayload,
  record: FailedJobRecord,
  reason: string
): Promise<DeadLetterProcessingResult> {
  try {
    // In a real implementation, you would send notifications via:
    // - Email
    // - Slack
    // - Discord
    // - SMS
    // - Push notifications
    
    console.error(`CRITICAL FAILURE NOTIFICATION:`, {
      jobId: payload.jobId,
      reason,
      failureCount: payload.failureCount,
      originalEndpoint: payload.originalEndpoint,
      userId: payload.originalJobPayload?.userId,
      platform: payload.originalJobPayload?.platform
    });
    
    // For now, just log the notification
    // TODO: Implement actual notification system
    
    return {
      action: 'notified',
      notificationSent: true,
      reason: `Critical failure notification sent: ${reason}`
    };
    
  } catch (error) {
    console.error('Error sending failure notification:', error);
    return {
      action: 'notified',
      notificationSent: false,
      reason: `Failed to send notification: ${error instanceof Error ? error.message : 'Unknown error'}`
    };
  }
}

// ============================================================================
// Worker Handler
// ============================================================================

async function deadLetterHandler(req: NextRequest): Promise<Response> {
  logWorkerRequest(req, 'DeadLetterHandler');

  const result = await processWorkerJob(req, async (payload: WorkerJobPayload) => {
    const deadLetterPayload = payload as unknown as DeadLetterJobPayload;
    console.log(`Processing dead letter job ${deadLetterPayload.jobId}`, {
      originalEndpoint: deadLetterPayload.originalEndpoint,
      failureReason: deadLetterPayload.failureReason,
      failureCount: deadLetterPayload.failureCount
    });

    const result = await processDeadLetterJob(deadLetterPayload);

    console.log(`Dead letter job ${deadLetterPayload.jobId} processed:`, result);
    
    return result;
  });

  return createWorkerResponse(result);
}

// ============================================================================
// Route Handlers
// ============================================================================

export const POST = createSecureWorkerHandler(deadLetterHandler);

export async function GET() {
  return createErrorResponse({
    success: false,
    error: {
      message: 'Method not allowed. This endpoint only accepts POST requests from QStash.',
      code: 'METHOD_NOT_ALLOWED'
    }
  }, 405);
}

export async function PUT() {
  return createErrorResponse({
    success: false,
    error: {
      message: 'Method not allowed. This endpoint only accepts POST requests from QStash.',
      code: 'METHOD_NOT_ALLOWED'
    }
  }, 405);
}

export async function DELETE() {
  return createErrorResponse({
    success: false,
    error: {
      message: 'Method not allowed. This endpoint only accepts POST requests from QStash.',
      code: 'METHOD_NOT_ALLOWED'
    }
  }, 405);
}
