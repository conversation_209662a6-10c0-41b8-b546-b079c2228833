/**
 * LinkedIn OAuth Initiate Endpoint
 * 
 * Initiates LinkedIn OAuth 2.0 authentication flow
 * POST /api/connect/linkedin/initiate
 */

import { NextRequest, NextResponse } from 'next/server';
import { createServerComponentClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { LinkedInAuthService } from '@/lib/services/linkedinAuthService';
import { 
  with<PERSON>rror<PERSON><PERSON><PERSON>, 
  requireAuth,
  ErrorFactory
} from '@/lib/error-handler';

/**
 * Request body interface for LinkedIn OAuth initiation
 */
interface LinkedInOAuthInitiateRequest {
  includeCompanyPages?: boolean;
  customRedirectUri?: string;
}

/**
 * Internal handler for LinkedIn OAuth initiation
 */
async function linkedinOAuthInitiateHandler(req: NextRequest) {
  try {
    // 1. Authenticate user
    const { user } = await requireAuth(req);

    // 2. Parse request body
    let requestBody: LinkedInOAuthInitiateRequest = {};
    try {
      requestBody = await req.json();
    } catch (error) {
      // Default to empty object if no body provided
    }

    const { 
      includeCompanyPages = true, 
      customRedirectUri 
    } = requestBody;

    // 3. Get redirect URI from request or construct default
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || process.env.NEXTAUTH_URL || 'http://localhost:3002';
    const redirectUri = customRedirectUri || `${baseUrl}/api/connect/linkedin/callback`;

    // 4. Initialize LinkedIn authentication service
    const supabase = createServerComponentClient({ cookies });
    const linkedinAuthService = new LinkedInAuthService({
      supabase,
      supabaseAdmin: supabase // Using same client for now
    });

    // 5. Generate OAuth authorization URL
    const result = await linkedinAuthService.generateAuthUrl(
      user.id, 
      redirectUri, 
      includeCompanyPages
    );

    if (!result.success) {
      throw result.error;
    }

    const { authUrl, state } = result.data!;

    // 6. Return authorization URL and state
    return NextResponse.json({
      success: true,
      data: {
        authUrl,
        state,
        provider: 'linkedin',
        redirectUri,
        includeCompanyPages,
        scopes: includeCompanyPages 
          ? ['r_liteprofile', 'r_emailaddress', 'w_member_social', 'r_organization_social', 'w_organization_social', 'rw_organization_admin']
          : ['r_liteprofile', 'r_emailaddress', 'w_member_social']
      }
    });

  } catch (error) {
    console.error('LinkedIn OAuth initiate error:', error);
    
    // Handle different error types
    if (error && typeof error === 'object' && 'code' in error) {
      // This is an AppError from our error handling system
      const appError = error as any; // Type assertion for error object
      return NextResponse.json({
        success: false,
        error: {
          message: appError.message || 'Failed to initiate LinkedIn OAuth',
          code: appError.code || 'OAUTH_INITIATE_FAILED',
          details: appError.details || null
        }
      }, { status: appError.statusCode || 500 });
    }

    // Generic error handling
    return NextResponse.json({
      success: false,
      error: {
        message: 'Failed to initiate LinkedIn OAuth',
        code: 'OAUTH_INITIATE_FAILED',
        details: error instanceof Error ? error.message : 'Unknown error'
      }
    }, { status: 500 });
  }
}

/**
 * POST handler with error wrapper
 */
export const POST = withErrorHandler(linkedinOAuthInitiateHandler);

/**
 * Handle unsupported methods
 */
export async function GET() {
  return NextResponse.json({
    success: false,
    error: {
      message: 'Method not allowed. Use POST to initiate LinkedIn OAuth.',
      code: 'METHOD_NOT_ALLOWED'
    }
  }, { status: 405 });
}

export async function PUT() {
  return NextResponse.json({
    success: false,
    error: {
      message: 'Method not allowed. Use POST to initiate LinkedIn OAuth.',
      code: 'METHOD_NOT_ALLOWED'
    }
  }, { status: 405 });
}

export async function DELETE() {
  return NextResponse.json({
    success: false,
    error: {
      message: 'Method not allowed. Use POST to initiate LinkedIn OAuth.',
      code: 'METHOD_NOT_ALLOWED'
    }
  }, { status: 405 });
}
