const fetch = require('node-fetch');

async function testEndpoint() {
  try {
    const url = 'https://7861-2404-8ec0-4-2d98-a950-a216-4887-d919.ngrok-free.app/api/workers/publish-post';
    console.log(`Testing endpoint: ${url}`);
    
    // Create a random post ID in the UUID format
    const randomPostId = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
    
    console.log(`Using test post ID: ${randomPostId}`);
    
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        postId: randomPostId,
        // Include additional test data to help debugging
        postData: {
          title: 'Test Post from ngrok',
          content: 'This is a test post to verify QStash callback functionality',
          user_id: 'test-user',
          status: 'draft'
        }
      })
    });
    
    const status = response.status;
    console.log(`Response status: ${status}`);
    
    const text = await response.text();
    console.log('Response body:', text);
    
    return { status, text };
  } catch (error) {
    console.error('Error testing endpoint:', error);
    return { error: error.message };
  }
}

testEndpoint(); 