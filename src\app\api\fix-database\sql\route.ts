import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';

// SQL script to create the necessary tables and functions
const SQL_SCRIPT = `
-- Create the media_assets table if it doesn't exist
CREATE TABLE IF NOT EXISTS media_assets (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  file_name TEXT,
  file_type TEXT,
  file_size INTEGER,
  file_url TEXT,
  thumbnail_url TEXT,
  width INTEGER,
  height INTEGER,
  duration INTEGER,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS
ALTER TABLE media_assets ENABLE ROW LEVEL SECURITY;

-- Create policies
DO $$
BEGIN
  -- Drop existing policies to avoid conflicts
  DROP POLICY IF EXISTS "Users can view their own media" ON media_assets;
  DROP POLICY IF EXISTS "Users can insert their own media" ON media_assets;
  DROP POLICY IF EXISTS "Users can update their own media" ON media_assets;
  DROP POLICY IF EXISTS "Users can delete their own media" ON media_assets;
  
  -- Create new policies
  CREATE POLICY "Users can view their own media"
    ON media_assets FOR SELECT
    USING (auth.uid() = user_id);
  
  CREATE POLICY "Users can insert their own media"
    ON media_assets FOR INSERT
    WITH CHECK (auth.uid() = user_id);
  
  CREATE POLICY "Users can update their own media"
    ON media_assets FOR UPDATE
    USING (auth.uid() = user_id);
  
  CREATE POLICY "Users can delete their own media"
    ON media_assets FOR DELETE
    USING (auth.uid() = user_id);
END$$;

-- Create media table if it doesn't exist (for compatibility)
CREATE TABLE IF NOT EXISTS media (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  file_name TEXT,
  file_type TEXT,
  file_size INTEGER,
  file_url TEXT,
  thumbnail_url TEXT,
  width INTEGER,
  height INTEGER,
  duration INTEGER,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS
ALTER TABLE media ENABLE ROW LEVEL SECURITY;

-- Create policies
DO $$
BEGIN
  -- Drop existing policies to avoid conflicts
  DROP POLICY IF EXISTS "Users can view their own media" ON media;
  DROP POLICY IF EXISTS "Users can insert their own media" ON media;
  DROP POLICY IF EXISTS "Users can update their own media" ON media;
  DROP POLICY IF EXISTS "Users can delete their own media" ON media;
  
  -- Create new policies
  CREATE POLICY "Users can view their own media"
    ON media FOR SELECT
    USING (auth.uid() = user_id);
  
  CREATE POLICY "Users can insert their own media"
    ON media FOR INSERT
    WITH CHECK (auth.uid() = user_id);
  
  CREATE POLICY "Users can update their own media"
    ON media FOR UPDATE
    USING (auth.uid() = user_id);
  
  CREATE POLICY "Users can delete their own media"
    ON media FOR DELETE
    USING (auth.uid() = user_id);
END$$;

-- Create functions to keep tables in sync
CREATE OR REPLACE FUNCTION sync_media_insert()
RETURNS TRIGGER AS $$
BEGIN
  IF TG_TABLE_NAME = 'media' THEN
    INSERT INTO media_assets (id, user_id, file_name, file_type, file_size, file_url, thumbnail_url, width, height, duration, created_at, updated_at)
    VALUES (NEW.id, NEW.user_id, NEW.file_name, NEW.file_type, NEW.file_size, NEW.file_url, NEW.thumbnail_url, NEW.width, NEW.height, NEW.duration, NEW.created_at, NEW.updated_at);
  ELSE
    INSERT INTO media (id, user_id, file_name, file_type, file_size, file_url, thumbnail_url, width, height, duration, created_at, updated_at)
    VALUES (NEW.id, NEW.user_id, NEW.file_name, NEW.file_type, NEW.file_size, NEW.file_url, NEW.thumbnail_url, NEW.width, NEW.height, NEW.duration, NEW.created_at, NEW.updated_at);
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create sync triggers
DO $$
BEGIN
  DROP TRIGGER IF EXISTS sync_media_to_media_assets ON media;
  CREATE TRIGGER sync_media_to_media_assets
    AFTER INSERT ON media
    FOR EACH ROW
    EXECUTE FUNCTION sync_media_insert();
  
  DROP TRIGGER IF EXISTS sync_media_assets_to_media ON media_assets;
  CREATE TRIGGER sync_media_assets_to_media
    AFTER INSERT ON media_assets
    FOR EACH ROW
    EXECUTE FUNCTION sync_media_insert();
END$$;

-- Update or Create post_media table
CREATE TABLE IF NOT EXISTS post_media (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  post_id UUID NOT NULL,
  media_id UUID NOT NULL,
  display_order INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(post_id, media_id)
);

-- Enable RLS
ALTER TABLE post_media ENABLE ROW LEVEL SECURITY;

-- Create policies
DO $$
BEGIN
  -- Drop existing policies to avoid conflicts
  DROP POLICY IF EXISTS "Users can view their own post media" ON post_media;
  DROP POLICY IF EXISTS "Users can insert their own post media" ON post_media;
  DROP POLICY IF EXISTS "Users can update their own post media" ON post_media;
  DROP POLICY IF EXISTS "Users can delete their own post media" ON post_media;
  
  -- Create new policies
  CREATE POLICY "Users can view their own post media"
    ON post_media FOR SELECT
    USING (EXISTS (
      SELECT 1 FROM posts
      WHERE posts.id = post_media.post_id
      AND posts.user_id = auth.uid()
    ));
  
  CREATE POLICY "Users can insert their own post media"
    ON post_media FOR INSERT
    WITH CHECK (EXISTS (
      SELECT 1 FROM posts
      WHERE posts.id = post_media.post_id
      AND posts.user_id = auth.uid()
    ));
  
  CREATE POLICY "Users can update their own post media"
    ON post_media FOR UPDATE
    USING (EXISTS (
      SELECT 1 FROM posts
      WHERE posts.id = post_media.post_id
      AND posts.user_id = auth.uid()
    ));
  
  CREATE POLICY "Users can delete their own post media"
    ON post_media FOR DELETE
    USING (EXISTS (
      SELECT 1 FROM posts
      WHERE posts.id = post_media.post_id
      AND posts.user_id = auth.uid()
    ));
END$$;
`;

export async function GET(req: NextRequest) {
  try {
    const supabase = createClient();
    
    console.log('Running SQL to fix database schema issues...');
    
    // Execute the SQL script
    const { data, error } = await supabase.rpc('run_sql', { sql: SQL_SCRIPT });
    
    if (error) {
      console.error('Error executing SQL:', error);
      return NextResponse.json({
        success: false,
        error: error.message
      }, { status: 500 });
    }
    
    return NextResponse.json({
      success: true,
      message: 'Database schema fixed successfully',
      data
    });
  } catch (error) {
    console.error('Error fixing database schema:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
} 