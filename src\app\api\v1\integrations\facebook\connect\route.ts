/**
 * API v1 - Facebook Integration Connect Route
 * 
 * Initiates Facebook OAuth flow for page integration
 */

import { NextRequest } from 'next/server';
import { FacebookService } from '@/lib/services/facebookService';
import { 
  withErrorHandler, 
  requireAuth
} from '@/lib/error-handler';
import { withVersioning } from '@/lib/versioning/middleware';

/**
 * Internal handler for Facebook OAuth initiation
 */
async function facebookConnectHandler(req: NextRequest) {
  // 1. Authenticate user
  const { user } = await requireAuth(req);

  // 2. Get redirect URI from environment or construct it
  const baseUrl = process.env.NEXTAUTH_URL || process.env.VERCEL_URL || 'http://localhost:3000';
  const redirectUri = `${baseUrl}/api/v1/integrations/facebook/callback`;

  // 3. Initialize Facebook service
  const { facebookService } = await import('@/lib/services');

  // 4. Generate OAuth authorization URL
  const result = await facebookService.generateAuthUrl(user.id, redirectUri);

  if (!result.success) {
    throw result.error;
  }

  // 5. Return authorization URL for client to redirect to
  return {
    success: true,
    data: {
      authUrl: result.data!.authUrl,
      state: result.data!.state,
      redirectUri
    },
    metadata: {
      provider: 'facebook',
      scopes: [
        'pages_show_list',
        'business_management',
        'pages_manage_posts',
        'pages_manage_engagement',
        'pages_read_engagement',
        'read_insights',
        'pages_read_user_content'
      ]
    }
  };
}

/**
 * GET /api/v1/integrations/facebook/connect
 * Initiate Facebook OAuth flow
 */
export const GET = withVersioning(withErrorHandler(facebookConnectHandler));
