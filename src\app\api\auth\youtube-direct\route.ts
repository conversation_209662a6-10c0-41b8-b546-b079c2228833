import { createClient } from '@/lib/supabase/server'
import { NextResponse, NextRequest } from 'next/server'
import { google } from 'googleapis'

/**
 * Direct YouTube authorization endpoint
 * This bypasses Supabase and directly uses Google OAuth2
 */
export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams
  const supabase = createClient()
  
  // Check if user is authenticated
  const { data: { session }, error: sessionError } = await supabase.auth.getSession()
    
  // Get the return path (where to redirect after authentication)
  // Default to /publish if not specified
  const returnTo = searchParams.get('return_to') || '/publish'
  // Check if analytics scopes should be included (default to true)
  const includeAnalytics = searchParams.get('include_analytics') !== 'false'
  // Check if this is an additional channel connection
  const isAdditional = searchParams.get('additional') === 'true'

  // Get state from query parameters if present (used by the YouTube reconnection component)
  const passedState = searchParams.get('state')
  let stateData = {
    redirectUri: returnTo,
    userId: session?.user?.id,
    includeAnalytics,
    additional: isAdditional,
    timestamp: Date.now()
  }
    
  if (sessionError || !session) {
    console.error('YouTube direct: No active session found', sessionError)
    
    // Instead of redirecting to sign in, redirect to the return path with error params
    return NextResponse.redirect(
      `${returnTo}?auth_error=true&message=${encodeURIComponent('Please sign in to connect your YouTube account.')}`
    )
  }
  
  try {
    // If a state was passed in, parse it and use its values
    if (passedState) {
      try {
        const parsedState = JSON.parse(passedState)
        stateData = {
          ...stateData,
          ...parsedState
        }
        console.log('YouTube direct: Using passed state data:', stateData)
      } catch (e) {
        console.error('YouTube direct: Error parsing passed state:', e)
      }
    }

    // IMPORTANT: This must match EXACTLY what's configured in Google Cloud Console
    // This is the critical part for fixing the redirect_uri_mismatch error
    const callbackUri = process.env.NEXT_PUBLIC_URL 
      ? `${process.env.NEXT_PUBLIC_URL}/api/auth/youtube-direct-callback`
      : 'http://localhost:3002/api/auth/youtube-direct-callback';
    
    console.log('YouTube direct: Using EXACT callback URI:', callbackUri);
    
    // Create OAuth client with the consistent callback URI
    console.log('YouTube direct: OAuth configuration:', {
      client_id_length: process.env.GOOGLE_CLIENT_ID ? process.env.GOOGLE_CLIENT_ID.length : 0,
      client_secret_length: process.env.GOOGLE_CLIENT_SECRET ? process.env.GOOGLE_CLIENT_SECRET.length : 0,
      callback_uri: callbackUri
    })
    
    // Before creating the OAuth client, verify that we have all required credentials
    if (!process.env.GOOGLE_CLIENT_ID || !process.env.GOOGLE_CLIENT_SECRET) {
      console.error('YouTube direct: Missing required Google OAuth credentials')
      return NextResponse.redirect(
        `${returnTo}?error=true&message=${encodeURIComponent('Server configuration error: Missing OAuth credentials')}`
      )
    }

    const oauth2Client = new google.auth.OAuth2(
      process.env.GOOGLE_CLIENT_ID,
      process.env.GOOGLE_CLIENT_SECRET,
      callbackUri
    )
    
    // Prepare state parameter
    const state = JSON.stringify(stateData)
    
    console.log('YouTube direct: Prepared state data:', {
      redirectUri: stateData.redirectUri,
      user_id_length: session.user.id.length,
      includeAnalytics: stateData.includeAnalytics,
      additional: stateData.additional
    })
    
    // YouTube scopes - now including analytics scopes and partner scope
    const scopes = [
      'https://www.googleapis.com/auth/youtube',
      'https://www.googleapis.com/auth/youtube.readonly',
      'https://www.googleapis.com/auth/youtube.upload',
      'https://www.googleapis.com/auth/youtube.force-ssl',
      'https://www.googleapis.com/auth/youtubepartner',
      'https://www.googleapis.com/auth/userinfo.email',
      'https://www.googleapis.com/auth/userinfo.profile',
    ]
    
    // Add analytics scopes if requested
    if (stateData.includeAnalytics) {
      scopes.push(
        'https://www.googleapis.com/auth/yt-analytics.readonly',
        'https://www.googleapis.com/auth/yt-analytics-monetary.readonly'
      )
      console.log('YouTube direct: Including analytics scopes')
    }
    
    // Log all scopes being requested
    console.log('YouTube direct: Requesting scopes:', scopes)
    
    // Generate authorization URL
    const authorizeUrl = oauth2Client.generateAuthUrl({
      access_type: 'offline',
      scope: scopes,
      include_granted_scopes: true,
      prompt: 'consent',
      state
    })
    
    // Verify the scopes are in the URL
    const authorizeUrlObj = new URL(authorizeUrl)
    console.log('YouTube direct: Verifying scopes in URL:', {
      hasScope: authorizeUrlObj.searchParams.has('scope'),
      includesAnalytics: stateData.includeAnalytics, 
      hasYTAnalytics: authorizeUrlObj.searchParams.get('scope')?.includes('yt-analytics.readonly') || false,
      hasYTMonetaryAnalytics: authorizeUrlObj.searchParams.get('scope')?.includes('yt-analytics-monetary.readonly') || false
    })
    
    console.log('YouTube direct: Redirecting to Google OAuth...')
    console.log('Redirect URI:', callbackUri)
    
    return NextResponse.redirect(authorizeUrl)
  } catch (error) {
    console.error('YouTube direct: Error generating auth URL:', error)
    
    return NextResponse.redirect(
      `${returnTo}?error=true&message=${encodeURIComponent('Failed to connect YouTube. Please try again.')}`
    )
  }
} 