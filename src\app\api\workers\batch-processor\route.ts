/**
 * QStash Worker: Batch Job Processor
 * 
 * Handles batch processing of multiple jobs with concurrency control
 * POST /api/workers/batch-processor
 */

import { NextRequest } from 'next/server';
import { createSecureWorkerHandler, extractQStashMetadata, logWorkerRequest, createWorkerResponse as createErrorResponse } from '../utils/signature-verification';
import { processWorkerJob, getSupabaseClient, WorkerJobPayload, createWorkerResponse } from '../utils/worker-helpers';
import { publishToQStash, batchPublish } from '../utils/qstash-client';

// ============================================================================
// Types and Interfaces
// ============================================================================

interface BatchJobPayload extends WorkerJobPayload {
  batchId: string;
  jobs: Array<{
    id: string;
    endpoint: string;
    payload: any;
    priority?: number;
    delay?: number;
  }>;
  concurrency?: number;
  failureCallback?: string;
  type: 'batch_processing';
}

interface BatchProcessingResult {
  batchId: string;
  totalJobs: number;
  successfulJobs: number;
  failedJobs: number;
  skippedJobs: number;
  processingTimeMs: number;
  results: Array<{
    jobId: string;
    status: 'success' | 'failed' | 'skipped';
    messageId?: string;
    error?: string;
  }>;
}

interface BatchJobRecord {
  id: string;
  batch_id: string;
  total_jobs: number;
  completed_jobs: number;
  failed_jobs: number;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  created_at: string;
  started_at?: string;
  completed_at?: string;
  metadata?: any;
}

// ============================================================================
// Batch Processing Logic
// ============================================================================

async function processBatchJobs(payload: BatchJobPayload): Promise<BatchProcessingResult> {
  const startTime = Date.now();
  const supabase = getSupabaseClient();
  
  try {
    // Create or update batch record
    const batchRecord = await createBatchRecord(payload);
    
    // Sort jobs by priority (higher priority first)
    const sortedJobs = payload.jobs.sort((a, b) => (b.priority || 0) - (a.priority || 0));
    
    // Process jobs with concurrency control
    const concurrency = payload.concurrency || 5;
    const results = await processJobsWithConcurrency(sortedJobs, concurrency, payload);
    
    // Calculate final results
    const successfulJobs = results.filter(r => r.status === 'success').length;
    const failedJobs = results.filter(r => r.status === 'failed').length;
    const skippedJobs = results.filter(r => r.status === 'skipped').length;
    const processingTimeMs = Date.now() - startTime;
    
    // Update batch record with final status
    await updateBatchRecord(batchRecord.id, {
      status: failedJobs > 0 ? 'failed' : 'completed',
      completed_jobs: successfulJobs,
      failed_jobs: failedJobs,
      completed_at: new Date().toISOString(),
      metadata: {
        ...batchRecord.metadata,
        processingTimeMs,
        skippedJobs
      }
    });
    
    console.log(`Batch ${payload.batchId} completed: ${successfulJobs} success, ${failedJobs} failed, ${skippedJobs} skipped`);
    
    return {
      batchId: payload.batchId,
      totalJobs: payload.jobs.length,
      successfulJobs,
      failedJobs,
      skippedJobs,
      processingTimeMs,
      results
    };
    
  } catch (error) {
    console.error(`Error processing batch ${payload.batchId}:`, error);
    throw error;
  }
}

async function createBatchRecord(payload: BatchJobPayload): Promise<BatchJobRecord> {
  const supabase = getSupabaseClient();
  
  try {
    const batchRecord = {
      batch_id: payload.batchId,
      total_jobs: payload.jobs.length,
      completed_jobs: 0,
      failed_jobs: 0,
      status: 'processing' as const,
      started_at: new Date().toISOString(),
      metadata: {
        concurrency: payload.concurrency || 5,
        maxRetries: payload.maxRetries || 3,
        failureCallback: payload.failureCallback,
        jobTypes: payload.jobs.map(job => job.payload?.type).filter(Boolean)
      }
    };
    
    const { data, error } = await supabase
      .from('batch_jobs')
      .insert(batchRecord)
      .select()
      .single();
    
    if (error) {
      throw new Error(`Failed to create batch record: ${error.message}`);
    }
    
    return data;
  } catch (error) {
    console.error('Error creating batch record:', error);
    throw error;
  }
}

async function updateBatchRecord(
  batchId: string, 
  updates: Partial<BatchJobRecord>
): Promise<void> {
  const supabase = getSupabaseClient();
  
  try {
    const { error } = await supabase
      .from('batch_jobs')
      .update({
        ...updates,
        updated_at: new Date().toISOString()
      })
      .eq('id', batchId);
    
    if (error) {
      throw new Error(`Failed to update batch record: ${error.message}`);
    }
  } catch (error) {
    console.error('Error updating batch record:', error);
    throw error;
  }
}

async function processJobsWithConcurrency(
  jobs: BatchJobPayload['jobs'],
  concurrency: number,
  batchPayload: BatchJobPayload
): Promise<BatchProcessingResult['results']> {
  const results: BatchProcessingResult['results'] = [];
  
  // Process jobs in chunks based on concurrency
  for (let i = 0; i < jobs.length; i += concurrency) {
    const chunk = jobs.slice(i, i + concurrency);
    
    console.log(`Processing batch chunk ${Math.floor(i / concurrency) + 1}: ${chunk.length} jobs`);
    
    // Process chunk concurrently
    const chunkPromises = chunk.map(job => processIndividualJob(job, batchPayload));
    const chunkResults = await Promise.allSettled(chunkPromises);
    
    // Collect results
    chunkResults.forEach((result, index) => {
      const job = chunk[index];
      
      if (result.status === 'fulfilled') {
        results.push(result.value);
      } else {
        console.error(`Job ${job.id} failed:`, result.reason);
        results.push({
          jobId: job.id,
          status: 'failed',
          error: result.reason instanceof Error ? result.reason.message : 'Unknown error'
        });
      }
    });
    
    // Add small delay between chunks to prevent overwhelming the system
    if (i + concurrency < jobs.length) {
      await new Promise(resolve => setTimeout(resolve, 100));
    }
  }
  
  return results;
}

async function processIndividualJob(
  job: BatchJobPayload['jobs'][0],
  batchPayload: BatchJobPayload
): Promise<BatchProcessingResult['results'][0]> {
  try {
    // Validate job
    if (!job.endpoint || !job.payload) {
      return {
        jobId: job.id,
        status: 'skipped',
        error: 'Missing endpoint or payload'
      };
    }
    
    // Prepare QStash publish options
    const publishOptions = {
      url: job.endpoint.startsWith('http') 
        ? job.endpoint 
        : `${process.env.NEXT_PUBLIC_APP_URL}${job.endpoint}`,
      body: {
        ...job.payload,
        batchId: batchPayload.batchId,
        batchJobId: job.id
      },
      delay: job.delay || 0,
      retries: batchPayload.maxRetries || 3,
      failureCallback: batchPayload.failureCallback,
      deduplicationId: `batch_${batchPayload.batchId}_job_${job.id}`
    };
    
    // Publish job to QStash
    const result = await publishToQStash(publishOptions);
    
    console.log(`Successfully queued job ${job.id} with message ID ${result.messageId}`);
    
    return {
      jobId: job.id,
      status: 'success',
      messageId: result.messageId
    };
    
  } catch (error) {
    console.error(`Error processing job ${job.id}:`, error);
    return {
      jobId: job.id,
      status: 'failed',
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

// ============================================================================
// Batch Utilities
// ============================================================================

async function getBatchStatus(batchId: string): Promise<BatchJobRecord | null> {
  const supabase = getSupabaseClient();
  
  try {
    const { data, error } = await supabase
      .from('batch_jobs')
      .select('*')
      .eq('batch_id', batchId)
      .single();
    
    if (error) {
      if (error.code === 'PGRST116') {
        return null; // Batch not found
      }
      throw new Error(`Failed to get batch status: ${error.message}`);
    }
    
    return data;
  } catch (error) {
    console.error('Error getting batch status:', error);
    throw error;
  }
}

async function cancelBatch(batchId: string): Promise<void> {
  const supabase = getSupabaseClient();
  
  try {
    const { error } = await supabase
      .from('batch_jobs')
      .update({
        status: 'cancelled',
        completed_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .eq('batch_id', batchId);
    
    if (error) {
      throw new Error(`Failed to cancel batch: ${error.message}`);
    }
    
    console.log(`Batch ${batchId} cancelled`);
  } catch (error) {
    console.error('Error cancelling batch:', error);
    throw error;
  }
}

// ============================================================================
// Worker Handler
// ============================================================================

async function batchProcessorHandler(req: NextRequest): Promise<Response> {
  logWorkerRequest(req, 'BatchProcessor');

  const result = await processWorkerJob(req, async (payload: WorkerJobPayload) => {
    const batchPayload = payload as BatchJobPayload;
    // Validate payload
    if (!batchPayload.batchId || !batchPayload.jobs || batchPayload.jobs.length === 0) {
      throw new Error('Missing required fields: batchId or jobs array is empty');
    }

    console.log(`Starting batch processing for batch ${batchPayload.batchId} with ${batchPayload.jobs.length} jobs`);

    // Check if batch already exists
    const existingBatch = await getBatchStatus(batchPayload.batchId);
    if (existingBatch && existingBatch.status === 'completed') {
      console.log(`Batch ${batchPayload.batchId} already completed`);
      return {
        batchId: batchPayload.batchId,
        totalJobs: existingBatch.total_jobs,
        successfulJobs: existingBatch.completed_jobs,
        failedJobs: existingBatch.failed_jobs,
        skippedJobs: 0,
        processingTimeMs: 0,
        results: [],
        message: 'Batch already completed (idempotent)'
      };
    }

    // Process the batch
    const result = await processBatchJobs(batchPayload);

    console.log(`Batch ${batchPayload.batchId} processing completed:`, {
      totalJobs: result.totalJobs,
      successfulJobs: result.successfulJobs,
      failedJobs: result.failedJobs,
      processingTimeMs: result.processingTimeMs
    });
    
    return result;
  });

  return createWorkerResponse(result);
}

// ============================================================================
// Route Handlers
// ============================================================================

export const POST = createSecureWorkerHandler(batchProcessorHandler);

export async function GET() {
  return createErrorResponse({
    success: false,
    error: {
      message: 'Method not allowed. This endpoint only accepts POST requests from QStash.',
      code: 'METHOD_NOT_ALLOWED'
    }
  }, 405);
}

export async function PUT() {
  return createErrorResponse({
    success: false,
    error: {
      message: 'Method not allowed. This endpoint only accepts POST requests from QStash.',
      code: 'METHOD_NOT_ALLOWED'
    }
  }, 405);
}

export async function DELETE() {
  return createErrorResponse({
    success: false,
    error: {
      message: 'Method not allowed. This endpoint only accepts POST requests from QStash.',
      code: 'METHOD_NOT_ALLOWED'
    }
  }, 405);
}
