/**
 * X (Twitter) OAuth Initiate Endpoint
 * 
 * Initiates X OAuth 1.0a authentication flow
 * POST /api/connect/x/initiate
 */

import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { XAuthService } from '@/lib/services/xAuthService';
import { 
  with<PERSON><PERSON>r<PERSON><PERSON><PERSON>, 
  requireAuth,
  ErrorFactory
} from '@/lib/error-handler';

/**
 * Internal handler for X OAuth initiation
 */
async function xOAuthInitiateHandler(req: NextRequest) {
  try {
    // 1. Authenticate user
    const { user } = await requireAuth(req);

    // 2. Get redirect URI from environment or construct it
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || process.env.NEXTAUTH_URL || 'http://localhost:3002';
    const redirectUri = `${baseUrl}/api/connect/x/callback`;

    // 3. Initialize X authentication service
    const supabase = createClient();
    const xAuthService = new XAuthService({
      supabase,
      supabaseAdmin: supabase // Using same client for now
    });

    // 4. Generate OAuth authorization URL
    const result = await xAuthService.generateAuthUrl(user.id, redirectUri);

    if (!result.success) {
      throw result.error;
    }

    const { authUrl, state } = result.data!;

    // 5. Return authorization URL and state
    return NextResponse.json({
      success: true,
      data: {
        authUrl,
        state,
        provider: 'x',
        redirectUri
      }
    });

  } catch (error) {
    console.error('X OAuth initiate error:', error);
    
    // Handle different error types
    if (error && typeof error === 'object' && 'code' in error) {
      // This is an AppError from our error handling system
      const appError = error as any; // Type assertion for error object
      return NextResponse.json({
        success: false,
        error: {
          message: appError.message || 'Failed to initiate X OAuth',
          code: appError.code || 'OAUTH_INITIATE_FAILED',
          details: appError.details || null
        }
      }, { status: appError.statusCode || 500 });
    }

    // Generic error handling
    return NextResponse.json({
      success: false,
      error: {
        message: 'Failed to initiate X OAuth',
        code: 'OAUTH_INITIATE_FAILED',
        details: error instanceof Error ? error.message : 'Unknown error'
      }
    }, { status: 500 });
  }
}

/**
 * POST handler with error wrapper
 */
export const POST = withErrorHandler(xOAuthInitiateHandler);

/**
 * Handle unsupported methods
 */
export async function GET() {
  return NextResponse.json({
    success: false,
    error: {
      message: 'Method not allowed. Use POST to initiate X OAuth.',
      code: 'METHOD_NOT_ALLOWED'
    }
  }, { status: 405 });
}

export async function PUT() {
  return NextResponse.json({
    success: false,
    error: {
      message: 'Method not allowed. Use POST to initiate X OAuth.',
      code: 'METHOD_NOT_ALLOWED'
    }
  }, { status: 405 });
}

export async function DELETE() {
  return NextResponse.json({
    success: false,
    error: {
      message: 'Method not allowed. Use POST to initiate X OAuth.',
      code: 'METHOD_NOT_ALLOWED'
    }
  }, { status: 405 });
}
