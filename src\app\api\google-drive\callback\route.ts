import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { createClient } from '@/lib/supabase/server';

export async function GET(request: NextRequest) {
  console.log('Google Drive auth callback handler');
  
  try {
    // Create a Supabase client
    const supabase = createClient();
    
    // Get the current session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    
    // Keep track of user ID throughout the function
    let userId: string | null = null;
    
    if (sessionError || !session) {
      console.error('No authenticated session found in callback:', sessionError);
      
      // Try to restore the session from cookies
      const cookieStore = cookies();
      const sessionCookie = cookieStore.get('drive-session');
      
      if (sessionCookie) {
        console.log('Found drive-session cookie, attempting to use it');
        // Attempt to get user by ID
        const { data: user } = await supabase.auth.admin.getUserById(sessionCookie.value);
        
        if (user && user.user) {
          console.log('Session restored from cookie for user:', user.user.id);
          // Continue with this user ID
          userId = user.user.id;
        } else {
          return NextResponse.json({ 
            error: 'Authentication required',
            message: 'Please sign in to connect Google Drive'
          }, { status: 401 });
        }
      } else {
        return NextResponse.json({ 
          error: 'Authentication required',
          message: 'Please sign in to connect Google Drive'
        }, { status: 401 });
      }
    } else {
      // Use the user ID from the session
      userId = session.user.id;
    }

    // At this point, we should have a valid userId or have returned an error
    if (!userId) {
      return NextResponse.json({ 
        error: 'Authentication required',
        message: 'Failed to identify user'
      }, { status: 401 });
    }

    // Get the code and scope from the URL
    const searchParams = request.nextUrl.searchParams;
    const code = searchParams.get('code');
    const scope = searchParams.get('scope');
    const stateParam = searchParams.get('state');
    
    let returnTo = '/';
    
    // Parse the state parameter if it exists
    if (stateParam) {
      try {
        // Try to parse it as JSON first (new format)
        const stateObj = JSON.parse(stateParam);
        if (stateObj && stateObj.returnTo) {
          returnTo = stateObj.returnTo;
          console.log('Return path from state JSON:', returnTo);
        }
      } catch (e) {
        // If it's not JSON, use it directly as the return path (old format)
        returnTo = stateParam;
        console.log('Return path from state string:', returnTo);
      }
    }
    
    console.log('Google Drive callback received with code and scope, return path:', returnTo);
    
    if (!code) {
      console.error('No authorization code found in callback');
      return Response.redirect(`${request.nextUrl.origin}${returnTo}?drive_error=no_code`);
    }
    
    // Get the access token using the code
    const tokenEndpoint = 'https://oauth2.googleapis.com/token';
    const clientId = process.env.GOOGLE_CLIENT_ID;
    const clientSecret = process.env.GOOGLE_CLIENT_SECRET;
    const redirectUri = `${request.nextUrl.origin}/api/google-drive/callback`;
    
    console.log('Exchanging code for token with redirect URI:', redirectUri);
    
    const tokenResponse = await fetch(tokenEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        code,
        client_id: clientId || '',
        client_secret: clientSecret || '',
        redirect_uri: redirectUri,
        grant_type: 'authorization_code',
      }),
    });
    
    if (!tokenResponse.ok) {
      const errorText = await tokenResponse.text();
      console.error('Failed to exchange code for token:', errorText);
      return Response.redirect(`${request.nextUrl.origin}${returnTo}?drive_error=token_exchange_failed`);
    }
    
    const tokenData = await tokenResponse.json();
    console.log('Token exchange successful, received tokens');
    
    // Get user info to store email
    let userEmail = null;
    try {
      const userInfoResponse = await fetch('https://www.googleapis.com/oauth2/v2/userinfo', {
        headers: {
          Authorization: `Bearer ${tokenData.access_token}`,
        },
      });
      
      if (userInfoResponse.ok) {
        const userData = await userInfoResponse.json();
        userEmail = userData.email;
        console.log('Retrieved user email from Google:', userEmail);
      }
    } catch (userInfoError) {
      console.error('Error fetching user info:', userInfoError);
    }
    
    // Store the tokens in the connected_accounts table
    console.log('Storing Drive connection for user:', userId);
    
    // First check if there's an existing record (check both formats)
    const { data: existingDriveAccounts, error: driveCheckError } = await supabase
      .from('connected_accounts')
      .select('id')
      .eq('user_id', userId)
      .eq('provider', 'google_drive');
      
    if (driveCheckError) {
      console.error('Error checking existing google_drive accounts:', driveCheckError);
    }
      
    const { data: existingLegacyAccounts, error: legacyCheckError } = await supabase
      .from('connected_accounts')
      .select('id')
      .eq('user_id', userId)
      .eq('provider', 'google')
      .eq('service_type', 'drive');
    
    if (legacyCheckError) {
      console.error('Error checking existing legacy Drive accounts:', legacyCheckError);
    }
    
    const totalExistingAccounts = 
      (existingDriveAccounts?.length || 0) + 
      (existingLegacyAccounts?.length || 0);
    
    if (totalExistingAccounts > 0) {
      console.log(`Found ${totalExistingAccounts} existing Drive accounts, removing them first`);
      
      // Delete all existing accounts to avoid duplicates
      if (existingDriveAccounts && existingDriveAccounts.length > 0) {
        const { error: driveDeleteError } = await supabase
          .from('connected_accounts')
          .delete()
          .eq('user_id', userId)
          .eq('provider', 'google_drive');
          
        if (driveDeleteError) {
          console.error('Error deleting existing google_drive accounts:', driveDeleteError);
        } else {
          console.log(`Successfully deleted ${existingDriveAccounts.length} google_drive accounts`);
        }
      }
      
      if (existingLegacyAccounts && existingLegacyAccounts.length > 0) {
        const { error: legacyDeleteError } = await supabase
          .from('connected_accounts')
          .delete()
          .eq('user_id', userId)
          .eq('provider', 'google')
          .eq('service_type', 'drive');
          
        if (legacyDeleteError) {
          console.error('Error deleting existing legacy Drive accounts:', legacyDeleteError);
        } else {
          console.log(`Successfully deleted ${existingLegacyAccounts.length} legacy Drive accounts`);
        }
      }
    }
    
    // Create a new expiration timestamp with a buffer
    const now = Math.floor(Date.now() / 1000);
    const expiresAt = now + tokenData.expires_in - 300; // 5 minutes buffer
    
    // Insert the new connection - using consistent 'google_drive' format
    const { error: insertError } = await supabase
      .from('connected_accounts')
      .insert({
        user_id: userId,
        provider: 'google_drive',
        service_type: 'drive',
        provider_account_email: userEmail,
        access_token: tokenData.access_token,
        refresh_token: tokenData.refresh_token,
        expires_at: expiresAt,
        scope: scope,
        token_type: tokenData.token_type,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      });
    
    if (insertError) {
      console.error('Error storing Drive tokens:', insertError);
      return Response.redirect(`${request.nextUrl.origin}${returnTo}?drive_error=storage_failed`);
    }
    
    console.log('Drive connection stored successfully');
    
    // Store session cookies to maintain session through redirects
    const cookieStore = cookies();
    cookieStore.set('drive-session', userId, {
      path: '/',
      maxAge: 60 * 60 * 24, // 1 day
      sameSite: 'lax',
      secure: process.env.NODE_ENV === 'production',
    });
    
    // Create a session cookie for the Drive connected account
    const { data: newAccount } = await supabase
      .from('connected_accounts')
      .select('id')
      .eq('user_id', userId)
      .eq('service_type', 'drive')
      .single();
      
    if (newAccount?.id) {
      cookieStore.set('drive-token-id', newAccount.id, {
        path: '/',
        maxAge: 60 * 60 * 24, // 1 day
        sameSite: 'lax',
        secure: process.env.NODE_ENV === 'production',
      });
    }
    
    console.log('Drive connection stored successfully with session cookies');
    
    // Redirect back to the app
    return Response.redirect(`${request.nextUrl.origin}${returnTo}?drive_connected=true`);
    
  } catch (error) {
    console.error('Error in Drive callback handler:', error);
    return Response.redirect(`${request.nextUrl.origin}/publish?drive_error=server_error`);
  }
} 