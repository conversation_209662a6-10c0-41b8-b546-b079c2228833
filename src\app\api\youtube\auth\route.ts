import { NextRequest, NextResponse } from 'next/server';
import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';
import { google } from 'googleapis';

// YouTube specific OAuth configuration
const GOOGLE_CLIENT_ID = process.env.GOOGLE_CLIENT_ID || '';
const GOOGLE_CLIENT_SECRET = process.env.GOOGLE_CLIENT_SECRET || '';
// Use a separate redirect URI specifically for YouTube
const REDIRECT_URI = process.env.YOUTUBE_REDIRECT_URI || 'http://localhost:3002/youtube-callback';

// YouTube specific scopes - including analytics scopes
const YOUTUBE_SCOPES = [
  'https://www.googleapis.com/auth/youtube.readonly',
  'https://www.googleapis.com/auth/youtube.upload',
  'https://www.googleapis.com/auth/youtube.force-ssl',
  'https://www.googleapis.com/auth/youtube',
  'https://www.googleapis.com/auth/userinfo.email',
  'https://www.googleapis.com/auth/userinfo.profile',
  'https://www.googleapis.com/auth/yt-analytics.readonly',
  'https://www.googleapis.com/auth/yt-analytics-monetary.readonly'
];

export async function GET(request: NextRequest) {
  const requestUrl = new URL(request.url);
  const returnTo = requestUrl.searchParams.get('returnTo') || '/publish';
  
  console.log('🎬 YouTube auth handler called with returnTo:', returnTo);
  
  try {
    // Create a Supabase client for the route handler
    const cookieStore = cookies();
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value;
          },
        },
      }
    );
    
    // Get the current session
    const { data: { session }, error } = await supabase.auth.getSession();
    
    if (error || !session) {
      console.error('❌ No authenticated session found:', error);
      return NextResponse.redirect(
        new URL(`/auth/signin?error=${encodeURIComponent('Authentication required')}&returnTo=${encodeURIComponent(returnTo)}`, request.url)
      );
    }
    
    console.log('✅ Session found, user authenticated:', session.user.id);
    
    // Initialize OAuth client with YouTube-specific redirect URI
    const oauth2Client = new google.auth.OAuth2(
      GOOGLE_CLIENT_ID,
      GOOGLE_CLIENT_SECRET,
      REDIRECT_URI
    );
    
    // Generate authentication URL with YouTube scopes
    const authUrl = oauth2Client.generateAuthUrl({
      access_type: 'offline',
      scope: YOUTUBE_SCOPES,
      prompt: 'consent', // Force to always get refresh token
      state: JSON.stringify({ 
        userId: session.user.id, 
        returnTo,
        provider: 'youtube' // Mark this as YouTube auth specifically
      })
    });
    
    console.log('🔗 Redirecting to YouTube auth URL');
    return NextResponse.redirect(authUrl);
  } catch (error: any) {
    console.error('❌ Error in YouTube auth handler:', error);
    return NextResponse.redirect(
      new URL(`/auth/signin?error=${encodeURIComponent(error.message || 'Unknown error')}&returnTo=${encodeURIComponent(returnTo)}`, request.url)
    );
  }
} 