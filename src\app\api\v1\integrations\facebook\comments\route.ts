/**
 * API v1 - Facebook Comments Route
 * 
 * Fetches and manages comments for Facebook posts
 */

import { NextRequest } from 'next/server';
import { FacebookService } from '@/lib/services/facebookService';
import { 
  with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 
  requireAuth,
  ErrorFactory
} from '@/lib/error-handler';
import {
  validateQueryParams
} from '@/lib/validation';
import { withVersioning } from '@/lib/versioning/middleware';
import { z } from 'zod';

// ============================================================================
// Validation Schemas
// ============================================================================

const FacebookCommentsQuerySchema = z.object({
  integration_id: z.string().uuid('Invalid integration ID'),
  platform_post_id: z.string().min(1, 'Platform post ID is required'),
  refresh: z.string().default('false'),
  include_replies: z.string().default('true')
}).strict();

// ============================================================================
// Route Handler
// ============================================================================

/**
 * Internal handler for Facebook comments
 */
async function facebookCommentsHandler(req: NextRequest) {
  // 1. Authenticate user
  const { user } = await requireAuth(req);

  // 2. Validate query parameters
  const queryParams = validateQueryParams(req, FacebookCommentsQuerySchema);

  // 3. Convert string parameters to booleans
  const refresh = queryParams.refresh === 'true';
  const includeReplies = queryParams.include_replies === 'true';

  // 4. Initialize Facebook service
  const { facebookService } = await import('@/lib/services');

  // 5. Check if we should fetch fresh data or use cached
  let commentsData;
  let fromCache = false;

  if (!refresh) {
    // Try to get cached comments first
    const cachedResult = await getCachedComments(
      queryParams.integration_id,
      queryParams.platform_post_id
    );
    
    if (cachedResult.success && cachedResult.data) {
      commentsData = cachedResult.data;
      fromCache = true;
    }
  }

  // 6. Fetch fresh comments if no cache or refresh requested
  if (!commentsData) {
    const commentsResult = await facebookService.fetchComments(
      queryParams.integration_id,
      queryParams.platform_post_id
    );

    if (!commentsResult.success) {
      throw commentsResult.error;
    }

    commentsData = commentsResult.data!;

    // Store comments in database
    if (commentsData.length > 0) {
      await facebookService.storeComments(
        queryParams.integration_id,
        queryParams.platform_post_id,
        commentsData
      );
    }
  }

  // 7. Process comments data
  const processedComments = processCommentsData(commentsData, includeReplies);

  // 8. Return comments data
  return {
    success: true,
    data: {
      comments: processedComments.comments,
      total_comments: processedComments.totalComments,
      total_replies: processedComments.totalReplies,
      engagement_summary: processedComments.engagementSummary
    },
    metadata: {
      provider: 'facebook',
      integration_id: queryParams.integration_id,
      platform_post_id: queryParams.platform_post_id,
      from_cache: fromCache,
      include_replies: includeReplies,
      fetched_at: new Date().toISOString(),
      comments_count: processedComments.comments.length
    }
  };
}

// ============================================================================
// Helper Functions
// ============================================================================

/**
 * Get cached comments from database
 */
async function getCachedComments(integrationId: string, platformPostId: string) {
  try {
    const { createClient } = await import('@supabase/supabase-js');
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );

    const result = await supabase
      .from('platform_comments')
      .select('*')
      .eq('account_id', integrationId)
      .eq('platform_post_id', platformPostId)
      .gte('created_at', new Date(Date.now() - 60 * 60 * 1000).toISOString()) // Cache for 1 hour
      .order('comment_timestamp', { ascending: true });

    if (result.error || !result.data || result.data.length === 0) {
      return { success: false, data: null };
    }

    // Convert database format to Facebook API format
    const comments = result.data.map(comment => ({
      id: comment.platform_comment_id,
      message: comment.comment_text,
      from: {
        id: comment.author_id,
        name: comment.author_name,
        picture: comment.author_avatar
      },
      created_time: comment.comment_timestamp,
      like_count: comment.likes_count,
      comment_count: comment.replies_count,
      parent: comment.parent_comment_id ? { id: comment.parent_comment_id } : undefined,
      can_hide: comment.metadata?.can_hide || false,
      can_remove: comment.metadata?.can_remove || false
    }));

    return { success: true, data: comments };
  } catch (error) {
    return { success: false, data: null };
  }
}

/**
 * Process comments data for response
 */
function processCommentsData(comments: any[], includeReplies: boolean) {
  let totalComments = 0;
  let totalReplies = 0;
  let totalLikes = 0;
  const processedComments = [];

  for (const comment of comments) {
    // Check if this is a reply (has parent)
    const isReply = !!comment.parent;
    
    if (isReply) {
      totalReplies++;
      
      // Only include replies if requested
      if (!includeReplies) {
        continue;
      }
    } else {
      totalComments++;
    }

    totalLikes += comment.like_count || 0;

    processedComments.push({
      id: comment.id,
      text: comment.message,
      author: {
        id: comment.from.id,
        name: comment.from.name,
        picture: comment.from.picture
      },
      created_time: comment.created_time,
      like_count: comment.like_count || 0,
      reply_count: comment.comment_count || 0,
      parent_id: comment.parent?.id,
      is_reply: isReply,
      can_hide: comment.can_hide || false,
      can_remove: comment.can_remove || false,
      sentiment: analyzeSentiment(comment.message) // Basic sentiment analysis
    });
  }

  const engagementSummary = {
    total_likes: totalLikes,
    average_likes_per_comment: totalComments > 0 ? Math.round(totalLikes / totalComments) : 0,
    engagement_rate: calculateEngagementRate(totalComments, totalReplies, totalLikes),
    most_liked_comment: processedComments.length > 0
      ? processedComments.reduce((max, comment) =>
          comment.like_count > max.like_count ? comment : max
        )
      : null
  };

  return {
    comments: processedComments,
    totalComments,
    totalReplies,
    engagementSummary
  };
}

/**
 * Basic sentiment analysis
 */
function analyzeSentiment(text: string): 'positive' | 'negative' | 'neutral' {
  const positiveWords = ['good', 'great', 'awesome', 'amazing', 'love', 'like', 'excellent', 'fantastic', 'wonderful'];
  const negativeWords = ['bad', 'terrible', 'awful', 'hate', 'dislike', 'horrible', 'worst', 'disappointing'];
  
  const lowerText = text.toLowerCase();
  const positiveCount = positiveWords.filter(word => lowerText.includes(word)).length;
  const negativeCount = negativeWords.filter(word => lowerText.includes(word)).length;
  
  if (positiveCount > negativeCount) return 'positive';
  if (negativeCount > positiveCount) return 'negative';
  return 'neutral';
}

/**
 * Calculate engagement rate
 */
function calculateEngagementRate(comments: number, replies: number, likes: number): number {
  const totalEngagement = comments + replies + likes;
  if (totalEngagement === 0) return 0;
  
  // This is a simplified calculation - in reality you'd need post reach/impressions
  return Math.round((totalEngagement / Math.max(comments, 1)) * 100) / 100;
}

/**
 * GET /api/v1/integrations/facebook/comments
 * Fetch Facebook post comments
 */
export const GET = withVersioning(withErrorHandler(facebookCommentsHandler));
