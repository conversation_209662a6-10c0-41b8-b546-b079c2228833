// Following QStash documentation exactly
import { promises as fs } from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __dirname = path.dirname(fileURLToPath(import.meta.url));

async function main() {
  try {
    // Read .env file directly
    const envPath = path.resolve(__dirname, '.env');
    const envContent = await fs.readFile(envPath, 'utf-8');
    
    // Extract QStash token
    const tokenMatch = envContent.match(/QSTASH_TOKEN="(.+)"/);
    if (!tokenMatch) {
      console.error("Failed to find QSTASH_TOKEN in .env file");
      return;
    }
    const qstashToken = tokenMatch[1];
    console.log(`Found QStash token: ${qstashToken.substring(0, 5)}...`);
    
    // Define destination URL
    const destinationUrl = "https://7861-2404-8ec0-4-2d98-a950-a216-4887-d919.ngrok-free.app/api/qstash-test";
    console.log(`Using destination URL: ${destinationUrl}`);
    
    // Create message payload - simple string payload per their documentation
    const payload = "Hello World!";
    
    // Following their documentation example exactly:
    // https://upstash.com/docs/qstash/quickstarts/nodejs
    console.log('Sending request to QStash following documentation...');
    
    // Method 1: Using the URL directly in the path
    console.log('\nMethod 1: URL in path');
    const response1 = await fetch(`https://qstash.upstash.io/v2/publish/${encodeURIComponent(destinationUrl)}`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${qstashToken}`,
        'Content-Type': 'text/plain'
      },
      body: payload
    });
    
    console.log(`Response 1 status: ${response1.status} ${response1.statusText}`);
    const responseBody1 = await response1.text();
    console.log(`Response 1 body: ${responseBody1}`);
    
    // Method 2: Using the Upstash-Forward-Url header
    console.log('\nMethod 2: Upstash-Forward-Url header');
    const response2 = await fetch('https://qstash.upstash.io/v2/publish', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${qstashToken}`,
        'Content-Type': 'text/plain',
        'Upstash-Forward-Url': destinationUrl
      },
      body: payload
    });
    
    console.log(`Response 2 status: ${response2.status} ${response2.statusText}`);
    const responseBody2 = await response2.text();
    console.log(`Response 2 body: ${responseBody2}`);
    
    // Method 3: Using the URL as a query parameter
    console.log('\nMethod 3: URL as query parameter');
    const response3 = await fetch(`https://qstash.upstash.io/v2/publish?url=${encodeURIComponent(destinationUrl)}`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${qstashToken}`,
        'Content-Type': 'text/plain'
      },
      body: payload
    });
    
    console.log(`Response 3 status: ${response3.status} ${response3.statusText}`);
    const responseBody3 = await response3.text();
    console.log(`Response 3 body: ${responseBody3}`);
    
  } catch (error) {
    console.error('Error:', error);
  }
}

main(); 