# Supabase Database Setup

This directory contains the database schema and migrations for the social media management application.

## Database Schema

The database consists of the following tables:

1. **profiles** - User profiles that extend Supabase auth.users
2. **connected_accounts** - Social media accounts connected to a user's profile
3. **media_assets** - Media files uploaded by users (images, videos)
4. **posts** - Social media posts created by users
5. **post_media** - Junction table linking posts to media assets
6. **post_channels** - Tracks which social channels a post is published to
7. **tags** - User-defined tags for organizing posts
8. **post_tags** - Junction table linking posts to tags

## Entity Relationship Diagram

```
profiles
  ↑
  |
  +--> connected_accounts <-+
  |                         |
  +--> media_assets         |
  |         ↑               |
  |         |               |
  +--> posts +-----+--------+
  |     ↑          |
  |     |          |
  |     |          v
  +--> tags <---- post_tags
        ↑
        |
post_media
```

## Row Level Security (RLS)

All tables have Row Level Security (RLS) policies enabled to ensure that users can only access their own data. The policies are set up to allow users to:

- View their own data
- Insert their own data
- Update their own data
- Delete their own data

## How to Apply Migrations

### Using Supabase CLI

1. Install the Supabase CLI:
   ```bash
   npm install -g supabase
   ```

2. Link your project:
   ```bash
   supabase link --project-ref vqslqthqmfuuussmezav
   ```

3. Apply migrations:
   ```bash
   supabase db push
   ```

### Manual Application

If you prefer to apply the migrations manually:

1. Navigate to the Supabase dashboard: https://app.supabase.io
2. Select your project
3. Go to the SQL Editor
4. Copy the contents of the migration files in the `migrations` directory
5. Paste and execute the SQL in the editor

## Working with the Database

### TypeScript Types

You can generate TypeScript types for your database schema using the Supabase CLI:

```bash
supabase gen types typescript --linked > src/types/supabase.ts
```

### Example Queries

#### Get User's Posts

```typescript
const { data: posts, error } = await supabase
  .from('posts')
  .select(`
    *,
    post_media (
      media_assets (*)
    ),
    post_channels (
      connected_accounts (*)
    ),
    post_tags (
      tags (*)
    )
  `)
  .order('created_at', { ascending: false });
```

#### Create a New Post

```typescript
const { data: post, error } = await supabase
  .from('posts')
  .insert({
    user_id: user.id,
    content: 'Hello world!',
    status: 'draft'
  })
  .select()
  .single();
```

#### Connect a Post to a Channel

```typescript
const { data, error } = await supabase
  .from('post_channels')
  .insert({
    post_id: postId,
    account_id: connectedAccountId,
    status: 'pending'
  });
```

## Troubleshooting

If you encounter issues with the database setup:

1. Check the Supabase logs in the dashboard
2. Verify that all migrations have been applied correctly
3. Ensure that your application has the correct permissions to access the database
4. Check that your environment variables are set correctly 