/**
 * LinkedIn Pages Selection Endpoint
 * 
 * Handles Company Page selection and connection finalization
 * POST /api/connect/linkedin/finalize-pages
 */

import { NextRequest, NextResponse } from 'next/server';
import { createServerComponentClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { LinkedInAuthService } from '@/lib/services/linkedinAuthService';
import { 
  withError<PERSON><PERSON><PERSON>, 
  requireAuth,
  ErrorFactory
} from '@/lib/error-handler';

/**
 * Request body interface for LinkedIn pages selection
 */
interface LinkedInPagesSelectionRequest {
  selectedPageIds: string[];
  accessToken: string;
  refreshToken?: string;
  expiresIn: number;
}

/**
 * Internal handler for LinkedIn pages selection
 */
async function linkedinPagesSelectionHandler(req: NextRequest) {
  try {
    // 1. Authenticate user
    const { user } = await requireAuth(req);

    // 2. Parse request body
    const requestBody: LinkedInPagesSelectionRequest = await req.json();
    const { selectedPageIds, accessToken, refreshToken, expiresIn } = requestBody;

    // 3. Validate required fields
    if (!selectedPageIds || !Array.isArray(selectedPageIds) || selectedPageIds.length === 0) {
      return NextResponse.json({
        success: false,
        error: {
          message: 'At least one page must be selected',
          code: 'INVALID_REQUEST'
        }
      }, { status: 400 });
    }

    if (!accessToken) {
      return NextResponse.json({
        success: false,
        error: {
          message: 'Access token is required',
          code: 'INVALID_REQUEST'
        }
      }, { status: 400 });
    }

    // 4. Initialize LinkedIn authentication service
    const supabase = createServerComponentClient({ cookies });
    const linkedinAuthService = new LinkedInAuthService({
      supabase,
      supabaseAdmin: supabase
    });

    // 5. Fetch all available company pages
    const companyPagesResult = await linkedinAuthService.fetchCompanyPages(accessToken);
    if (!companyPagesResult.success) {
      return NextResponse.json({
        success: false,
        error: {
          message: 'Failed to fetch company pages',
          code: 'EXTERNAL_SERVICE_ERROR',
          details: companyPagesResult.error?.message || 'Unknown error'
        }
      }, { status: 500 });
    }

    const allCompanyPages = companyPagesResult.data!;

    // 6. Filter selected pages and validate permissions
    const selectedPages = allCompanyPages.filter(page => selectedPageIds.includes(page.id));
    
    if (selectedPages.length !== selectedPageIds.length) {
      const missingPageIds = selectedPageIds.filter(id => !selectedPages.find(page => page.id === id));
      return NextResponse.json({
        success: false,
        error: {
          message: 'Some selected pages are not available or you lack permissions',
          code: 'INVALID_PERMISSIONS',
          details: { missingPageIds }
        }
      }, { status: 403 });
    }

    // 7. Calculate token expiry
    const expiresAt = new Date(Date.now() + expiresIn * 1000);

    // 8. Store or update company page connections
    const results = [];
    for (const page of selectedPages) {
      try {
        // Check if connection already exists
        const { data: existingConnection } = await supabase
          .from('connected_accounts')
          .select('id, status')
          .eq('user_id', user.id)
          .eq('provider', 'linkedin')
          .eq('service_type', 'company_page')
          .eq('provider_account_id', page.id)
          .single();

        const connectionData = {
          user_id: user.id,
          provider: 'linkedin',
          service_type: 'company_page',
          provider_account_id: page.id,
          account_username: page.name,
          platform_account_name: page.name,
          profile_picture_url: page.logoUrl,
          access_token: accessToken, // Will be encrypted by database trigger
          refresh_token: refreshToken,
          expires_at: expiresAt.toISOString(),
          scopes_granted: ['r_organization_social', 'w_organization_social', 'rw_organization_admin'],
          status: 'active',
          last_sync_at: new Date().toISOString(),
          metadata: {
            description: page.description,
            industry: page.industry,
            website: page.website,
            followerCount: page.followerCount,
            isAdmin: page.isAdmin,
            permissions: page.permissions
          }
        };

        let result;
        if (existingConnection) {
          // Update existing connection
          result = await supabase
            .from('connected_accounts')
            .update(connectionData)
            .eq('id', existingConnection.id)
            .select('id, platform_account_name, status')
            .single();
        } else {
          // Create new connection
          result = await supabase
            .from('connected_accounts')
            .insert(connectionData)
            .select('id, platform_account_name, status')
            .single();
        }

        if (result.error) {
          throw new Error(`Database error: ${result.error.message}`);
        }

        results.push({
          pageId: page.id,
          pageName: page.name,
          connectionId: result.data.id,
          status: 'connected',
          action: existingConnection ? 'updated' : 'created'
        });

      } catch (error) {
        console.error(`Failed to store connection for page ${page.name}:`, error);
        results.push({
          pageId: page.id,
          pageName: page.name,
          status: 'failed',
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    // 9. Deactivate any company page connections that weren't selected
    try {
      const { data: allUserCompanyConnections } = await supabase
        .from('connected_accounts')
        .select('id, provider_account_id')
        .eq('user_id', user.id)
        .eq('provider', 'linkedin')
        .eq('service_type', 'company_page')
        .eq('status', 'active');

      if (allUserCompanyConnections) {
        const connectionsToDeactivate = allUserCompanyConnections.filter(
          conn => !selectedPageIds.includes(conn.provider_account_id)
        );

        if (connectionsToDeactivate.length > 0) {
          const { error: deactivateError } = await supabase
            .from('connected_accounts')
            .update({ 
              status: 'inactive',
              updated_at: new Date().toISOString()
            })
            .in('id', connectionsToDeactivate.map(conn => conn.id));

          if (deactivateError) {
            console.error('Failed to deactivate unselected company pages:', deactivateError);
          }
        }
      }
    } catch (error) {
      console.error('Error managing unselected company pages:', error);
      // Don't fail the entire request for this
    }

    // 10. Return results
    const successCount = results.filter(r => r.status === 'connected').length;
    const failureCount = results.filter(r => r.status === 'failed').length;

    return NextResponse.json({
      success: true,
      data: {
        totalSelected: selectedPageIds.length,
        successCount,
        failureCount,
        results
      }
    });

  } catch (error) {
    console.error('LinkedIn pages selection error:', error);
    
    return NextResponse.json({
      success: false,
      error: {
        message: 'Failed to finalize LinkedIn pages selection',
        code: 'INTERNAL_ERROR',
        details: error instanceof Error ? error.message : 'Unknown error'
      }
    }, { status: 500 });
  }
}

/**
 * POST handler with error wrapper
 */
export const POST = withErrorHandler(linkedinPagesSelectionHandler);

/**
 * Handle unsupported methods
 */
export async function GET() {
  return NextResponse.json({
    success: false,
    error: {
      message: 'Method not allowed. Use POST to finalize LinkedIn pages selection.',
      code: 'METHOD_NOT_ALLOWED'
    }
  }, { status: 405 });
}

export async function PUT() {
  return NextResponse.json({
    success: false,
    error: {
      message: 'Method not allowed. Use POST to finalize LinkedIn pages selection.',
      code: 'METHOD_NOT_ALLOWED'
    }
  }, { status: 405 });
}

export async function DELETE() {
  return NextResponse.json({
    success: false,
    error: {
      message: 'Method not allowed. Use POST to finalize LinkedIn pages selection.',
      code: 'METHOD_NOT_ALLOWED'
    }
  }, { status: 405 });
}
