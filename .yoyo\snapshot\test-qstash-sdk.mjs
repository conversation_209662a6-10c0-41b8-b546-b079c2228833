import { Client } from "@upstash/qstash";
import { promises as fs } from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __dirname = path.dirname(fileURLToPath(import.meta.url));

async function main() {
  try {
    // Read and parse token from .env
    const envPath = path.resolve(__dirname, '.env');
    const envContent = await fs.readFile(envPath, 'utf-8');
    const tokenMatch = envContent.match(/QSTASH_TOKEN="(.+)"/);
    if (!tokenMatch) {
      console.error("Failed to find QSTASH_TOKEN in .env file");
      return;
    }
    const qstashToken = tokenMatch[1];
    
    // Initialize the QStash client
    console.log("Initializing QStash client...");
    const qstash = new Client({
      token: qstashToken
    });
    
    // Test with httpbin.org first to confirm basic functionality
    const testUrl = "https://httpbin.org/post";
    console.log(`\nTesting publication to ${testUrl}`);
    console.log("This is a public test endpoint that will echo back the request");
    
    const testPayload = {
      test: true,
      timestamp: new Date().toISOString(),
      message: "Hello from QStash SDK test"
    };
    
    console.log("Sending test payload:", JSON.stringify(testPayload, null, 2));
    
    try {
      const result = await qstash.publishJSON({
        url: testUrl,
        body: testPayload
      });
      
      console.log("\n✅ Successfully published to test endpoint!");
      console.log("QStash response:", result);
      
      // Now test with the ngrok URL
      const ngrokUrl = "https://7861-2404-8ec0-4-2d98-a950-a216-4887-d919.ngrok-free.app/api/qstash-test";
      console.log(`\nNow testing publication to your ngrok URL: ${ngrokUrl}`);
      
      try {
        const ngrokResult = await qstash.publishJSON({
          url: ngrokUrl,
          body: {
            test: true,
            timestamp: new Date().toISOString(),
            message: "Hello from QStash SDK to ngrok"
          }
        });
        
        console.log("\n✅ Successfully published to ngrok endpoint!");
        console.log("QStash response:", ngrokResult);
        console.log("\nYour QStash integration is working correctly with the SDK!");
        console.log("Check your application logs to confirm the message was received.");
      } catch (ngrokError) {
        console.log("\n❌ Failed to publish to ngrok endpoint");
        console.log("Error:", ngrokError.message);
        console.log("This suggests an issue with the ngrok URL specifically.");
        console.log("\nRecommendations:");
        console.log("1. Check if your ngrok tunnel is still active");
        console.log("2. Try using a different ngrok URL");
        console.log("3. Ensure your Next.js application is running and the endpoint is accessible");
      }
    } catch (testError) {
      console.log("\n❌ Failed to publish to test endpoint");
      console.log("Error:", testError.message);
      console.log("\nThis suggests a fundamental issue with your QStash setup.");
      console.log("Please check your QStash account status and token validity.");
    }
  } catch (error) {
    console.error('Error:', error);
  }
}

main(); 