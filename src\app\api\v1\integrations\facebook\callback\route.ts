/**
 * API v1 - Facebook Integration Callback Route
 * 
 * Handles Facebook OAuth callback and completes integration
 */

import { NextRequest, NextResponse } from 'next/server';
import { FacebookService } from '@/lib/services/facebookService';
import { 
  with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 
  requireAuth,
  ErrorFactory
} from '@/lib/error-handler';
import { withVersioning } from '@/lib/versioning/middleware';

/**
 * Internal handler for Facebook OAuth callback
 */
async function facebookCallbackHandler(req: NextRequest) {
  const url = new URL(req.url);
  const code = url.searchParams.get('code');
  const state = url.searchParams.get('state');
  const error = url.searchParams.get('error');
  const errorDescription = url.searchParams.get('error_description');

  // 1. Check for OAuth errors
  if (error) {
    const frontendUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';
    const errorUrl = `${frontendUrl}/integrations?error=${encodeURIComponent(error)}&description=${encodeURIComponent(errorDescription || 'Facebook OAuth failed')}`;
    return NextResponse.redirect(errorUrl);
  }

  // 2. Validate required parameters
  if (!code || !state) {
    const frontendUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';
    const errorUrl = `${frontendUrl}/integrations?error=invalid_request&description=${encodeURIComponent('Missing authorization code or state')}`;
    return NextResponse.redirect(errorUrl);
  }

  try {
    // 3. Authenticate user (this will validate the session)
    const { user } = await requireAuth(req);

    // 4. Get redirect URI
    const baseUrl = process.env.NEXTAUTH_URL || process.env.VERCEL_URL || 'http://localhost:3000';
    const redirectUri = `${baseUrl}/api/v1/integrations/facebook/callback`;

    // 5. Initialize Facebook service
    const { facebookService } = await import('@/lib/services');

    // 6. Exchange code for access token
    const authResult = await facebookService.authenticate(code, state, redirectUri);

    if (!authResult.success) {
      const frontendUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';
      const errorMessage = authResult.error?.message || 'Authentication failed';
      const errorUrl = `${frontendUrl}/integrations?error=auth_failed&description=${encodeURIComponent(errorMessage)}`;
      return NextResponse.redirect(errorUrl);
    }

    const { accessToken, userInfo } = authResult.data!;

    // 7. Get user's Facebook pages
    const pagesResult = await facebookService.getPages(accessToken);

    if (!pagesResult.success) {
      const frontendUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';
      const errorUrl = `${frontendUrl}/integrations?error=pages_failed&description=${encodeURIComponent('Failed to fetch Facebook pages')}`;
      return NextResponse.redirect(errorUrl);
    }

    const pages = pagesResult.data!;

    if (pages.length === 0) {
      const frontendUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';
      const errorUrl = `${frontendUrl}/integrations?error=no_pages&description=${encodeURIComponent('No Facebook pages found. Please create a Facebook page first.')}`;
      return NextResponse.redirect(errorUrl);
    }

    // 8. If only one page, auto-connect it
    if (pages.length === 1) {
      const page = pages[0];
      const storeResult = await facebookService.storePageIntegration(user.id, page, accessToken);

      if (!storeResult.success) {
        const frontendUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';
        const errorUrl = `${frontendUrl}/integrations?error=store_failed&description=${encodeURIComponent('Failed to store Facebook page integration')}`;
        return NextResponse.redirect(errorUrl);
      }

      // Redirect to success page
      const frontendUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';
      const successUrl = `${frontendUrl}/integrations?success=facebook&page=${encodeURIComponent(page.name)}&integration_id=${storeResult.data}`;
      return NextResponse.redirect(successUrl);
    }

    // 9. Multiple pages - redirect to page selection
    const frontendUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';
    
    // Store temporary data for page selection (in a real app, you might use a temporary table or session storage)
    // For now, we'll pass the data as URL parameters (not ideal for production)
    const tempData = {
      user_id: user.id,
      access_token: accessToken, // In production, encrypt this
      pages: pages.map(p => ({ id: p.id, name: p.name, category: p.category, picture: p.picture }))
    };
    
    const selectUrl = `${frontendUrl}/integrations/facebook/select?data=${encodeURIComponent(btoa(JSON.stringify(tempData)))}`;
    return NextResponse.redirect(selectUrl);

  } catch (error) {
    console.error('Facebook callback error:', error);
    const frontendUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';
    const errorUrl = `${frontendUrl}/integrations?error=callback_failed&description=${encodeURIComponent('Facebook integration callback failed')}`;
    return NextResponse.redirect(errorUrl);
  }
}

/**
 * GET /api/v1/integrations/facebook/callback
 * Handle Facebook OAuth callback
 */
export const GET = withVersioning(withErrorHandler(facebookCallbackHandler));
