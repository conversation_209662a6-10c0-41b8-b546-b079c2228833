require('dotenv').config();
const https = require('https');
const fs = require('fs');
const path = require('path');

// Extract the QStash token from the .env file
const envContent = fs.readFileSync(path.resolve('.env'), 'utf8');
const tokenMatch = envContent.match(/QSTASH_TOKEN="(.+)"/);
if (!tokenMatch) {
  console.error("Failed to find QSTASH_TOKEN in .env file");
  process.exit(1);
}
const qstashToken = tokenMatch[1];

// Define the destination URL using the qstash-test endpoint
const destinationUrl = "https://7861-2404-8ec0-4-2d98-a950-a216-4887-d919.ngrok-free.app/api/qstash-test";
console.log(`Using destination URL: ${destinationUrl}`);

// Encode the URL explicitly for the query parameter
const encodedUrl = encodeURIComponent(destinationUrl);
console.log(`Encoded URL for query parameter: ${encodedUrl}`);

// Create the test message
const message = JSON.stringify({
  postId: 'test-post-id',
  timestamp: new Date().toISOString()
});

// Create request options using query parameter instead of header
const options = {
  hostname: 'qstash.upstash.io',
  port: 443,
  path: `/v2/publish?url=${encodedUrl}`,
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${qstashToken}`
  }
};

console.log(`Full request URL: https://${options.hostname}${options.path}`);
console.log("Request headers:");
for (const [key, value] of Object.entries(options.headers)) {
  if (key === 'Authorization') {
    console.log(`- ${key}: Bearer [REDACTED]`);
  } else {
    console.log(`- ${key}: ${value}`);
  }
}

// Make request
const req = https.request(options, (res) => {
  console.log(`\nResponse status code: ${res.statusCode}`);
  console.log('Response headers:');
  for (const [key, value] of Object.entries(res.headers)) {
    console.log(`- ${key}: ${value}`);
  }
  
  let data = '';
  res.on('data', (chunk) => {
    data += chunk;
  });
  
  res.on('end', () => {
    console.log(`\nResponse body: ${data}`);
    try {
      const jsonResponse = JSON.parse(data);
      console.log('Parsed JSON response:');
      console.log(JSON.stringify(jsonResponse, null, 2));
    } catch (e) {
      console.log('Response is not valid JSON');
    }
  });
});

req.on('error', (error) => {
  console.error(`\nRequest error: ${error.message}`);
});

// Send the request
console.log('\nSending request...');
req.write(message);
req.end(); 