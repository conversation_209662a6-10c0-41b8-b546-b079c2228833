/**
 * Utility functions for comment processing workers
 */

/**
 * Utility function to trigger comment processing via QStash
 */
export async function triggerCommentProcessing(
  videoId: string,
  connectionId: string,
  userId: string,
  delaySeconds: number = 0
) {
  try {
    // Import QStash client
    const { Client } = await import('@upstash/qstash');
    const qstashClient = new Client({ token: process.env.QSTASH_TOKEN! });

    // Schedule the comment processing job
    const result = await qstashClient.publishJSON({
      url: `${process.env.NEXT_PUBLIC_APP_URL}/api/workers/process-comments`,
      body: {
        videoId,
        connectionId,
        userId
      },
      delay: delaySeconds,
      retries: 2,
      failureCallback: `${process.env.NEXT_PUBLIC_APP_URL}/api/workers/failed-comment-handler`
    });

    console.log(`Comment processing job scheduled for video ${videoId}, message ID: ${result.messageId}`);
    return result.messageId;

  } catch (error) {
    console.error('Failed to trigger comment processing:', error);
    throw error;
  }
}
