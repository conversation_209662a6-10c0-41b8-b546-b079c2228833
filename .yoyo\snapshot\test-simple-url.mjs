import { promises as fs } from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __dirname = path.dirname(fileURLToPath(import.meta.url));

async function main() {
  try {
    // Read and parse token from .env
    const envPath = path.resolve(__dirname, '.env');
    const envContent = await fs.readFile(envPath, 'utf-8');
    const tokenMatch = envContent.match(/QSTASH_TOKEN="(.+)"/);
    if (!tokenMatch) {
      console.error("Failed to find QSTASH_TOKEN in .env file");
      return;
    }
    const qstashToken = tokenMatch[1];
    
    // Use a simple, well-known URL as the destination
    const destinationUrl = "https://httpbin.org/post";
    console.log(`Using simple destination URL: ${destinationUrl}`);
    
    // Create a simple message
    const message = "Hello World!";
    
    // Try the simplest possible QStash request
    console.log('\nSending basic request to QStash...');
    const response = await fetch('https://qstash.upstash.io/v2/publish', {
      method: 'POST',
      headers: {
        'Content-Type': 'text/plain',
        'Authorization': `Bearer ${qstashToken}`,
        'Upstash-Forward-Url': destinationUrl
      },
      body: message
    });
    
    console.log(`Response status: ${response.status} ${response.statusText}`);
    const responseBody = await response.text();
    console.log(`Response body: ${responseBody}`);
    
    if (response.status === 200 || response.status === 201) {
      console.log("\n✅ Success! QStash accepted the request.");
      console.log("This confirms that your QStash account is working correctly.");
      console.log("The issue may be specific to the ngrok URL or how it's being processed.");
      console.log("\nTroubleshooting steps:");
      console.log("1. Check if your ngrok tunnel is still active and accepting requests");
      console.log("2. Try a different ngrok tunnel or a public URL");
      console.log("3. Ensure the API endpoint at /api/qstash-test is accessible from the internet");
    } else {
      console.log("\n❌ QStash still rejected the request even with a simple URL.");
      console.log("This might indicate an issue with your QStash account configuration.");
    }
  } catch (error) {
    console.error('Error:', error);
  }
}

main(); 